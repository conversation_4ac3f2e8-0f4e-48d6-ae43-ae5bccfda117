package com.inossem.wms.bizbasis.sap.restful.service;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpCostCenterDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpProductionReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpProductionReceiptItemDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.erp.util.SapInterfaceOtherUtil;
import com.inossem.wms.bizbasis.erp.util.SapInterfaceUtil;
import com.inossem.wms.bizbasis.erp.util.SapInterfaceWriteOffUtil;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFacotryWbsDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialGroupDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialTypeDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicUnitDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.sap.SapConst;
import com.inossem.wms.common.enums.EnumDbDefaultValueString;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.enums.inconformity.EnumDifferentType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplySearchReqPlanPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingDocItemDTO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.erp.dto.SapWbsDTO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.erp.po.ReserveReceiptCreatePO;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.mat.base.entity.DicMaterialGroup;
import com.inossem.wms.common.model.masterdata.mat.base.entity.DicMaterialType;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFacotryWbs;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFactory;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCode;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilErp;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.sap.SapApiCallProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * Sap接口调用 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-03-10
 */
@Service
@Slf4j
public class SapInterfaceService {

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected EditCacheService editCacheService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected DicMaterialDataWrap dicMaterialDataWrap;

    @Autowired
    protected DicMaterialFactoryDataWrap dicMaterialFactoryDataWrap;

    @Autowired
    protected DicUnitDataWrap dicUnitDataWrap;

    @Autowired
    protected DicMaterialGroupDataWrap dicMaterialGroupDataWrap;

    @Autowired
    protected DicMaterialTypeDataWrap dicMaterialTypeDataWrap;

    @Autowired
    protected ErpCostCenterDataWrap erpCostCenterDataWrap;

    @Autowired
    protected ErpPurchaseReceiptHeadDataWrap erpPurchaseReceiptHeadDataWrap;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected ErpProductionReceiptHeadDataWrap erpProductionReceiptHeadDataWrap;

    @Autowired
    protected ErpProductionReceiptItemDataWrap erpProductionReceiptItemDataWrap;



    //@Autowired
    // 预留领料单抬头表-新建erp_mat_req_receipt_head

    //@Autowired
    // 预留领料单行项目表-新建erp_mat_req_receipt_item

    //@Autowired
    // 预留供应商信息表-新建dic_supplier

    //@Autowired
    // 获取物料库存信息-暂定erp_stock_diff（未创建datawrap文件），若文档字段与表结构出入过大，建议新建erp_stock_bin

    //@Autowired
    // 获取批次信息-暂定erp_stock_batch（未创建datawrap文件），若文档字段与表结构出入过大，建议原有表中追加字段，不要修改原有字段（报表功能使用）

    @Autowired
    @Lazy
    protected ErpPostingService erpPostingService;

    @Autowired
    protected SapApiCallProxy sapApiCallProxy;
    @Autowired
    private BizBatchInfoDataWrap bizBatchInfoDataWrap;

    /**
     * 时间格式化模板
     */
    private static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyyMMdd");
    @Autowired
    private DataFillService dataFillService;

    public static void main(String[] args) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport("82059061", Const.STRING_EMPTY, SapConst.TYPE_XXXXX);
        JSONArray paramsOfProduct = new JSONArray();
        JSONObject productInfo = new JSONObject();
        productInfo.put("SIGN", "I");
        productInfo.put("OPTION", "EQ");
        productInfo.put("LOW", "");
        paramsOfProduct.add(productInfo);
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_AUFNR", paramsOfProduct);
        System.out.print(params.toJSONString());
    }

    /**
     * 查询当天入库的所有物料 更新价格
     * @param currentTime
     */
    public void synWbsPrice(String ftyCode,List<String> matCodeList,Date currentTime){
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        // 定义入参
        JSONObject params = new JSONObject();
        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfImport = UtilErp.getImport(userCode, Const.STRING_EMPTY, Const.STRING_EMPTY);
        // ********  I_MATNR 物料编码 ********
        JSONArray paramsOfMatnr = new JSONArray();
        // ********  I_AEDAT 物料创建日期 ********
        JSONArray paramsOfAedat = new JSONArray();
        // ********  I_LAEDA 物料修改日期 ********
        JSONArray paramsOfLaeda = new JSONArray();
        // ********  I_WERKS 工厂 ********
        JSONArray paramsOfWerks = new JSONArray();

        // 期初导入 通过工厂code获取物料
        if(UtilString.isNotNullOrEmpty(ftyCode)) {
            JSONObject paramsWerks = new JSONObject();
            paramsWerks.put("SIGN", "I");
            paramsWerks.put("OPTION", "EQ");
            paramsWerks.put("LOW", ftyCode);
            paramsOfWerks.add(paramsWerks);
        }
        // 定时任务 通过当天时间获取创建/修改的物料
        if (UtilObject.isNotNull(currentTime)) {
            JSONObject paramsLaeda = new JSONObject();
            paramsLaeda.put("SIGN", "I");
            paramsLaeda.put("OPTION", "BT");
            paramsLaeda.put("LOW", FORMAT.format(currentTime));
            paramsLaeda.put("HIGH", FORMAT.format(currentTime));
            paramsOfLaeda.add(paramsLaeda);
        }

        if(currentTime!=null){
            // 查询入库单
            Set<String> matCodeSet = dicMaterialFacotryWbsDataWrap.getBaseMapper().getInputMatCodeSetFromInput(currentTime);
            if(matCodeList==null){
                matCodeList = new ArrayList<>(matCodeSet);
            }else{
                matCodeList.addAll(new ArrayList<>(matCodeSet));
            }
        }

        // 同步按钮 通过物料编码获取物料
        if(UtilCollection.isNotEmpty(matCodeList)) {
            matCodeList.forEach(matCode -> {
                JSONObject paramsMatnr = new JSONObject();
                paramsMatnr.put("SIGN", "I");
                paramsMatnr.put("OPTION", "EQ");
                paramsMatnr.put("LOW", UtilCode.matEncode(matCode));
                paramsOfMatnr.add(paramsMatnr);
            });
        }



        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_MATNR", paramsOfMatnr);
        params.put("I_AEDAT", paramsOfAedat);
        params.put("I_LAEDA", paramsOfLaeda);
        params.put("I_WERKS", paramsOfWerks);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.SYN_MATERIAL);

        log.debug("SAP同步物料入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP同步物料出参：" + returnObject);

        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfReturn = returnObject.getJSONObject("I_RETURN");
        // ********  I_MARA 物料明细 ********
        JSONArray paramsOfMara = returnObject.getJSONArray("T_MARA");
        // ********  T_MBEW 物料评估价格 ********
        JSONArray paramsOfMbew = returnObject.getJSONArray("T_MBEW");
        // ********  T_QBEW 项目库存评估价格 ********
        JSONArray paramsOfQbew = returnObject.getJSONArray("T_QBEW");

        // 保存/更新物料及物料工厂
        if(Const.ERP_RETURN_TYPE_S.equals(paramsOfReturn.getString("CODE"))) {
            this.saveOrUpdateMatWbs(paramsOfQbew);
        }
    }


    /**
     * 同步物料信息
     * 调用来源包括：
     * <li> 采购订单同步阶段发现新增物料</li>
     * <li> 期初同步全部工厂下物料</li>
     * <li> 物料主数据功能，基于物料编码的手动同步</li>
     * @deprecated 2024-07-30 将synMaterialInfo与synMaterialInfoNew方法合并，此方法不再使用
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void synMaterialInfo(String ftyCode, List<String> matCodeList, Date currentTime) {

        // ####################################################
        // ## 注意！！ 此方法已废弃 改用synMaterialInfoNew代替 ##
        // ####################################################

        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        // 定义入参
        JSONObject params = new JSONObject();
        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfImport = UtilErp.getImport(userCode, Const.STRING_EMPTY, Const.STRING_EMPTY);
        // ********  I_MATNR 物料编码 ********
        JSONArray paramsOfMatnr = new JSONArray();
        // ********  I_AEDAT 物料创建日期 ********
        JSONArray paramsOfAedat = new JSONArray();
        // ********  I_LAEDA 物料修改日期 ********
        JSONArray paramsOfLaeda = new JSONArray();
        // ********  I_WERKS 工厂 ********
        JSONArray paramsOfWerks = new JSONArray();

        // 期初导入 通过工厂code获取物料
        if(UtilString.isNotNullOrEmpty(ftyCode)) {
            JSONObject paramsWerks = new JSONObject();
            paramsWerks.put("SIGN", "I");
            paramsWerks.put("OPTION", "EQ");
            paramsWerks.put("LOW", ftyCode);
            paramsOfWerks.add(paramsWerks);
        }
        // 定时任务 通过当天时间获取创建/修改的物料
        if (UtilObject.isNotNull(currentTime)) {
            JSONObject paramsLaeda = new JSONObject();
            paramsLaeda.put("SIGN", "I");
            paramsLaeda.put("OPTION", "BT");
            paramsLaeda.put("LOW", FORMAT.format(currentTime));
            paramsLaeda.put("HIGH", FORMAT.format(currentTime));
            paramsOfLaeda.add(paramsLaeda);
        }
        // 同步按钮 通过物料编码获取物料
        if(UtilCollection.isNotEmpty(matCodeList)) {
            matCodeList.forEach(matCode -> {
                JSONObject paramsMatnr = new JSONObject();
                paramsMatnr.put("SIGN", "I");
                paramsMatnr.put("OPTION", "EQ");
                paramsMatnr.put("LOW", UtilCode.matEncode(matCode));
                paramsOfMatnr.add(paramsMatnr);
            });
        }

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_MATNR", paramsOfMatnr);
        params.put("I_AEDAT", paramsOfAedat);
        params.put("I_LAEDA", paramsOfLaeda);
        params.put("I_WERKS", paramsOfWerks);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.SYN_MATERIAL);

        log.debug("SAP同步物料入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP同步物料出参：" + returnObject);

        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfReturn = returnObject.getJSONObject("I_RETURN");
        // ********  I_MARA 物料明细 ********
        JSONArray paramsOfMara = returnObject.getJSONArray("T_MARA");
        // ********  T_MBEW 物料评估价格 ********
        JSONArray paramsOfMbew = returnObject.getJSONArray("T_MBEW");
        // ********  T_QBEW 项目库存评估价格 ********
        JSONArray paramsOfQbew = returnObject.getJSONArray("T_QBEW");

        // 保存/更新物料及物料工厂
        if(Const.ERP_RETURN_TYPE_S.equals(paramsOfReturn.getString("CODE"))) {

            List<DicStockLocationDTO> locationDTOList = new ArrayList<>(dictionaryService.getAllLocationCache()) ;
            Map<String,String> eprioMap = new HashMap<>();

            if(UtilCollection.isNotEmpty(locationDTOList)){
                eprioMap = locationDTOList.stream().collect(Collectors.toMap(e->e.getLocationCode(), e->e.getEprio(),(k1,k2)->k2));
            }

            // 物料
            List<DicMaterialDTO> materialDTOList = new ArrayList<>();
            // 物料工厂
            List<DicMaterialFactoryDTO> materialFactoryDTOList = new ArrayList<>();
            // 计量单位
            Set<DicUnit> unitSet = new HashSet<>();
            // 物料组
            Set<DicMaterialGroup> materialGroupSet = new HashSet<>();
            // 物料类型
            Set<DicMaterialType> materialTypeSet = new HashSet<>();
            // 物料明细
            Map<String, Integer> pkgTypeMap = new HashMap<>();
            for(int i = 0; i < paramsOfMara.size(); i++) {
                JSONObject jsonObject = paramsOfMara.getJSONObject(i);
                String factoryCode = jsonObject.getString("WERKS");
                Long ftyId= dictionaryService.getFtyIdCacheByCode(factoryCode);
                if(ftyId==null){
                    continue;
                }
                // 物料编码去重
                if(!materialDTOList.stream().map(p -> p.getMatCode()).collect(Collectors.toSet())
                        .contains(paramsOfMara.getJSONObject(i).getString("MATNR"))) {
                    // 物料
                    DicMaterialDTO materialDTO = new DicMaterialDTO();
                    materialDTO.setMatCode(paramsOfMara.getJSONObject(i).getString("MATNR")); // 物料编码
                    materialDTO.setMatName(paramsOfMara.getJSONObject(i).getString("MAKTX")); // 物料描述
                    materialDTO.setUnitCode(paramsOfMara.getJSONObject(i).getString("MEINS")); // 计量单位编码
                    materialDTO.setMatGroupCode(paramsOfMara.getJSONObject(i).getString("MATKL")); // 物料组编码
                    materialDTO.setMatTypeCode(paramsOfMara.getJSONObject(i).getString("MTART")); // 物料类型编码
                    materialDTO.setEprio(paramsOfMara.getJSONObject(i).getString("EPRIO")); // 存储级别



                    if(!UtilString.hasText(materialDTO.getEprio())){
                        materialDTO.setEprio(null);
                    }
                    if(UtilString.hasText(materialDTO.getEprio())){
                        String eprio = eprioMap.get(materialDTO.getEprio());
                        if(UtilString.hasText(eprio)){
                            materialDTO.setEprio(eprio);
                        }
                    }
                    materialDTO.setLength(BigDecimal.ZERO); // 长度
                    materialDTO.setWidth(BigDecimal.ZERO); // 宽度
                    materialDTO.setHeight(BigDecimal.ZERO); // 高度
                    materialDTO.setUnitLength(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_LENGTH.getValue()); // 长度/宽度/高度的单位
                    materialDTO.setGrossWeight(BigDecimal.ZERO); // 毛重
                    materialDTO.setNetWeight(BigDecimal.ZERO); // 净重
                    materialDTO.setUnitWeight(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_WEIGHT.getValue()); // 重量的单位
                    materialDTO.setWeightTolerance(BigDecimal.ZERO); // 重量容差
                    materialDTO.setVolume(BigDecimal.ZERO); // 体积
                    materialDTO.setUnitVolume(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_VOLUME.getValue()); // 体积的单位
                    materialDTO.setShelfLife(0); // 保质期
                    materialDTO.setUnitShelfLife(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_SHELF_LIFE.getValue()); // 保质期的单位
                    materialDTO.setIsShelfLife(EnumRealYn.FALSE.getIntValue()); // 是否启用保质期
                    materialDTO.setIsFreeze(EnumRealYn.FALSE.getIntValue()); // 是否冻结
                    materialDTO.setIsDangerous(EnumRealYn.FALSE.getIntValue()); // 是否危险物料
                    materialDTO.setShelfLifeMax(paramsOfMara.getJSONObject(i).getInteger("MHDHB")); // 总货架寿命
                    materialDTO.setShelfLifeMin(paramsOfMara.getJSONObject(i).getInteger("MHDRZ")); // 最小货架寿命
                    //materialDTO.setPackageType(paramsOfMara.getJSONObject(i).getInteger("ZBZFS")); // 包装方式
                    //materialDTO.setDepositType(paramsOfMara.getJSONObject(i).getInteger("ZCFFS")); // 存放方式
                    materialDTOList.add(materialDTO);
                }

                String pkgTypeStr = jsonObject.getString("ZBZFS");
                String depositTypeStr = jsonObject.getString("ZCFFS");
                // 只记录JO46的包装类型
                int pkgType = 0;
                int depositType = 0;
                if (StringUtils.isNotBlank(pkgTypeStr)) {
                    Pattern pattern = Pattern.compile("^[0-9]*$");
                    Matcher matcher = pattern.matcher(pkgTypeStr);
                    if (matcher.find()) {
                        try {
                            pkgType = Integer.parseInt(pkgTypeStr);
                        } catch (Exception e) {
                            log.error("cannot parse int: ", e);
                        }
                    }
                }
                if ("J046".equalsIgnoreCase(factoryCode)) {
                    // 修正工厂物料中除J046外的其他工厂保存不上包装类型的问题。此处保持原有逻辑，物料主数据中的包装类型与J046工厂一致
                    String maraCode = jsonObject.getString("MATNR");
                    pkgTypeMap.put(maraCode, pkgType);
                }
                if (StringUtils.isNotBlank(depositTypeStr)) {
                    Pattern pattern = Pattern.compile("^[0-9]*$");
                    Matcher matcher = pattern.matcher(depositTypeStr);
                    if (matcher.find()) {
                        try {
                            depositType = Integer.parseInt(depositTypeStr);
                        } catch (Exception e) {
                            log.error("cannot parse int: ", e);
                        }
                    } else {
                        if ("直立存放".equals(depositTypeStr)) {
                            depositType = 1;
                        }
                        if ("悬挂".equals(depositTypeStr)) {
                            depositType = 2;
                        }
                        if ("朝上存放".equals(depositTypeStr)) {
                            depositType = 3;
                        }
                        if ("非关闭状态".equals(depositTypeStr)) {
                            depositType = 4;
                        }
                        if ("倒置存放".equals(depositTypeStr)) {
                            depositType = 5;
                        }
                    }
                }
                // 物料工厂
                DicMaterialFactoryDTO materialFactoryDTO = new DicMaterialFactoryDTO();
                materialFactoryDTO.setMatCode(paramsOfMara.getJSONObject(i).getString("MATNR")); // 物料编码
                materialFactoryDTO.setFtyId(ftyId); // 工厂id
                materialFactoryDTO.setInspectClassifyId(0L); // 验收分类id
                materialFactoryDTO.setMaterialClassifyId(0L); // 物料分类id
                materialFactoryDTO.setTagType(EnumTagType.METAL_UNRESISTANT.getValue()); // 标签类型
                materialFactoryDTO.setIsSingle(EnumRealYn.FALSE.getIntValue()); // 单品/批次
                materialFactoryDTO.setShelfLife(0); // 保质期
                materialFactoryDTO.setRemindDay(0); // 临期预警天数
                materialFactoryDTO.setSecurityQty(BigDecimal.ZERO); // 安全库存数量
                materialFactoryDTO.setOrderPointQty(BigDecimal.ZERO); // 订货点数量
                materialFactoryDTO.setIsBatchErpEnabled(EnumRealYn.FALSE.getIntValue()); // 是否启用ERP批次
                materialFactoryDTO.setIsBatchProductEnabled(EnumRealYn.FALSE.getIntValue()); // 是否启用生产批次
                materialFactoryDTO.setIsPackageEnabled(EnumRealYn.FALSE.getIntValue()); // 是否启用包装物
                materialFactoryDTO.setShelfLifeMax(paramsOfMara.getJSONObject(i).getInteger("MHDHB")); // 总货架寿命
                materialFactoryDTO.setShelfLifeMin(paramsOfMara.getJSONObject(i).getInteger("MHDRZ")); // 最小货架寿命
                materialFactoryDTO.setPackageType(pkgType); // 包装方式
                materialFactoryDTO.setDepositType(depositType); // 存放方式
                String stockGroup = jsonObject.getString("EPRIO");
                materialFactoryDTO.setStockGroup(stockGroup);
                if (StringUtils.isNotBlank(stockGroup)) {
                    Long locationId = dictionaryService.getLocationIdCacheByCode(factoryCode, stockGroup);
                    if (locationId != null) {
                        materialFactoryDTO.setStockGroupId(locationId);
                    }
                }
                if(materialFactoryDTO.getFtyId()!=null){
                    materialFactoryDTOList.add(materialFactoryDTO);
                }
                // 计量单位
                if(UtilString.isNotNullOrEmpty(paramsOfMara.getJSONObject(i).getString("MEINS")) &&
                        UtilNumber.isEmpty(dictionaryService.getUnitIdCacheByCode(paramsOfMara.getJSONObject(i).getString("MEINS")))) {
                    DicUnit unit = new DicUnit();
                    unit.setUnitCode(paramsOfMara.getJSONObject(i).getString("MEINS")); // 计量单位编码
                    unit.setUnitName(paramsOfMara.getJSONObject(i).getString("MSEHL")); // 计量单位描述
                    unitSet.add(unit);
                }
                // 物料组
                if(UtilString.isNotNullOrEmpty(paramsOfMara.getJSONObject(i).getString("MATKL")) &&
                        UtilNumber.isEmpty(dictionaryService.getMatGroupIdByMatGroupCode(paramsOfMara.getJSONObject(i).getString("MATKL")))) {
                    DicMaterialGroup materialGroup = new DicMaterialGroup();
                    materialGroup.setMatGroupCode(paramsOfMara.getJSONObject(i).getString("MATKL")); // 物料组编码
                    materialGroup.setMatGroupName(paramsOfMara.getJSONObject(i).getString("WGBEZ")); // 物料组描述
                    materialGroupSet.add(materialGroup);
                }
                // 物料类型
                if(UtilString.isNotNullOrEmpty(paramsOfMara.getJSONObject(i).getString("MTART")) &&
                        UtilNumber.isEmpty(dictionaryService.getMatTypeIdByMatTypeCode(paramsOfMara.getJSONObject(i).getString("MTART")))) {
                    DicMaterialType materialType = new DicMaterialType();
                    materialType.setMatTypeCode(paramsOfMara.getJSONObject(i).getString("MTART")); // 物料类型编码
                    materialType.setMatTypeName(paramsOfMara.getJSONObject(i).getString("MTBEZ")); // 物料类型描述
                    materialTypeSet.add(materialType);
                }
            }
            // 物料评估价格
            for(int i = 0; i < paramsOfMbew.size(); i++) {
                for(DicMaterialFactoryDTO materialFactoryDTO : materialFactoryDTOList) {
                    if(paramsOfMbew.getJSONObject(i).getString("MATNR").equals(materialFactoryDTO.getMatCode())) {
                        materialFactoryDTO.setMoveAvgPrice(UtilObject.getBigDecimalOrZero(paramsOfMbew.getJSONObject(i).getBigDecimal("VERPR"))); // 移动平均价
                        materialFactoryDTO.setPriceUnit(paramsOfMbew.getJSONObject(i).getString("PEINH")); // 价格单位
                        materialFactoryDTO.setPriceMethod(UtilNumber.isNotEmpty(paramsOfMbew.getJSONObject(i).getBigDecimal("VERPR")) ? "V" : "S"); // 计价方式
                        materialFactoryDTO.setStandardPrice(paramsOfMbew.getJSONObject(i).getBigDecimal("STPRS")); // 标准价格
                    }
                }
            }
            if(UtilCollection.isNotEmpty(unitSet)) {
                // 保存计量单位
                dicUnitDataWrap.saveBatch(unitSet);
                // 刷新缓存
                editCacheService.refreshUnitCache();
            }
            if(UtilCollection.isNotEmpty(materialGroupSet)) {
                // 保存物料组
                dicMaterialGroupDataWrap.saveBatch(materialGroupSet);
                // 刷新缓存
                editCacheService.refreshDicMaterialGroupCache();
            }
            if(UtilCollection.isNotEmpty(materialTypeSet)) {
                // 保存物料类型
                dicMaterialTypeDataWrap.saveBatch(materialTypeSet);
                // 刷新缓存
                editCacheService.refreshDicMaterialTypeCache();
            }
            if(UtilCollection.isNotEmpty(materialDTOList)) {
                // 回填 物料-记录单位id 物料组id 物料类型id
                materialDTOList.forEach(p -> {
                    p.setUnitId(UtilNumber.isNotEmpty(dictionaryService.getUnitIdCacheByCode(p.getUnitCode())) ?
                            dictionaryService.getUnitIdCacheByCode(p.getUnitCode()) : 0L); // 计量单位id
                    p.setMatGroupId(UtilNumber.isNotEmpty(dictionaryService.getMatGroupIdByMatGroupCode(p.getMatGroupCode())) ?
                            dictionaryService.getMatGroupIdByMatGroupCode(p.getMatGroupCode()) : 0L); // 物料组id
                    p.setMatTypeId(UtilNumber.isNotEmpty(dictionaryService.getMatTypeIdByMatTypeCode(p.getMatTypeCode())) ?
                            dictionaryService.getMatTypeIdByMatTypeCode(p.getMatTypeCode()) : 0L); // 物料类型id
                    Integer pkgType = pkgTypeMap.get(p.getMatCode());
                    if (pkgType != null) {
                        p.setPackageType(pkgType);
                    }
                });
                // 根据物料编码查询已有物料
                QueryWrapper<DicMaterial> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().in(DicMaterial::getMatCode, materialDTOList.stream().map(p -> p.getMatCode()).collect(Collectors.toList()));
                List<DicMaterial> materialList = dicMaterialDataWrap.list(queryWrapper);
                materialDTOList.forEach(p -> {
                    materialList.forEach(q -> {
                        if(p.getMatCode().equals(q.getMatCode())) {
                            p.setId(q.getId());
                        }
                     });
                });
                // 保存物料
                dicMaterialDataWrap.saveOrUpdateBatchDto(materialDTOList);
                // 刷新缓存
                editCacheService.refreshMatCacheByMatIdList(materialDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()));
            }
            if(UtilCollection.isNotEmpty(materialFactoryDTOList)) {
                // 回填 物料工厂-物料id
                materialFactoryDTOList.forEach(p -> {
                    p.setMatId(dictionaryService.getMatIdByMatCode(p.getMatCode()));
                });
                // 根据工厂+物料查询已有工厂物料
                QueryWrapper<DicMaterialFactory> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().in(DicMaterialFactory::getFtyId, materialFactoryDTOList.stream().map(p -> p.getFtyId()).collect(Collectors.toList()))
                        .in(DicMaterialFactory::getMatId, materialFactoryDTOList.stream().map(p -> p.getMatId()).collect(Collectors.toList()));
                List<DicMaterialFactory> materialFactoryList = dicMaterialFactoryDataWrap.list(queryWrapper);
                materialFactoryDTOList.forEach(p -> {
                    materialFactoryList.forEach(q -> {
                        if(p.getFtyId().equals(q.getFtyId()) && p.getMatId().equals(q.getMatId())) {
                            p.setId(q.getId());
                        }
                    });
                });
                // 保存物料工厂
                dicMaterialFactoryDataWrap.saveOrUpdateBatchDto(materialFactoryDTOList);
                dataFillService.fillAttr(materialFactoryDTOList);
                // 刷新缓存
                dictionaryService.refreshDicMaterialFactoryByUniqueKey(materialFactoryDTOList);

                // 刷新价格
                this.saveOrUpdateMatWbs(paramsOfQbew);
                // 更新过期日期
                updateBatchDate(materialFactoryDTOList);
            }
        }else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, UtilObject.getStringOrEmpty(paramsOfReturn.getString("MES")));
        }
    }

    @Autowired
    private DicMaterialFacotryWbsDataWrap dicMaterialFacotryWbsDataWrap;

    /***
     * 更新wbs信息 T_QBEW
     */
    public void saveOrUpdateMatWbs(JSONArray paramsOfQbew){

        if(paramsOfQbew !=null && !paramsOfQbew.isEmpty()){
            List<DicMaterialFacotryWbs> mWbsList = new ArrayList<>();
            for(int i=0 ;i<paramsOfQbew.size();i++){
                JSONObject qbew = paramsOfQbew.getJSONObject(i);
                String matCode = qbew.getString("MATNR");
                String ftyCode = qbew.getString("BWKEY");
                String specStock = qbew.getString("SOBKZ");
                String wbs = qbew.getString("POSID");
                BigDecimal moveAvgPrice = UtilObject.getBigDecimalOrZero(qbew.getString("VERPR"));
                BigDecimal totalPrice = UtilObject.getBigDecimalOrZero(qbew.getString("SALK3"));
                BigDecimal totalQty = UtilObject.getBigDecimalOrZero(qbew.getString("LBKUM"));
                Long matId = dictionaryService.getMatIdByMatCode(matCode);
                Long ftyId = dictionaryService.getFtyIdCacheByCode(ftyCode);
                DicMaterialFacotryWbs mwbs = new DicMaterialFacotryWbs();
                mwbs.setMatId(matId);
                mwbs.setFtyId(ftyId);
                mwbs.setSpecStock(specStock);
                mwbs.setSpecStockCode(wbs);
                mwbs.setMoveAvgPrice(moveAvgPrice);
                mwbs.setTotalPrice(totalPrice);
                mwbs.setTotalQty(totalQty);
                mWbsList.add(mwbs);
            }
            if(UtilCollection.isNotEmpty(mWbsList)){
                // 反写 id 用于更新

                QueryWrapper<DicMaterialFacotryWbs> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().in(DicMaterialFacotryWbs::getMatId, mWbsList.stream().map(e->e.getMatId()).collect(Collectors.toList()));
                List<DicMaterialFacotryWbs> findList = dicMaterialFacotryWbsDataWrap.list(queryWrapper);
                if(UtilCollection.isNotEmpty(findList)){
                    Map<String,DicMaterialFacotryWbs> wbsMap = findList.stream().collect(Collectors.toMap(e->e.getMatId()+"-"+e.getFtyId()+"-"+e.getSpecStock()+"-"+e.getSpecStockCode(),e->e,(k1,k2)->k2));
                for(DicMaterialFacotryWbs wbs:mWbsList){
                    String key = wbs.getMatId()+"-"+wbs.getFtyId()+"-"+wbs.getSpecStock()+"-"+wbs.getSpecStockCode();
                    DicMaterialFacotryWbs find = wbsMap.get(key);
                    if(find!=null){
                        wbs.setId(find.getId());
                    }
                }

                }
                if (UtilCollection.isNotEmpty(mWbsList)) {
                    dicMaterialFacotryWbsDataWrap.saveOrUpdateBatchOptimize(mWbsList,200);
                    // 更新缓存数据
                    editCacheService.refreshDicMaterialFacotryWbsCache(mWbsList);
                }
            }
        }
    }

    /**
     * 3.2获取WBS、成本中心信息
     *
     * @param user       用户信息
     * @param costObj    成本对象
     * @param commonName 一般姓名
     * @param fac        工厂
     * @param ivType     1.WBS 2.成本中心3.资产卡片4.内部订单5.网络6.设备
     */
    public void synCostCenterInfo(CurrentUser user, String costObj, String commonName, String fac, String ivType) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_XXXXX);
        params.put("I_IMPORT", paramsOfImport);
        // UUID
        params.put("IV_GUID", "");
        params.put("IV_CBBH", costObj);
        params.put("IV_KTEXT", commonName);
        params.put("IV_WERKS", fac);
        params.put("IV_TYPE", "");
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.SYN_MATERIAL);
        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);

    }


    /**
     * 调用ERP获取领料单信息
     *
     * @param po   采购领料单查询条件
     * @param user 当前用户
     */
    public JSONObject getReceiveReceiptInfo (BizReceiptApplySearchReqPlanPO po, CurrentUser user) {
        String receiptCode = po.getReceiptCode();
        String erpCreateUserCode = po.getErpCreateUserCode();
        String remark = po.getRemark();
        String matCode = po.getMatCode();
        String workOrder = po.getWorkOrder();
        if (StringUtils.isBlank(receiptCode) && StringUtils.isBlank(erpCreateUserCode) && StringUtils.isBlank(remark)&& StringUtils.isBlank(matCode)&& StringUtils.isBlank(workOrder)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        JSONObject params = new JSONObject();
        // ********  I_Import参数 接口通用输入参数 ********
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_ONE);
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IV_ERNAM", erpCreateUserCode); //领料单创建人
        params.put("IV_ZLLDX", remark);//领料单描述
        params.put("IV_ZLLDH", receiptCode); //领料单号
        params.put("IV_MATNR", matCode);//物料编码
        params.put("IV_AUFNR", workOrder);//工单
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.ERP_RECEIVE_RECEIPT_POST);
        log.debug("调用ERP获取领料单接口信息:{}", params.toJSONString());
        return sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 调用ERP获取采购订单信息
     *
     * @param po   采购订单查询条件
     * @param user 当前用户
     */
    public JSONObject getPurchaseReceiptInfo (BizReceiptPreSearchPO po, CurrentUser user) {
        String receiptCode = po.getPurchaseReceiptCode();
        String contractCode = po.getContractCode();
        String contractName = po.getContractName();
        if (StringUtils.isBlank(receiptCode) && StringUtils.isBlank(contractCode) && StringUtils.isBlank(contractName)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        JSONObject params = new JSONObject();
        // ********  I_Import参数 接口通用输入参数 ********
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_ONE);

        // ********  I_EBELN 采购订单号参数  ********
        JSONArray paramsOfPurchase = new JSONArray();
        JSONObject purchaseInfo = new JSONObject();
        purchaseInfo.put("SIGN", "I");
        purchaseInfo.put("OPTION", "EQ");
        purchaseInfo.put("LOW", po.getPurchaseReceiptCode());
        purchaseInfo.put("HIGH",  Const.STRING_EMPTY);
        paramsOfPurchase.add(purchaseInfo);

        // ********  I_AEDAT 参数 采购订单创建日期参数 ********
        JSONArray paramsOfAedat = new JSONArray();
        JSONObject aedatInfo = new JSONObject();
        aedatInfo.put("SIGN",  Const.STRING_EMPTY);
        aedatInfo.put("OPTION",  Const.STRING_EMPTY);
        aedatInfo.put("LOW",  Const.STRING_EMPTY);
        aedatInfo.put("HIGH",  Const.STRING_EMPTY);
        paramsOfAedat.add(aedatInfo);

        /* =========== I_OTNUM 合同编号  =========== */
        JSONArray paramsOfI_OTNUM = new JSONArray();
        JSONObject obj_I_OTNUM = new JSONObject();
        obj_I_OTNUM.put("SIGN","I");
        obj_I_OTNUM.put("OPTION","EQ");
        obj_I_OTNUM.put("LOW",po.getContractCode());
        obj_I_OTNUM.put("HIGH",Const.STRING_EMPTY);
        paramsOfI_OTNUM.add(obj_I_OTNUM);

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_EBELN", paramsOfPurchase);
        params.put("I_AEDAT", paramsOfAedat);
        params.put("I_OTNUM", paramsOfI_OTNUM);
        params.put("IV_HTNAME",po.getContractName());
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.ERP_PURCHASE_RECEIPT_POST);

        log.debug("调用ERP获取采购订单信息:{}", params.toJSONString());
        return sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 3.4获取生产订单信息
     *
     * @param user
     * @param productCode 订单号
     */
    public void synProductionReceiptInfo(CurrentUser user, String productCode) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_XXXXX);
        JSONArray paramsOfProduct = new JSONArray();
        JSONObject productInfo = new JSONObject();
        productInfo.put("SIGN", "I");
        productInfo.put("OPTION", "EQ");
        productInfo.put("LOW", productCode);
        paramsOfProduct.add(productInfo);
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_AUFNR", paramsOfProduct);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.SYN_MATERIAL);
        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 3.5获取领料单信息
     *
     * @param user
     * @param matReqSupplierCode 供应商编号
     */
    public void synMatReqReceiptInfo(CurrentUser user, String matReqSupplierCode) {

    }

    /**
     * 3.6获取供应商信息
     *
     * @param user
     * @param supplierCode 供应商编码
     */
    public void synSupplierInfo(CurrentUser user, String supplierCode) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_XXXXXX);
        JSONArray paramsOfLifnr = new JSONArray();
        JSONObject lifnrInfo = new JSONObject();
        lifnrInfo.put("SIGN", "I");
        lifnrInfo.put("OPTION", "EQ");
        lifnrInfo.put("LOW", supplierCode);
        paramsOfLifnr.add(lifnrInfo);
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_KUNNR", "");
        params.put("I_ERDAT", "");
        params.put("I_VTWEG", "");
        params.put("I_SPART", "");
        params.put("I_LIFNR", paramsOfLifnr);
        params.put("I_BUKRS", "");
        params.put("I_EKORG", "");
        params.put("IV_NAME1", "");
        params.put("IV_TYPE", "");
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.SYN_MATERIAL);
        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);

//        JSONObject params = new JSONObject();
//        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_XXXXXXX);
//        JSONArray paramsOfLifnr = new JSONArray();
//        JSONObject lifnrInfo = new JSONObject();
//        lifnrInfo.put("SIGN","I");
//        lifnrInfo.put("OPTION","EQ");
//        lifnrInfo.put("LOW",supplierCode);
//        paramsOfLifnr.add(lifnrInfo);
//
//        params.put("IS_IMPORT",paramsOfImport);
//        params.put("I_KUNNR","");
//        params.put("I_ERDAT","");
//        params.put("I_VTWEG","");
//        params.put("I_SPART","");
//        params.put("I_LIFNR",paramsOfLifnr);
//        params.put("I_BUKRS","");
//        params.put("I_EKORG","");
//        params.put("IV_NAME1","");
//        params.put("IV_TYPE","");
//        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_05);
//        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 3.7获取物料库存信息
     *
     * @param user
     * @param matCode      物料编码
     * @param stockBInDate 日期
     */
    public void synStockBinInfo(CurrentUser user, String matCode, String stockBInDate) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_XXXXXXXX);
        JSONArray paramsOfMatnr = new JSONArray();
        JSONObject matnrInfo = new JSONObject();
        matnrInfo.put("SIGN", "I");
        matnrInfo.put("OPTION", "EQ");
        matnrInfo.put("LOW", stockBInDate);
        paramsOfMatnr.add(matnrInfo);
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IV_GUID", "");
        params.put("IV_WERKS", "");
        params.put("IV_LGORT", "");
        params.put("I_MATNR", paramsOfMatnr);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_05);
        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);
    }


    /**
     * 3.8获取批次信息
     *
     * @param user
     * @param matCode 物料编码
     */
    public void synStockBatchInfo(CurrentUser user, String matCode) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_STOCK);
        JSONArray paramsOfMatnr = new JSONArray();
        JSONArray paramsOfWerks = new JSONArray();
        JSONArray paramsOfErsda = new JSONArray();
        JSONObject matnrInfo = new JSONObject();
        JSONObject werksInfo = new JSONObject();
        JSONObject ersdaInfo = new JSONObject();
        matnrInfo.put("SIGN", "I");
        matnrInfo.put("OPTION", "EQ");
        matnrInfo.put("LOW", matCode);
        paramsOfMatnr.add(matnrInfo);

        werksInfo.put("SIGN", "I");
        werksInfo.put("OPTION", "EQ");
        werksInfo.put("LOW", "工厂");
        paramsOfWerks.add(werksInfo);

        ersdaInfo.put("SIGN", "I");
        ersdaInfo.put("OPTION", "EQ");
        ersdaInfo.put("LOW", "日期");
        paramsOfErsda.add(ersdaInfo);

        params.put("IS_IMPORT", paramsOfImport);
        params.put("IV_GUID", "");
        params.put("I_MATNR", paramsOfMatnr);
        params.put("I_WERKS", paramsOfWerks);
        params.put("I_ERSDA", paramsOfErsda);

        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_06);
        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 3.10 取消物料凭证信息
     *
     * @param user
     */
    public void synCancelStockBinInfo(CurrentUser user) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_CANCEL_STOCK_BIN);
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IV_GUID", "");
        params.put("IV_MBLNR", "");
        params.put("IV_MJAHR", "");
        params.put("IV_DATE", "");
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_07);
        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 创建物料凭证
     */
    public void synCreateStockBinInfo(String postingItem,ErpReturnObject erpReturnObj){

        JSONObject params = SapInterfaceOtherUtil.getSynCreateStockBinInfoParams(postingItem);//领料出库
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_CREATE_STOCK_BIN_INFO);
        log.debug("调用ERP创建物料凭证信息:{}", params.toJSONString());

        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();

        JSONObject returnObj = sapApiCallProxy.callSapApi(erpUrl, params);
        JSONArray mseg = returnObj.getJSONArray("T_MSEG");
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        /* ======================= SAP参数返回处理 ======================= */
        log.info("过账SAP返回参数:{}", JSON.toJSONString(returnObj));
        jsonObjectList.forEach(item -> {
            JSONArray jsonArray = item.getJSONArray("binDTOList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObj.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            }
        });
        erpReturnObj.setSuccess(returnObj.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObj.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setMatDocCode(returnObj.getString("MBLNR"));
        erpReturnObj.setMatDocYear(Integer.valueOf(returnObj.getString("MJAHR")));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 创建物料凭证 退转库
     */
    public void synCreateStockBinInfoTransferReturn(String postingItem,ErpReturnObject erpReturnObj){
//        JSONArray jsonArray = new JSONArray(Collections.singletonList(postingItem));
        List<BizReceiptOutputItemDTO> itemDTOList = JSONObject.parseArray(postingItem,BizReceiptOutputItemDTO.class);
        log.info("需要创建物料凭证的信息-实体:{}",itemDTOList);
        log.info("需要创建物料凭证的信息-JSON:{}",postingItem);
        CurrentUser user = bizCommonService.getUser();
        JSONObject params = new JSONObject();
        Integer callReceiptType= itemDTOList.get(0).getReceiptType();
        String  callReceiptCode=itemDTOList.get(0).getReceiptCode();
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(),itemDTOList.get(0).getReceiptCode(), SapConst.TYPE_CREATE_STOCK_BIN_INFO);
        log.info("创建物料凭证的信息:{}",postingItem);

        /* ================ IS_HEADER ================ */
        JSONObject obj_IS_HEADER = new JSONObject();
        // 凭证中的过账日期
//        String posingDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getPostingDate());
        String posingDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getPostingDate()).replace("-", "");
        obj_IS_HEADER.put("PSTNG_DATE",posingDate);
        // 凭证中的凭证日期
//        String docDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getDocDate());
        String docDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getDocDate()).replace("-", "");
        obj_IS_HEADER.put("DOC_DATE",docDate);
        // 参考凭证号
        obj_IS_HEADER.put("REF_DOC_NO",itemDTOList.get(0).getMatDocCode());
        // 用户名
        obj_IS_HEADER.put("PR_UNAME",user.getUserCode());

        /**
         * 佳园0721非要去掉
         凭证抬头文本
         obj_IS_HEADER.put("HEADER_TXT","部分退货");
         */
        /* ================ T_ITEM ================ */
        JSONArray paramsArrayT_ITEM = new JSONArray();
        itemDTOList.forEach(bizReceiptOutputItemDTO -> {
            List<BizReceiptOutputBinDTO> binDTOList = bizReceiptOutputItemDTO.getBinDTOList();
            binDTOList.forEach(bizReceiptOutputBinDTO -> {
                String batchCode = bizReceiptOutputBinDTO.getBatchInfo().getBatchCode();
                JSONObject obj_T_ITEM = new JSONObject();
                // 物料编号
                obj_T_ITEM.put("MATERIAL",bizReceiptOutputItemDTO.getMatCode());
                // 工厂
                obj_T_ITEM.put("PLANT",bizReceiptOutputItemDTO.getFtyCode());
                // 库存地点
                obj_T_ITEM.put("STGE_LOC",bizReceiptOutputItemDTO.getLocationCode());
                // 批号
                obj_T_ITEM.put("BATCH",batchCode);
                // 特殊库存标识
                obj_T_ITEM.put("SPEC_STOCK",bizReceiptOutputItemDTO.getSpecStock());
                // 供应商帐户号
                obj_T_ITEM.put("VENDOR","");
                // 以输入单位计的数量
                obj_T_ITEM.put("ENTRY_QNT",bizReceiptOutputBinDTO.getQty());
                if(bizReceiptOutputItemDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())) {
                    obj_T_ITEM.put("ENTRY_QNT",bizReceiptOutputBinDTO.getAmount());

                }
                // 条目单位
                obj_T_ITEM.put("ENTRY_UOM",bizReceiptOutputItemDTO.getUnitCode());
                // 项目文本
                obj_T_ITEM.put("ITEM_TEXT",bizReceiptOutputItemDTO.getItemRemark());
                // 预留/相关需求的编号
                obj_T_ITEM.put("RESERV_NO",bizReceiptOutputItemDTO.getReservedOrderCode());
                // 预留/相关需求的项目编号
                obj_T_ITEM.put("RES_ITEM",bizReceiptOutputItemDTO.getReservedOrderRid());
                // 该预留的最后发货
                obj_T_ITEM.put("WITHDRAWN",Const.STRING_EMPTY);
                // 工作分解结构元素 (WBS 元素)
                obj_T_ITEM.put("WBS_ELEM",bizReceiptOutputItemDTO.getSpecStockCode());
                // 生产日期
                obj_T_ITEM.put("PROD_DATE",Const.STRING_EMPTY);

                obj_T_ITEM.put("MOVE_TYPE",Const.TRANSFER_RETURN_MOVE_TYPE);
                // 工作分解结构元素 (WBS 元素)
                obj_T_ITEM.put("VAL_WBS_ELEM",bizReceiptOutputItemDTO.getSpecStockCode());
                obj_T_ITEM.put("ZDJBH",bizReceiptOutputItemDTO.getReceiptCode());
                obj_T_ITEM.put("ZDJXM",bizReceiptOutputItemDTO.getRid());
                obj_T_ITEM.put("ZHXH",bizReceiptOutputBinDTO.getBid());
                paramsArrayT_ITEM.add(obj_T_ITEM);
            });


        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER",obj_IS_HEADER);
        params.put("T_ITEM",paramsArrayT_ITEM);

        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_CREATE_STOCK_BIN_INFO);
        log.debug("调用ERP创建物料凭证信息:{}", params.toJSONString());

        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();

        JSONObject returnObj = sapApiCallProxy.callSapApi(erpUrl, params);
        JSONArray mseg = returnObj.getJSONArray("T_MSEG");
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        /* ======================= SAP参数返回处理 ======================= */
        log.info("过账SAP返回参数:{}", JSON.toJSONString(returnObj));
        jsonObjectList.forEach(item -> {
            JSONArray jsonArray = item.getJSONArray("binDTOList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObj.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            }
        });
        erpReturnObj.setSuccess(returnObj.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObj.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setMatDocCode(returnObj.getString("MBLNR"));
        erpReturnObj.setMatDocYear(Integer.valueOf(returnObj.getString("MJAHR")));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 获取出库单的金额
     * @param t_mseg
     * @param rid
     * @param bid
     * @return
     */
    private BigDecimal getDmbtr(JSONArray t_mseg,  String rid, String bid) {
        for (Object o : t_mseg) {
            JSONObject jsonObject = (JSONObject) o;
            if(
                    rid.equals(jsonObject.getString("ZDJXM")) &&
                    bid.equals(jsonObject.getString("ZHXH"))
            ){
                return jsonObject.getBigDecimal("DMBTR");
            }

        }
        return null;
    }

    /**
     * 3.11 盘点差异过账
     *
     * @param user
     */
    public void synCountingDifferencePosting(CurrentUser user) {
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_DIFF_POST);
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IV_GUID", "");
        params.put("IV_ZIVNUM", "");
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_08);
        JSONObject sapReturn = sapApiCallProxy.callSapApi(erpUrl, params);

    }

    /**
     * 获取预留信息
     *
     * @param user 用户信息
     */
    public JSONObject synReservedInfo(CurrentUser user) {
        /* ====== 参数组装 ====== */
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_DIFF_POST);

        JSONObject werksObj = new JSONObject();
        JSONArray werksArray = new JSONArray();
        werksObj.put("SIGN", "");
        werksObj.put("OPTION", "");
        werksObj.put("LOW", "");
        werksObj.put("HIGH", "");
        werksArray.add(werksObj);

        JSONArray matnrArray = new JSONArray();
        JSONObject matnrObj = new JSONObject();
        matnrObj.put("SIGN", "");
        matnrObj.put("OPTION", "");
        matnrObj.put("LOW", "");
        matnrObj.put("HIGH", "");
        matnrArray.add(matnrObj);

        JSONArray ersdaArray = new JSONArray();
        JSONObject ersdaObj = new JSONObject();
        ersdaObj.put("SIGN", "");
        ersdaObj.put("OPTION", "");
        ersdaObj.put("LOW", "");
        ersdaObj.put("HIGH", "");
        ersdaArray.add(ersdaObj);

        JSONArray rsnumArray = new JSONArray();
        JSONObject rsnumObj = new JSONObject();
        rsnumObj.put("SIGN", "");
        rsnumObj.put("OPTION", "");
        rsnumObj.put("LOW", "");
        rsnumObj.put("HIGH", "");
        rsnumArray.add(rsnumObj);

        JSONArray retpoArray = new JSONArray();
        JSONObject retpoObj = new JSONObject();
        retpoObj.put("SIGN", "");
        retpoObj.put("OPTION", "");
        retpoObj.put("LOW", "");
        retpoObj.put("HIGH", "");
        retpoArray.add(retpoObj);

        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_WERKS", werksArray);
        params.put("I_MATNR", matnrArray);
        params.put("I_ERSDA", ersdaArray);
        params.put("I_RSNUM", rsnumArray);
        params.put("I_RETPO", retpoArray);
        params.put("IV_USNAM", user.getUserCode());
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.ERP_PURCHASE_RECEIPT_POST);
        log.debug("调用ERP获取采购订单信息:{}", params.toJSONString());
        return sapApiCallProxy.callSapApi(erpUrl, params);

    }

    /**
     * SAP创建预留单
     *
     * @param po
     * @param user
     * @return
     */
    public JSONObject createReserveReceiptSynBySap(ReserveReceiptCreatePO po, CurrentUser user) {
        JSONObject params = SapInterfaceOtherUtil.getCreateReserveReceiptParams(po,user);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_CREATE_RESERVE);
        log.debug("调用ERP创建预留单信息:{}", params.toJSONString());
        return sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 过账通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject posting(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.postingBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.postingByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }
    /**
     * 过账通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject postingNew(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.postingBySapNew(postingItem, erpReturnObj);
        } else {
            erpPostingService.postingByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 过账
     * 物料转码 移动类型为 Y81/Y82（Q）
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject postingTransportMatY81AndY82New(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.postingBySapTransportMatY81AndY82New(postingItem, erpReturnObj);
        } else {
            erpPostingService.postingByNotSapTransportMatY81AndY82New(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 调用sap过账
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    public void postingBySapNew(String postingItem, ErpReturnObject erpReturnObj) {

        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_POST);
        JSONObject params = SapInterfaceUtil.getRequestParams(postingItem);
        log.debug("SAP过账入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP过账出参：" + returnObject);
        SapInterfaceUtil.delRespondObject(postingItem,erpReturnObj,returnObject);
    }


    /**
     * 调用sap过账
     * 物料转码 移动类型为 Y81/Y82（Q）
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    public void postingBySapTransportMatY81AndY82New(String postingItem, ErpReturnObject erpReturnObj) {
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_TRANSPORT_MAT_DIFF_UNIT_POST);
        JSONObject params = SapInterfaceUtil.getStockTransportMatY81AndY82Params(postingItem);
        log.debug("SAP过账入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP过账出参：" + returnObject);
        SapInterfaceUtil.delStockTransportMatY81AndY82Object(postingItem,erpReturnObj,returnObject);
    }

    /**
     * 调用sap过账
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    public void postingBySap(String postingItem, ErpReturnObject erpReturnObj) {
        // json转list
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        // 定义入参
        JSONObject params = new JSONObject();

        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;

        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            Integer callReceiptType= jsonObjectList.get(0).getInteger("receiptType");
            String  callReceiptCode=jsonObjectList.get(0).getString("receiptCode");
            params.put("callerReceiptType", callReceiptType);
            params.put("callReceiptCode", callReceiptCode);
        }

        // 领料退库
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            Integer receiptType = jsonObjectList.get(0).getInteger("receiptType");
            if (receiptType.equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                    || receiptType.equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue()) || receiptType.equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                type = SapConst.TYPE_TWO;
            }
        }
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        //入库冲销 104 - 冲销
        if (EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(jsonObjectList.get(0).getInteger("receiptType"))
                || EnumReceiptType.STOCK_MATERIAL_RETURN_INPUT.getValue().equals(jsonObjectList.get(0).getInteger("receiptType"))) {
            if (UtilString.isNotNullOrEmpty(jsonObjectList.get(0).getString("writeOffMatDocCode"))) {
                paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("deliveryWriteOffPostingDate")); // 冲销-过账日期
                paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("deliveryWriteOffDocDate")); // 冲销-凭证日期
            }
            else {
                paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("writeOffPostingDate")); // 冲销-过账日期
                paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("writeOffDocDate")); // 冲销-凭证日期
            }
        }
        // 不符合项处置/到货登记 104 - 冲销
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            if (jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())
                    || (jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.ARRIVAL_REGISTER.getValue())
                    && UtilString.isNotNullOrEmpty(jsonObjectList.get(0).getString("matDocCode")))) {
                paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("writeOffPostingDate")); // 冲销-过账日期
                paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("writeOffDocDate")); // 冲销-凭证日期
            }
        }
        // 成套设备-不符合项处置
        // 数量差异
        else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("numberInconformityItem"))
                && jsonObjectList.get(0).getJSONObject("numberInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                && jsonObjectList.get(0).getJSONObject("numberInconformityItem").getInteger("differentType").equals(EnumDifferentType.NUMBER_DIFF.getValue())) {
                paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("nWriteOffPostingDate")); // 冲销-过账日期
                paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("nWriteOffDocDate")); // 冲销-凭证日期
        }
        // 质量差异
        else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem"))
                && jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                && EnumDifferentType.isQualityDiff(jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem").getInteger("differentType"))) {
                paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("qWriteOffPostingDate")); // 冲销-过账日期
                paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("qWriteOffDocDate")); // 冲销-凭证日期
        }
        // 成套设备-到货登记 104 - 冲销
        else if (UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("arrivalRegisterItem"))
                && jsonObjectList.get(0).getJSONObject("arrivalRegisterItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())
                && UtilString.isNotNullOrEmpty(jsonObjectList.get(0).getJSONObject("arrivalRegisterItem").getString("matDocCode"))) {
            paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("aWriteOffPostingDate")); // 冲销-过账日期
            paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("aWriteOffDocDate")); // 冲销-凭证日期
        }
        // 成套设备-验收入库
        else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("inspectInputItem"))
                && jsonObjectList.get(0).getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())) {
            paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("iPostingDate")); // 过账日期
            paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("iDocDate")); // 凭证日期
        }
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer receiptTypeObj = jsonObjectList.get(0).getInteger("receiptType");
        // 到货登记/验收入库/不符合项处置/成套设备-到货登记 item表赋值
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.ARRIVAL_REGISTER.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_MATERIAL_RETURN_INPUT.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())) {
                jsonObjectList.forEach(item -> {
                    JSONObject sapItem = new JSONObject();
                    sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                    sapItem.put("ZHXH", item.getString("rid")); // instock配货号
                    if (jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue())) {
                        sapItem.put("ZDJBH", item.getString("preReceiptCode")); // instock单据号
                        sapItem.put("ZDJXM", item.getString("preReceiptRid")); // istock行号
                        sapItem.put("ZHXH", item.getString("preReceiptRid")); // instock配货号
                    }
                    sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
                    sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
                    sapItem.put("BATCH", item.getString("batchCode")); // 批次号
                    sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
                    // 到货登记/成套设备-到货登记 103
                    if (item.getInteger("receiptType").equals(EnumReceiptType.ARRIVAL_REGISTER.getValue())
                            || item.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())) {
                        // 104 - 冲销
                        if ((UtilString.isNotNullOrEmpty(item.getString("matDocCode")) &&
                                !item.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue()))) {
                            sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                            sapItem.put("ENTRY_QNT", item.getString("writeOffQty")); // 数量
                            sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                            sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                            sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                        } else {
                            sapItem.put("MOVE_TYPE", "103"); // 移动类型
                        }
                    }
                    // // 104 - 冲销
                    if ((item.getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue()) ||
                            item.getInteger("receiptType").equals(EnumReceiptType.STOCK_MATERIAL_RETURN_INPUT.getValue()))
                            && UtilString.isNotNullOrEmpty(item.getString("writeOffMatDocCode"))) {
                        sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                        sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear103")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode103")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid103")); // 参考凭证行项目号
                    }
                    // 验收入库 105
                    else if (item.getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())) {
                        sapItem.put("MOVE_TYPE", "105"); // 移动类型
                        sapItem.put("REF_DOC_YR", item.getIntValue("preMatDocYear")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("preMatDocCode")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("preMatDocRid")); // 参考凭证行项目号
                    }
                    // 验收入库 106
                    else if (item.getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue()) ||
                            item.getInteger("receiptType").equals(EnumReceiptType.STOCK_MATERIAL_RETURN_INPUT.getValue())) {
                        sapItem.put("MOVE_TYPE", "106"); // 移动类型
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear103")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode103")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid103")); // 参考凭证行项目号
                    }
                    // 退旧入库 Y91 - 冲销
                    else if (item.getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_OLD_INPUT.getValue())) {
                        sapItem.put("MOVE_TYPE", "Y91"); // 移动类型
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                    }
                    // 闲置入库 Y91 - 冲销
                    else if (item.getInteger("receiptType").equals(EnumReceiptType.LEISURE_INPUT.getValue())) {
                        sapItem.put("MOVE_TYPE", "Y91"); // 移动类型
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                    }
                    // 不符合项处置 104 - 冲销
                    else if (item.getInteger("receiptType").equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())) {
                        sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                    }
                    sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                    sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
                    sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                    sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                    sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                    sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                    sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                    sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                    sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                    sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                    sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                    sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                    sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                    sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                    sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                    sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                    sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                    sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                    sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                    sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                    sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                    sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                    sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
                    paramsOfItem.add(sapItem);
                });
            } else if (receiptTypeObj.equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                    || receiptTypeObj.equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                    || receiptTypeObj.equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())
                    || receiptTypeObj.equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                // 采购退货/领料退库/退转库入库/报废出库 bin表赋值
                jsonObjectList.forEach(item -> {
                    // json转list
                    JSONArray jsonArray = new JSONArray();
                    if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                        jsonArray = item.getJSONArray("binDTOList");
                    }else if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())) {
                        jsonArray = item.getJSONArray("itemInfoList");
                    }
                    for(int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        JSONObject sapItem = new JSONObject();
                        sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                        sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                        sapItem.put("ZHXH", jsonObject.getString("bid")); // instock配货号
                        sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
                        sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
                        sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
                        sapItem.put("BATCH", jsonObject.getJSONObject("batchInfo").getString("batchCode")); // 批次号
                        // 采购退货 161
                        if (item.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())) {
                            sapItem.put("MOVE_TYPE", "101"); // 移动类型
                            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                        }
                        // 报废出库
                        else if (item.getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                            if (jsonObject.getJSONObject("batchInfo").getString("specStock").equals("Q")) {
                                sapItem.put("MOVE_TYPE", "555Q"); // 移动类型
                            }else {
                                sapItem.put("MOVE_TYPE", "555"); // 移动类型
                            }
                        }
                        // 领料退库 X标识
                        else if (item.getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())) {
                            sapItem.put("MOVE_TYPE", Const.STRING_EMPTY); // 移动类型
                            sapItem.put("ZTKBS", "X"); // 退库标识 X
                            sapItem.put("XSTOB", Const.STRING_EMPTY); // 退库标识 X
                            sapItem.put("VENDOR", jsonObject.getJSONObject("batchInfo").getString("supplierCode")); // 供应商
                            sapItem.put("REF_DOC_YR", item.getIntValue("preMatDocYear")); // 参考凭证年度
                            sapItem.put("REF_DOC", item.getString("preMatDocCode")); // 参考凭证号
                            sapItem.put("REF_DOC_IT", jsonObject.getIntValue("preMatDocRid")); // 参考凭证行项目号
                        }
                        // 退转库入库 Y91
                        else if (item.getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())) {
                            sapItem.put("MOVE_TYPE", "Y91"); // 移动类型
                            sapItem.put("VENDOR", jsonObject.getJSONObject("batchInfo").getString("supplierCode")); // 供应商
                        }
                        sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                        sapItem.put("SPEC_STOCK", jsonObject.getJSONObject("batchInfo").getString("specStock")); // 特殊库存标识
                        sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                        sapItem.put("ENTRY_QNT", jsonObject.getString("qty")); // 数量
                        sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
                        sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
                        sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
                        sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                        sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                        sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                        sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                        sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                        sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                        sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                        sapItem.put("RESERV_NO",  item.getString("reserveReceiptCode")); // 预留/相关需求的编号
                        sapItem.put("RES_ITEM", item.getString("reserveReceiptRid")); // 预留/相关需求的项目编号
                        sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                        sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                        sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                        sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                        sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                        sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                        sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                        sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                        sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                        sapItem.put("WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                        sapItem.put("EXPIRYDATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 货架寿命到期日
                        sapItem.put("PROD_DATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 生产日期
                        sapItem.put("VAL_WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                        paramsOfItem.add(sapItem);
                    }
                });
            }
        }
        // 成套设备-到货登记 104 - 冲销/验收入库/不符合项处置 item表赋值
        else if((UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("arrivalRegisterItem"))
                && jsonObjectList.get(0).getJSONObject("arrivalRegisterItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())
                && UtilString.isNotNullOrEmpty(jsonObjectList.get(0).getJSONObject("arrivalRegisterItem").getString("matDocCode")))
                || (UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("inspectInputItem"))
                && jsonObjectList.get(0).getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue()))
                || (UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("numberInconformityItem"))
                && jsonObjectList.get(0).getJSONObject("numberInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                && jsonObjectList.get(0).getJSONObject("numberInconformityItem").getInteger("differentType").equals(EnumDifferentType.NUMBER_DIFF.getValue()))
                || (UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem"))
                && jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                && EnumDifferentType.isQualityDiff(jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem").getInteger("differentType")))) {
            jsonObjectList.forEach(waybill -> {
                JSONObject sapItem = new JSONObject();
                // 到货登记冲销 104
                if(UtilObject.isNotNull(waybill.getJSONObject("arrivalRegisterItem"))
                        && waybill.getJSONObject("arrivalRegisterItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())) {
                    sapItem.put("ZDJBH", waybill.getJSONObject("arrivalRegisterItem").getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", waybill.getString("arrivalRegisterItemRid")); // istock行号
                    sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", waybill.getJSONObject("arrivalRegisterItem").getString("matCode")); // 物料编码
                    sapItem.put("PLANT", waybill.getJSONObject("arrivalRegisterItem").getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", waybill.getJSONObject("arrivalRegisterItem").getString("locationCode")); // 库存地点
                    sapItem.put("BATCH", waybill.getJSONObject("bizBatchInfoDTO").getString("batchCode")); // 批次号
                    sapItem.put("ENTRY_QNT", waybill.getBigDecimal("aWriteOffQty").multiply(waybill.getBigDecimal("price"))
                            .add(waybill.getBigDecimal("remainder"))); // 数量
                    sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                    sapItem.put("REF_DOC_YR", waybill.getJSONObject("arrivalRegisterItem").getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", waybill.getJSONObject("arrivalRegisterItem").getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", waybill.getJSONObject("arrivalRegisterItem").getIntValue("matDocRid")); // 参考凭证行项目号
                    sapItem.put("SPEC_STOCK", waybill.getJSONObject("arrivalRegisterItem").getString("specStock")); // 特殊库存标识
                    sapItem.put("VENDOR", waybill.getJSONObject("arrivalRegisterItem").getString("supplierCode")); // 供应商
                    sapItem.put("ENTRY_UOM", waybill.getJSONObject("arrivalRegisterItem").getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", waybill.getJSONObject("arrivalRegisterItem").getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", waybill.getJSONObject("arrivalRegisterItem").getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("WBS_ELEM", waybill.getJSONObject("arrivalRegisterItem").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", waybill.getJSONObject("arrivalRegisterItem").getString("productDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", waybill.getJSONObject("arrivalRegisterItem").getString("productDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", waybill.getJSONObject("arrivalRegisterItem").getString("specStockCode")); // WBS
                }
                // 验收入库 105
                else if (UtilObject.isNotNull(waybill.getJSONObject("inspectInputItem"))
                        && waybill.getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())) {
                    sapItem.put("ZDJBH", waybill.getJSONObject("inspectInputItem").getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", waybill.getString("inspectInputItemRid")); // istock行号
                    sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", waybill.getJSONObject("inspectInputItem").getString("matCode")); // 物料编码
                    sapItem.put("PLANT", waybill.getJSONObject("inspectInputItem").getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", waybill.getJSONObject("inspectInputItem").getString("locationCode")); // 库存地点
                    sapItem.put("BATCH", waybill.getJSONObject("bizBatchInfoDTO").getString("batchCode")); // 批次号
                    sapItem.put("ENTRY_QNT", waybill.getBigDecimal("qualifiedQty").multiply(waybill.getBigDecimal("price"))
                            .add(waybill.getBigDecimal("remainder"))); // 数量
                    sapItem.put("MOVE_TYPE", "105"); // 移动类型
                    sapItem.put("REF_DOC_YR", waybill.getIntValue("preMatDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", waybill.getString("preMatDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", waybill.getIntValue("preMatDocRid")); // 参考凭证行项目号
                    sapItem.put("SPEC_STOCK", waybill.getJSONObject("inspectInputItem").getString("specStock")); // 特殊库存标识
                    sapItem.put("VENDOR", waybill.getJSONObject("inspectInputItem").getString("supplierCode")); // 供应商
                    sapItem.put("ENTRY_UOM", waybill.getJSONObject("inspectInputItem").getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", waybill.getJSONObject("inspectInputItem").getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", waybill.getJSONObject("inspectInputItem").getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("WBS_ELEM", waybill.getJSONObject("inspectInputItem").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", waybill.getJSONObject("inspectInputItem").getString("productDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", waybill.getJSONObject("inspectInputItem").getString("productDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", waybill.getJSONObject("inspectInputItem").getString("specStockCode")); // WBS
                }
                // 不符合项处置 104 - 冲销
                // 数量差异
                else if (UtilObject.isNotNull(waybill.getJSONObject("numberInconformityItem"))
                        && waybill.getJSONObject("numberInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                        && waybill.getJSONObject("numberInconformityItem").getInteger("differentType").equals(EnumDifferentType.NUMBER_DIFF.getValue())) {
                    sapItem.put("ZDJBH", waybill.getJSONObject("numberInconformityItem").getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", waybill.getString("numberInconformityMaintainItemRid")); // istock行号
                    sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", waybill.getJSONObject("numberInconformityItem").getString("matCode")); // 物料编码
                    sapItem.put("PLANT", waybill.getJSONObject("numberInconformityItem").getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", waybill.getJSONObject("numberInconformityItem").getString("locationCode")); // 库存地点
                    JSONObject batchInfoObj = waybill.getJSONObject("bizBatchInfoDTO");
                    if (batchInfoObj != null) {
                        sapItem.put("BATCH", batchInfoObj.getString("batchCode")); // 批次号
                    }
                    BigDecimal remainder = waybill.getBigDecimal("remainder");
                    Integer useSignRemainder = waybill.getInteger("useSignRemainder");
                    if (useSignRemainder != null && useSignRemainder.equals(EnumRealYn.TRUE.getIntValue())) {
                        remainder = BigDecimal.ZERO;
                    }
                    sapItem.put("ENTRY_QNT", waybill.getBigDecimal("nWriteOffQty").multiply(waybill.getBigDecimal("price")).add(remainder)); // 数量
                    sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                    sapItem.put("REF_DOC_YR", waybill.getJSONObject("numberInconformityItem").getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", waybill.getJSONObject("numberInconformityItem").getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", waybill.getJSONObject("numberInconformityItem").getIntValue("matDocRid")); // 参考凭证行项目号
                    sapItem.put("SPEC_STOCK", waybill.getJSONObject("numberInconformityItem").getString("specStock")); // 特殊库存标识
                    sapItem.put("VENDOR", waybill.getJSONObject("numberInconformityItem").getString("supplierCode")); // 供应商
                    sapItem.put("ENTRY_UOM", waybill.getJSONObject("numberInconformityItem").getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", waybill.getJSONObject("numberInconformityItem").getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", waybill.getJSONObject("numberInconformityItem").getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("WBS_ELEM", waybill.getJSONObject("numberInconformityItem").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", waybill.getJSONObject("numberInconformityItem").getString("productDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", waybill.getJSONObject("numberInconformityItem").getString("productDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", waybill.getJSONObject("numberInconformityItem").getString("specStockCode")); // WBS
                }
                // 质量差异
                else if (UtilObject.isNotNull(waybill.getJSONObject("qualifiedInconformityItem"))
                        && waybill.getJSONObject("qualifiedInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                        && EnumDifferentType.isQualityDiff(waybill.getJSONObject("qualifiedInconformityItem").getInteger("differentType"))) {
                    sapItem.put("ZDJBH", waybill.getJSONObject("qualifiedInconformityItem").getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", waybill.getString("qualityInconformityMaintainItemRid")); // istock行号
                    sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", waybill.getJSONObject("qualifiedInconformityItem").getString("matCode")); // 物料编码
                    sapItem.put("PLANT", waybill.getJSONObject("qualifiedInconformityItem").getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", waybill.getJSONObject("qualifiedInconformityItem").getString("locationCode")); // 库存地点
                    JSONObject batchInfoObj = waybill.getJSONObject("bizBatchInfoDTO");
                    if (batchInfoObj != null) {
                        sapItem.put("BATCH", batchInfoObj.getString("batchCode")); // 批次号
                    }
                    BigDecimal remainder = waybill.getBigDecimal("remainder");
                    Integer useSignRemainder = waybill.getInteger("useSignRemainder");
                    if (useSignRemainder != null && useSignRemainder.equals(EnumRealYn.TRUE.getIntValue())) {
                        remainder = BigDecimal.ZERO;
                    }
                    sapItem.put("ENTRY_QNT", waybill.getBigDecimal("qWriteOffQty").multiply(waybill.getBigDecimal("price")).add(remainder)); // 数量
                    sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                    sapItem.put("REF_DOC_YR", waybill.getJSONObject("qualifiedInconformityItem").getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", waybill.getJSONObject("qualifiedInconformityItem").getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", waybill.getJSONObject("qualifiedInconformityItem").getIntValue("matDocRid")); // 参考凭证行项目号
                    sapItem.put("SPEC_STOCK", waybill.getJSONObject("qualifiedInconformityItem").getString("specStock")); // 特殊库存标识
                    sapItem.put("VENDOR", waybill.getJSONObject("qualifiedInconformityItem").getString("supplierCode")); // 供应商
                    sapItem.put("ENTRY_UOM", waybill.getJSONObject("qualifiedInconformityItem").getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", waybill.getJSONObject("qualifiedInconformityItem").getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", waybill.getJSONObject("qualifiedInconformityItem").getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("WBS_ELEM", waybill.getJSONObject("qualifiedInconformityItem").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", waybill.getJSONObject("qualifiedInconformityItem").getString("productDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", waybill.getJSONObject("qualifiedInconformityItem").getString("productDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", waybill.getJSONObject("qualifiedInconformityItem").getString("specStockCode")); // WBS
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                paramsOfItem.add(sapItem);
            });
        }

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_POST);

        log.debug("SAP过账入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP过账出参：" + returnObject);

        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        // 到货登记/验收入库/不符合项处置/成套设备-到货登记 item表匹配
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.ARRIVAL_REGISTER.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_OLD_INPUT.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.LEISURE_INPUT.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())) {
                jsonObjectList.forEach(item -> {
                    for(int i = 0; i < mseg.size(); i++) {
                        JSONObject obj = mseg.getJSONObject(i);
                        if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                                && item.getString("rid").equals(obj.getString("ZDJXM"))
                                && item.getString("rid").equals(obj.getString("ZHXH"))) {
                            ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                            returnItem.setReceiptCode(obj.getString("ZDJBH"));
                            returnItem.setReceiptRid(obj.getString("ZDJXM"));
                            returnItem.setReceiptBid(obj.getString("ZHXH"));
                            returnItem.setMatDocCode(obj.getString("MBLNR"));
                            returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                            returnItem.setMatDocRid(obj.getString("ZEILE"));
                            returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                            returnItemList.add(returnItem);
                        }
                    }
                });
            }
        }
        //入库冲销
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            if (jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue()) ||
                    jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_MATERIAL_RETURN_INPUT.getValue())) {
                jsonObjectList.forEach(item -> {
                    for(int i = 0; i < mseg.size(); i++) {
                        JSONObject obj = mseg.getJSONObject(i);
                        if(item.getString("preReceiptCode").equals(obj.getString("ZDJBH"))
                                && item.getString("preReceiptRid").equals(obj.getString("ZDJXM"))
                                && item.getString("preReceiptRid").equals(obj.getString("ZHXH"))) {
                            ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                            returnItem.setReceiptCode(obj.getString("ZDJBH"));
                            returnItem.setReceiptRid(obj.getString("ZDJXM"));
                            returnItem.setReceiptBid(obj.getString("ZHXH"));
                            returnItem.setWriteOffMatDocCode(obj.getString("MBLNR"));
                            returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                            returnItem.setWriteOffMatDocRid(obj.getString("ZEILE"));
                            returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                            returnItemList.add(returnItem);
                        }
                    }
                });
            }
        }
        // 成套设备-到货登记 104 - 冲销 item表匹配
        if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("arrivalRegisterItem"))
                && jsonObjectList.get(0).getJSONObject("arrivalRegisterItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())
                && UtilString.isNotNullOrEmpty(jsonObjectList.get(0).getJSONObject("arrivalRegisterItem").getString("matDocCode"))) {
            jsonObjectList.forEach(waybill -> {
                for(int i = 0; i < mseg.size(); i++) {
                    JSONObject obj = mseg.getJSONObject(i);
                    if(waybill.getJSONObject("arrivalRegisterItem").getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && waybill.getString("arrivalRegisterItemRid").equals(obj.getString("ZDJXM"))
                            && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            });
        }
        // 成套设备-验收入库 item表匹配
        else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("inspectInputItem"))
                && jsonObjectList.get(0).getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())) {
            jsonObjectList.forEach(waybill -> {
                for(int i = 0; i < mseg.size(); i++) {
                    JSONObject obj = mseg.getJSONObject(i);
                    if(waybill.getJSONObject("inspectInputItem").getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && waybill.getString("inspectInputItemRid").equals(obj.getString("ZDJXM"))
                            && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            });
        }
        // 成套设备-不符合项处置 item表匹配
        // 数量差异
        else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("numberInconformityItem"))
                && jsonObjectList.get(0).getJSONObject("numberInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                && jsonObjectList.get(0).getJSONObject("numberInconformityItem").getInteger("differentType").equals(EnumDifferentType.NUMBER_DIFF.getValue())) {
            jsonObjectList.forEach(waybill -> {
                for(int i = 0; i < mseg.size(); i++) {
                    JSONObject obj = mseg.getJSONObject(i);
                    if(waybill.getJSONObject("numberInconformityItem").getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && waybill.getString("numberInconformityMaintainItemRid").equals(obj.getString("ZDJXM"))
                            && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            });
        }
        // 质量差异
        else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem"))
                && jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())
                && EnumDifferentType.isQualityDiff(jsonObjectList.get(0).getJSONObject("qualifiedInconformityItem").getInteger("differentType"))) {
            jsonObjectList.forEach(waybill -> {
                for(int i = 0; i < mseg.size(); i++) {
                    JSONObject obj = mseg.getJSONObject(i);
                    if(waybill.getJSONObject("qualifiedInconformityItem").getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && waybill.getString("qualityInconformityMaintainItemRid").equals(obj.getString("ZDJXM"))
                            && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            });
        }
        // 采购退货/领料退库/退转库入库 bin表匹配
        else if (UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            if (jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                jsonObjectList.forEach(item -> {
                    JSONArray jsonArray = new JSONArray();
                    if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                        jsonArray = item.getJSONArray("binDTOList");
                    }else if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())) {
                        jsonArray = item.getJSONArray("itemInfoList");
                    }
                    for(int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        for(int j = 0; j < mseg.size(); j++) {
                            JSONObject obj = mseg.getJSONObject(j);
                            if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                                    && item.getString("rid").equals(obj.getString("ZDJXM"))
                                    && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                                ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                                returnItem.setReceiptCode(obj.getString("ZDJBH"));
                                returnItem.setReceiptRid(obj.getString("ZDJXM"));
                                returnItem.setReceiptBid(obj.getString("ZHXH"));
                                returnItem.setMatDocCode(obj.getString("MBLNR"));
                                returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                                returnItem.setMatDocRid(obj.getString("ZEILE"));
                                returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                                returnItemList.add(returnItem);
                            }
                        }
                    }
                });
            }
        }
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 冲销通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject writeOff(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.writeOffBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.writeOffByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }
    /**
     * 冲销通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject writeOffNew(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.writeOffBySapNew(postingItem, erpReturnObj);
        } else {
            erpPostingService.writeOffByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }
    /**
     * 调用sap冲销
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    public void writeOffBySap(String postingItem, ErpReturnObject erpReturnObj) {
        JSONObject params = SapInterfaceWriteOffUtil.getRequestParams(postingItem);
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_WRITEOFF);
        log.debug("SAP冲销入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP冲销出参：" + returnObject);

        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        // 验收入库 item表赋值
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))){
            if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())) {
                jsonObjectList.forEach(item -> {
                    for(int i = 0; i < mseg.size(); i++) {
                        JSONObject obj = mseg.getJSONObject(i);
                        if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                                && item.getString("rid").equals(obj.getString("ZDJXM"))
                                && item.getString("rid").equals(obj.getString("ZHXH"))) {
                            ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                            returnItem.setReceiptCode(obj.getString("ZDJBH"));
                            returnItem.setReceiptRid(obj.getString("ZDJXM"));
                            returnItem.setReceiptBid(obj.getString("ZHXH"));
                            returnItem.setMatDocCode(obj.getString("MBLNR"));
                            returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                            returnItem.setMatDocRid(obj.getString("ZEILE"));
                            returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                            returnItemList.add(returnItem);
                        }
                    }
                });
            }
        }
        // 成套设备-验收入库
        else if(UtilObject.isNotNull(jsonObjectList.get(0).getJSONObject("inspectInputItem"))
                    && jsonObjectList.get(0).getJSONObject("inspectInputItem").getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())) {
            jsonObjectList.forEach(waybill -> {
                for(int i = 0; i < mseg.size(); i++) {
                    JSONObject obj = mseg.getJSONObject(i);
                    if(waybill.getJSONObject("inspectInputItem").getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && waybill.getString("inspectInputItemRid").equals(obj.getString("ZDJXM"))
                            && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            });
        }
        // 采购退货/领料退库/退转库入库/报废出库 bin表匹配
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))){
            if (jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                    || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                jsonObjectList.forEach(item -> {
                    JSONArray jsonArray = new JSONArray();
                    if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())) {
                        jsonArray = item.getJSONArray("binDTOList");
                    }else if(jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                            || jsonObjectList.get(0).getInteger("receiptType").equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())) {
                        jsonArray = item.getJSONArray("itemInfoList");
                    }
                    for(int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        for(int j = 0; j < mseg.size(); j++) {
                            JSONObject obj = mseg.getJSONObject(j);
                            if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                                    && item.getString("rid").equals(obj.getString("ZDJXM"))
                                    && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                                ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                                returnItem.setReceiptCode(obj.getString("ZDJBH"));
                                returnItem.setReceiptRid(obj.getString("ZDJXM"));
                                returnItem.setReceiptBid(obj.getString("ZHXH"));
                                returnItem.setMatDocCode(obj.getString("MBLNR"));
                                returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                                returnItem.setMatDocRid(obj.getString("ZEILE"));
                                returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                                returnItemList.add(returnItem);
                            }
                        }
                    }
                });
            }
        }
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    public void writeOffBySapNew(String postingItem, ErpReturnObject erpReturnObj) {
        JSONObject params = SapInterfaceWriteOffUtil.getRequestParams(postingItem);
        log.debug("SAP冲销入参：" + params);

        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_WRITEOFF);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);

        SapInterfaceWriteOffUtil.delRespondObject(postingItem,erpReturnObj);
        log.debug("SAP冲销出参：" + returnObject);
    };

    /**
     * 冲销通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject scrapWriteOff(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.scrapWriteOffBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.writeOffByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 调用sap冲销
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    public void scrapWriteOffBySap(String postingItem, ErpReturnObject erpReturnObj) {
        // json转list
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        // 定义入参
        JSONObject params = new JSONObject();

        // ********  I_IMPORT 接口通用输入参数 ********
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), SapConst.TYPE_CREATE_STOCK_BIN_INFO);

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("MBLPO", item.getIntValue("matDocRid")); // 物料凭证行项目
            sapItem.put("ZDJBH", item.getString("receiptCode")); // 单据号
            sapItem.put("ZDJXM", item.getString("rid")); // 单据行项目号
            paramsOfItem.add(sapItem);
        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IV_MBLNR", jsonObjectList.get(0).getString("matDocCode"));
        params.put("IV_MJAHR", jsonObjectList.get(0).getIntValue("matDocYear"));
        params.put("IV_DATE", jsonObjectList.get(0).getString("docDate"));
        params.put("IT_ITEM", paramsOfItem);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_WRITEOFF);

        log.debug("SAP冲销入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP冲销出参：" + params);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE  + e.getString("rid"));
            returnItemList.add(item);
        });
        if(Const.ERP_RETURN_TYPE_S.equals(returnObject.getJSONObject("I_RETURN").getString("CODE"))){
            erpReturnObj.setMatDocYear(Integer.valueOf(returnObject.getString("MJAHR")));
            erpReturnObj.setMatDocCode(returnObject.getString("MBLNR"));
        }
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 盘点差异过账通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject stocktakingPosting(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.stocktakingPostingBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.stocktakingPostingByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 调用sap盘点差异过账
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    private void stocktakingPostingBySap(String postingItem, ErpReturnObject erpReturnObj) {

    }

    /**
     * 过账通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject transPosting(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.transPostingBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.postingByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 调用sap转储转性过账
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    public void transPostingBySap(String postingItem, ErpReturnObject erpReturnObj) {

        // json转list
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        // 定义入参
        JSONObject params = new JSONObject();

        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType = "";
        switch (EnumReceiptType.getByValue(jsonObjectList.get(0).getInteger("receiptType"))){
            case STOCK_TRANSPORT: //转储
            case STOCK_TRANSFER: //转性
            case STOCK_FREEZE_SCRAP: //报废冻结
            case STOCK_FREEZE_EXPIRE: //过期冻结
                ivType =  SapConst.TYPE_TRANS;
                break;
            default:;
        }
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);
        if(UtilNumber.isNotEmpty(jsonObjectList.get(0).getInteger("receiptType"))) {
            Integer callReceiptType= jsonObjectList.get(0).getInteger("receiptType");
            String  callReceiptCode=jsonObjectList.get(0).getString("receiptCode");
            params.put("callerReceiptType", callReceiptType);
            params.put("callReceiptCode", callReceiptCode);
        }
        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject> itemList =  new ArrayList<>();
            switch (EnumReceiptType.getByValue(item.getInteger("receiptType"))){
                case STOCK_TRANSPORT: //转储
                    itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("assembleDTOList")), JSONObject.class);
                    break;
                case STOCK_TRANSFER: //转性
                case STOCK_FREEZE_SCRAP: //报废冻结
                case STOCK_FREEZE_EXPIRE: //过期冻结
                    itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
                    break;
                default:;
            }

            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();
                // 转储 511
                if (EnumReceiptType.STOCK_TRANSPORT.getValue().equals(item.getInteger("receiptType"))) {
                    sapItem.put("MATERIAL", assemble.getString("matCode")); // 物料编码
                    sapItem.put("PLANT", assemble.getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", assemble.getString("locationCode")); // 库存地点
                    sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                    sapItem.put("WBS_ELEM", assemble.getString("specStockCode")); // WBS
                    sapItem.put("VAL_WBS_ELEM", assemble.getString("specStockCode")); // WBS
                    sapItem.put("MOVE_MAT",assemble.getString("matCode")); // 收货/发货物料
                    sapItem.put("SPEC_STOCK", assemble.getString("specStock")); // 特殊库存标识
                    sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                    sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                    sapItem.put("ENTRY_UOM", assemble.getString("unitCode")); // 计量单位
                    sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                    if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                        sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                    } else {
                        sapItem.put("MOVE_TYPE", "311"); // 移动类型
                    }
                }
                // 转性 510
                if (EnumReceiptType.STOCK_TRANSFER.getValue().equals(item.getInteger("receiptType"))) {
                    sapItem.put("MATERIAL", item.getString("outputMatCode")); // 物料编码
                    sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                    sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                    sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                    sapItem.put("WBS_ELEM", item.getString("inputSpecStockCode")); // WBS
                    sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                    sapItem.put("MOVE_MAT",item.getString("inputMatCode")); // 收货/发货物料
                    sapItem.put("SPEC_STOCK", assemble.getString("outSpecStock")); // 特殊库存标识
                    sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                    sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                    sapItem.put("ENTRY_UOM", item.getString("outputUnitCode")); // 计量单位
                    sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                    if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                        sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                    } else {
                        sapItem.put("MOVE_TYPE", "415"); // 移动类型
                    }
                }
                // 报废冻结
                if (EnumReceiptType.STOCK_FREEZE_SCRAP.getValue().equals(item.getInteger("receiptType"))
                ||EnumReceiptType.STOCK_FREEZE_EXPIRE.getValue().equals(item.getInteger("receiptType"))) {
                    sapItem.put("MATERIAL", item.getString("outputMatCode")); // 物料编码
                    sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                    sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                    sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                    sapItem.put("WBS_ELEM", Const.STRING_EMPTY); // WBS
                    sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                    sapItem.put("MOVE_MAT",Const.STRING_EMPTY); // 收货/发货物料
                    sapItem.put("SPEC_STOCK", assemble.getString("outputSpecStock")); // 特殊库存标识
                    sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                    sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                    sapItem.put("MOVE_REAS", item.getString("scrapCause")); // 移动原因
                    if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                        sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                        sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                        sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                        sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                    } else {
                        sapItem.put("MOVE_TYPE", "344"); // 移动类型
                    }
                }

                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_POST);

        log.debug("SAP过账入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP过账出参：" + params);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE  + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }


    /**
     * 冲销通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject transWriteOff(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.transWriteOffBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.writeOffByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }


    /**
     * 调用sap冲销
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    public void transWriteOffBySap(String postingItem, ErpReturnObject erpReturnObj) {
        // json转list
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        // 定义入参
        JSONObject params = new JSONObject();

        // ********  I_IMPORT 接口通用输入参数 ********
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), SapConst.TYPE_ONE);

        // ********  I_ITEM 行项目参数  ********
//        JSONArray paramsOfItem = new JSONArray();
//        jsonObjectList.forEach(item -> {
//            JSONObject sapItem = new JSONObject();
//            sapItem.put("MBLPO", item.getIntValue("matDocRid")); // 物料凭证行项目
//            sapItem.put("ZDJBH", item.getString("receiptCode")); // 单据号
//            sapItem.put("ZDJXM", item.getString("rid")); // 单据行项目号
//            paramsOfItem.add(sapItem);
//        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IV_MBLNR", jsonObjectList.get(0).getString("matDocCode"));
        params.put("IV_MJAHR", jsonObjectList.get(0).getIntValue("matDocYear"));
        params.put("IV_DATE", jsonObjectList.get(0).getString("docDate"));
        //params.put("IT_ITEM", paramsOfItem);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_WRITEOFF);

        log.debug("SAP冲销入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP冲销出参：" + params);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE  + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * SAP关闭预留
     * @param closingItem 行项目JSON串
     * @return
     */
    public JSONObject synClosingReservation(String closingItem) {
        // JSON转实体List
        List<BizReceiptOutputItemDTO> itemDTOList = JSONObject.parseArray(closingItem,BizReceiptOutputItemDTO.class);
        // TODO-BO 2022/5/24 封装SAP关闭预留参数(等SAP预留接口)
        return null;
    }



    /**
     * 创建物料凭证
     */
    public void synScrapCreateStockBinInfo(String postingItem,ErpReturnObject erpReturnObj){
//        JSONArray jsonArray = new JSONArray(Collections.singletonList(postingItem));
        List<BizReceiptOutputItemDTO> itemDTOList = JSONObject.parseArray(postingItem,BizReceiptOutputItemDTO.class);
        log.info("需要创建物料凭证的信息-实体:{}",itemDTOList);
        log.info("需要创建物料凭证的信息-JSON:{}",postingItem);
        CurrentUser user = bizCommonService.getUser();
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(),itemDTOList.get(0).getReceiptCode(), SapConst.TYPE_CREATE_STOCK_BIN_INFO);
        log.info("创建物料凭证的信息:{}",postingItem);

        /* ================ IS_HEADER ================ */
        JSONObject obj_IS_HEADER = new JSONObject();
        // 凭证中的过账日期
//        String posingDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getPostingDate());
        String posingDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getPostingDate()).replace("-", "");
        obj_IS_HEADER.put("PSTNG_DATE",posingDate);
        // 凭证中的凭证日期
//        String docDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getDocDate());
        String docDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getDocDate()).replace("-", "");
        obj_IS_HEADER.put("DOC_DATE",docDate);
        // 参考凭证号
        obj_IS_HEADER.put("REF_DOC_NO",itemDTOList.get(0).getMatDocCode());
        // 用户名
        obj_IS_HEADER.put("PR_UNAME",user.getUserCode());
        // 凭证抬头文本
        obj_IS_HEADER.put("HEADER_TXT",itemDTOList.get(0).getHeadRemark());

        /* ================ T_ITEM ================ */
        JSONArray paramsArrayT_ITEM = new JSONArray();
        itemDTOList.forEach(itemDTO -> {
            List<BizReceiptAssembleDTO> assembleDTOList = itemDTO.getAssembleDTOList();
            assembleDTOList.forEach(assemble -> {
                String batchCode = assemble.getBatchCode();
                JSONObject obj_T_ITEM = new JSONObject();
                // 物料编号
                obj_T_ITEM.put("MATERIAL",itemDTO.getMatCode());

                // 工厂
                obj_T_ITEM.put("PLANT",itemDTO.getFtyCode());
                // 工厂
                obj_T_ITEM.put("MOVE_PLANT",itemDTO.getFtyCode());
                // 库存地点
                obj_T_ITEM.put("STGE_LOC",itemDTO.getLocationCode());
                obj_T_ITEM.put("MOVE_STLOC",itemDTO.getLocationCode());
                // 批号
                obj_T_ITEM.put("BATCH",batchCode);
                obj_T_ITEM.put("MOVE_BATCH",batchCode);
                // 供应商帐户号
                obj_T_ITEM.put("VENDOR","");
                // 以输入单位计的数量
                obj_T_ITEM.put("ENTRY_QNT",itemDTO.getQty());
                // 条目单位
                obj_T_ITEM.put("ENTRY_UOM",itemDTO.getUnitCode());
                // 项目文本
                obj_T_ITEM.put("ITEM_TEXT",itemDTO.getItemRemark());
                // 工作分解结构元素 (WBS 元素)
                obj_T_ITEM.put("WBS_ELEM",itemDTO.getSpecStockCode());
                // 生产日期
                obj_T_ITEM.put("PROD_DATE",Const.STRING_EMPTY);
                // 成本中心
                obj_T_ITEM.put("COSTCENTER",itemDTO.getCostCenterCode());
                // 工作分解结构元素 (WBS 元素)
                obj_T_ITEM.put("VAL_WBS_ELEM",itemDTO.getSpecStockCode());
                obj_T_ITEM.put("ITEM_TEXT",itemDTO.getItemRemark());
                // 工作分解结构元素 (WBS 元素)
                DicMoveType dicMoveType = dictionaryService.getMoveCacheById(itemDTO.getMoveTypeId());
                obj_T_ITEM.put("MOVE_TYPE",dicMoveType.getMoveTypeCode());
                // 特殊库存标识
                obj_T_ITEM.put("SPEC_STOCK",dicMoveType.getSpecStock());
                paramsArrayT_ITEM.add(obj_T_ITEM);
            });


        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER",obj_IS_HEADER);
        params.put("T_ITEM",paramsArrayT_ITEM);

        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_CREATE_STOCK_BIN_INFO);
        log.debug("调用ERP创建物料凭证信息:{}", params.toJSONString());

        JSONObject returnObj = sapApiCallProxy.callSapApi(erpUrl, params);
        /* ======================= SAP参数返回处理 ======================= */
        log.info("过账SAP返回参数:{}", JSON.toJSONString(returnObj));
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        if(Const.ERP_RETURN_TYPE_S.equals(returnObj.getJSONObject("I_RETURN").getString("CODE"))){
            erpReturnObj.setMatDocYear(Integer.valueOf(returnObj.getString("MJAHR")));
            erpReturnObj.setMatDocCode(returnObj.getString("MBLNR"));
        }
        erpReturnObj.setSuccess(returnObj.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObj.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 成套过账通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject unitizedPosting(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.postingBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.unitizedPostingByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 成套冲销通用方法
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject unitizedWriteOff(String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.writeOffBySap(postingItem, erpReturnObj);
        } else {
            erpPostingService.unitizedWriteOffByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 按日期查询WBS
     * @param startDate
     * @param endDate
     */
    public List<SapWbsDTO> syncWBS(Date startDate, Date endDate) {
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        // 定义入参
        JSONObject params = new JSONObject();
        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfImport = UtilErp.getImport(userCode, Const.STRING_EMPTY, Const.STRING_EMPTY);
        paramsOfImport.put("ZLCID", RandomUtil.randomNumbers(15));
        paramsOfImport.put("ZDJBH", "");
        paramsOfImport.put("IV_TYPE", SapConst.TYPE_ONE);
        paramsOfImport.put("ZNAME", userCode);
        // ********  I_ERDAT 日期 ********
        JSONArray paramsOfErdat = new JSONArray();
        JSONObject paramsErdat = new JSONObject();
        paramsErdat.put("SIGN", "I");
        paramsErdat.put("OPTION", "BT");
        paramsErdat.put("LOW", FORMAT.format(startDate));
        paramsErdat.put("HIGH", FORMAT.format(endDate));
        paramsOfErdat.add(paramsErdat);
        // ********  I_WERKS 工厂 ********
        JSONArray paramsOfWerks = new JSONArray();
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_ERDAT", paramsOfErdat);
        params.put("I_WERKS", paramsOfWerks);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_WBS);
        log.debug("SAP同步WBS入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP同步WBS出参：" + returnObject);
        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfReturn = returnObject.getJSONObject("I_RETURN");
        // ********  T_PRPS WBS主数据 ********
        JSONArray paramsOfPrps = returnObject.getJSONArray("T_PRPS");
        List<SapWbsDTO> dtoList = new ArrayList<>();
        if (Const.ERP_RETURN_TYPE_S.equals(paramsOfReturn.getString("CODE"))) {
            for (int i = 0; i < paramsOfPrps.size(); i++) {
                JSONObject jsonObject = paramsOfPrps.getJSONObject(i);
                String pspnr = jsonObject.getString("PSPNR");
                String posid = jsonObject.getString("POSID");
                String post1 = jsonObject.getString("POST1");
                String ernam = jsonObject.getString("ERNAM");
                String erdat = jsonObject.getString("ERDAT");
                String werks = jsonObject.getString("WERKS");
                String pbukr = jsonObject.getString("PBUKR");
                String loevm = jsonObject.getString("LOEVM");
                String isEndCls = jsonObject.getString("ISENDCLS");
                SapWbsDTO erpWbsDTO = new SapWbsDTO();
                erpWbsDTO.setWbsCodeIn(pspnr);
                erpWbsDTO.setWbsCodeOut(posid);
                erpWbsDTO.setWbsName(post1);
                erpWbsDTO.setWbsCopany(pbukr);
                erpWbsDTO.setWbsFactory(werks);
                erpWbsDTO.setWbsCreateDate(erdat);
                erpWbsDTO.setWbsCreator(ernam);
                erpWbsDTO.setWbsDeleteFlag(loevm);
                erpWbsDTO.setWbsLeafFlag(isEndCls);
                dtoList.add(erpWbsDTO);
            }
        }
        return dtoList;
    }

    /**
     * SAP创建预留单
     *
     * @param po
     * @param user
     * @return
     */
    public JSONObject createReserveReceiptSynBySapNew(ReserveReceiptCreatePO po, CurrentUser user) {
        JSONObject params = SapInterfaceOtherUtil.getCreateReserveReceiptParamsNew(po,user);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_CREATE_RESERVE);
        log.debug("调用ERP创建预留单信息:{}", params.toJSONString());
        return sapApiCallProxy.callSapApi(erpUrl, params);
    }

    /**
     * 物料信息同步 定时任务（根据工厂参数）同步指定工厂下物料数据&手工同步指定工厂下物料数据<br/>
     * 2024-07-29 二期项目有限重构，由于原有方法已趋于稳定，项目工期及资源限制仅适当添加注释和调整部分代码位置<br/>
     *
     * @param ftyCode 按照工厂参数同步指定工厂下的物料
     * @param matCodeList 物料编码列表
     * @param syncStartTime 同步的起始时间
     * @param syncEndTime 同步的截止时间
     * @see {@link #synMaterialInfo(String, List, Date)} 已弃用的物料方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void synMaterialInfoNew(String ftyCode, List<String> matCodeList, Date syncStartTime, Date syncEndTime) {

        // #############################
        // ######## 拼装接口参数 ########
        // #############################
        long startTime = System.currentTimeMillis();
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        // 定义入参
        JSONObject params = new JSONObject();
        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfImport = UtilErp.getImport(userCode, Const.STRING_EMPTY, Const.STRING_EMPTY);
        // ********  I_MATNR 物料编码 ********
        JSONArray paramsOfMatnr = new JSONArray();
        // ********  I_AEDAT 物料创建日期 ********
        JSONArray paramsOfAedat = new JSONArray();
        // ********  I_LAEDA 物料修改日期 ********
        JSONArray paramsOfLaeda = new JSONArray();
        // ********  I_WERKS 工厂 ********
        JSONArray paramsOfWerks = new JSONArray();

        // 期初导入 通过工厂code获取物料
        if(UtilString.isNotNullOrEmpty(ftyCode)) {
            JSONObject paramsWerks = new JSONObject();
            paramsWerks.put("SIGN", "I");
            paramsWerks.put("OPTION", "EQ");
            paramsWerks.put("LOW", ftyCode);
            paramsOfWerks.add(paramsWerks);
        }
        // 定时任务 通过当天时间获取创建/修改的物料
        if (UtilObject.isNotNull(syncStartTime)) {
            JSONObject paramsLaeda = new JSONObject();
            paramsLaeda.put("SIGN", "I");
            paramsLaeda.put("OPTION", "BT");
            paramsLaeda.put("LOW", FORMAT.format(syncStartTime));
            if (UtilObject.isNotNull(syncEndTime)) {
                paramsLaeda.put("HIGH", FORMAT.format(syncEndTime));
            }
            paramsOfLaeda.add(paramsLaeda);
        }
        // 同步按钮 通过物料编码获取物料
        if(UtilCollection.isNotEmpty(matCodeList)) {
            matCodeList.forEach(matCode -> {
                JSONObject paramsMatnr = new JSONObject();
                paramsMatnr.put("SIGN", "I");
                paramsMatnr.put("OPTION", "EQ");
                paramsMatnr.put("LOW", UtilCode.matEncode(matCode));
                paramsOfMatnr.add(paramsMatnr);
            });
        }

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_MATNR", paramsOfMatnr);
        params.put("I_AEDAT", paramsOfAedat);
        params.put("I_LAEDA", paramsOfLaeda);
        params.put("I_WERKS", paramsOfWerks);


        // #############################
        // ######## 发起接口调用 ########
        // #############################
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.SYN_MATERIAL);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);


        // #################################
        // ######## 处理接口返回结果 ########
        // #################################
        // ********  I_IMPORT 接口通用输入参数 ********
        JSONObject paramsOfReturn = returnObject.getJSONObject("I_RETURN");
        // ********  I_MARA 物料明细 ********
        JSONArray paramsOfMara = returnObject.getJSONArray("T_MARA");
        // ********  T_MBEW 物料评估价格 ********
        JSONArray paramsOfMbew = returnObject.getJSONArray("T_MBEW");
        // ********  T_QBEW 项目库存评估价格 ********
        JSONArray paramsOfQbew = returnObject.getJSONArray("T_QBEW");

        if (Const.ERP_RETURN_TYPE_S.equals(paramsOfReturn.getString("CODE"))) {
            // 接口调用成功时
            // 保存/更新物料及工厂物料
            Map<String, Long> ftyCodeMap = new HashMap<>();
            Map<String, Long> unitCodeMap = new HashMap<>();
            Map<String, Long> groupCodeMap = new HashMap<>();
            Map<String, Long> typeCodeMap = new HashMap<>();
            Map<String, JSONObject> mbewMap = new HashMap<>();
            Map<String, JSONObject> qbewMap = new HashMap<>();
            // 物料
            List<DicMaterialDTO> materialDTOList = new ArrayList<>();
            Set<String> maraCodeSet = new HashSet<>();
            // 物料工厂
            Map<String, List<DicMaterialFactoryDTO>> maraFtyMap = new HashMap<>();
            // 计量单位
            Set<DicUnit> unitSet = new HashSet<>();
            // 物料组
            Set<DicMaterialGroup> materialGroupSet = new HashSet<>();
            // 物料类型
            Set<DicMaterialType> materialTypeSet = new HashSet<>();
            // 物料明细
            Map<String, Integer> pkgTypeMap = new HashMap<>();

            // 物料评估价格按照物料编码调整为Map结构，便于物料主数据取值
            for (int i = 0; i < paramsOfMbew.size(); i++) {
                JSONObject mbew = paramsOfMbew.getJSONObject(i);
                String matCode = mbew.getString("MATNR");
                mbewMap.put(matCode, mbew);
            }

            // 项目库存评估价格按照物料编码调整为Map结构，便于物料主数据取值
            for (int i = 0; i < paramsOfQbew.size(); i++) {
                JSONObject qbew = paramsOfQbew.getJSONObject(i);
                String matCode = qbew.getString("MATNR");
                qbewMap.put(matCode, qbew);
            }


            /*
             * ！注意：
             * paramsOfMara当前由于历史设计原因，对应结构T_MARA是一个包含了"工厂"信息的结构体
             * 因此，相同物料在不同工厂时，会有多个记录
             */
            for (int i = 0; i < paramsOfMara.size(); i++) {
                JSONObject maraObj = paramsOfMara.getJSONObject(i);

                String factoryCode = maraObj.getString("WERKS");  // 工厂
                if (getFtyId(ftyCodeMap, factoryCode) == null) {
                    // 校验工厂id是否存在（仅通过物料编码查询时，可能会返回WMS尚未维护的工厂物料数据）
                    continue;
                }

                String maraCode = maraObj.getString("MATNR");
                // 物料编码通过Set结构去重 -- 处理物料主数据部分的公共数据，仅循环中MatCode首次出现时处理
                if (maraCodeSet.add(maraCode)) {
                    // 物料主数据保存/更新
                    DicMaterialDTO materialDTO = new DicMaterialDTO();
                    materialDTO.setMatCode(maraCode); // 物料编码
                    materialDTO.setMatName(maraObj.getString("MAKTX")); // 物料描述（短文本）
                    materialDTO.setUnitCode(maraObj.getString("MEINS")); // 计量单位编码
                    materialDTO.setMatGroupCode(maraObj.getString("MATKL")); // 物料组编码
                    materialDTO.setMatTypeCode(maraObj.getString("MTART")); // 物料类型编码
                    materialDTO.setEprio(null); // 存储级别，物料主数据不维护库存确定组，该数据保存在“工厂物料主数据”上
                    materialDTO.setLength(BigDecimal.ZERO); // 长度
                    materialDTO.setWidth(BigDecimal.ZERO); // 宽度
                    materialDTO.setHeight(BigDecimal.ZERO); // 高度
                    materialDTO.setUnitLength(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_LENGTH.getValue()); // 长度/宽度/高度的单位
                    materialDTO.setGrossWeight(BigDecimal.ZERO); // 毛重
                    materialDTO.setNetWeight(BigDecimal.ZERO); // 净重
                    materialDTO.setUnitWeight(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_WEIGHT.getValue()); // 重量的单位
                    materialDTO.setWeightTolerance(BigDecimal.ZERO); // 重量容差
                    materialDTO.setVolume(BigDecimal.ZERO); // 体积
                    materialDTO.setUnitVolume(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_VOLUME.getValue()); // 体积的单位
                    materialDTO.setShelfLife(0); // 保质期
                    materialDTO.setUnitShelfLife(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_SHELF_LIFE.getValue()); // 保质期的单位
                    materialDTO.setIsShelfLife(EnumRealYn.FALSE.getIntValue()); // 是否启用保质期
                    materialDTO.setIsFreeze(EnumRealYn.FALSE.getIntValue()); // 是否冻结
                    materialDTO.setIsDangerous(EnumRealYn.FALSE.getIntValue()); // 是否危险物料
                    materialDTO.setShelfLifeMax(maraObj.getInteger("MHDHB")); // 总货架寿命
                    materialDTO.setShelfLifeMin(maraObj.getInteger("MHDRZ")); // 最小货架寿命
                    // 2024-07-29 二期新增响应字段
                    materialDTO.setExtBasicMaterial(maraObj.getString("ZZJBWL"));  // 基本物料
                    materialDTO.setExtWarrantyLevel(maraObj.getString("ZZBDJ"));  // 质保等级
                    materialDTO.setExtWarrantyLevelDesc(maraObj.getString("ZZJBWL"));  // 质保等级描述
                    materialDTO.setExtIsUnderNuclearSafetySupervision(maraObj.getString("ZSFHJG"));  // 是否核安全局监管备件
                    materialDTO.setExtIsUnderNuclearSafetySupervisionDesc(maraObj.getString("ZSFHJGT"));  // 是否核安全局监管备件描述
                    materialDTO.setExtManufacturerDrawingNumber(maraObj.getString("ZZZTZ"));  // 制造厂图纸号
                    materialDTO.setExtManufacturerDrawingItemNumber(maraObj.getString("ZZZTX"));  // 制造厂图项号
                    materialDTO.setExtManufacturer(maraObj.getString("ZZCCJ"));  // 制造厂家
                    materialDTO.setExtManufacturerCode(maraObj.getString("ZZZSBH"));  // 制造商编码
                    materialDTO.setExtManufacturerName(maraObj.getString("ZZZSBHT"));  // 制造商名称
                    materialDTO.setExtManufacturerPartNumber(maraObj.getString("ZZZSLJ"));  // 制造商零件编号
                    materialDTO.setExtIsRadioactive(maraObj.getString("ZSFFSX"));  // 是否带放射性
                    materialDTO.setExtIsRadioactiveDesc(maraObj.getString("ZSFFSXT"));  // 是否带放射性描述
                    materialDTO.setExtMaterialClassification(maraObj.getString("ZWZFL"));  // 物资分类
                    materialDTO.setExtMaterialClassificationDesc(maraObj.getString("ZWZFLT"));  // 物资分类描述
                    materialDTO.setExtPurchaseOrderText(maraObj.getString("ZCGDDWB"));  // 采购订单文本
                    // 2024-07-29 二期新增，物料评估类特殊处理，尝试从MBEW结构取，取不到时则尝试从QBEW结构取
                    if (mbewMap.get(maraCode) != null && UtilObject.isNotEmpty(mbewMap.get(maraCode).get("BKLAS"))) {
                        materialDTO.setExtEvaluationClassification(mbewMap.get(maraCode).get("BKLAS").toString());  // 评估类
                        materialDTO.setExtEvaluationClassificationDesc(mbewMap.get(maraCode).get("BKBEZ").toString());  // 评估类描述
                    } else if (qbewMap.get(maraCode) != null && UtilObject.isNotEmpty(qbewMap.get(maraCode).get("BKLAS"))) {
                        materialDTO.setExtEvaluationClassification(qbewMap.get(maraCode).get("BKLAS").toString());  // 评估类
                        materialDTO.setExtEvaluationClassificationDesc(qbewMap.get(maraCode).get("BKBEZ").toString());  // 评估类描述
                    }

                    materialDTOList.add(materialDTO);
                }


                String pkgTypeStr = maraObj.getString("ZBZFS");  // 包装方式
                String depositTypeStr = maraObj.getString("ZCFFS");  // 存放方式
                // 只记录JO46的包装类型
                int pkgType = 0;
                if (NumberUtil.isNumber(pkgTypeStr)) {
                    pkgType = Integer.parseInt(pkgTypeStr);
                }
                if ("J046".equalsIgnoreCase(factoryCode)) {
                    // 修正工厂物料中除J046外的其他工厂保存不上包装类型的问题。此处保持原有逻辑，物料主数据中的包装类型与J046工厂一致
                    pkgTypeMap.put(maraCode, pkgType);
                }

                // 存放类型字段文本数值转换
                int depositType = 0;
                if (StringUtils.isNotBlank(depositTypeStr)) {
                    if (NumberUtil.isNumber(depositTypeStr)) {
                        // 原始值是数字，直接存储
                        depositType = Integer.parseInt(depositTypeStr);
                    } else {
                        // 原始值是文本，转换后存储
                        if ("直立存放".equals(depositTypeStr)) {
                            depositType = 1;
                        }
                        if ("悬挂".equals(depositTypeStr)) {
                            depositType = 2;
                        }
                        if ("朝上存放".equals(depositTypeStr)) {
                            depositType = 3;
                        }
                        if ("非关闭状态".equals(depositTypeStr)) {
                            depositType = 4;
                        }
                        if ("倒置存放".equals(depositTypeStr)) {
                            depositType = 5;
                        }
                    }
                }
                // 工厂物料主数据信息保存/更新
                DicMaterialFactoryDTO materialFactoryDTO = new DicMaterialFactoryDTO();
                materialFactoryDTO.setMatCode(maraCode); // 物料编码
                materialFactoryDTO.setFtyId(getFtyId(ftyCodeMap, factoryCode)); // 工厂id
                materialFactoryDTO.setInspectClassifyId(0L); // 验收分类id
                materialFactoryDTO.setMaterialClassifyId(0L); // 物料分类id
                materialFactoryDTO.setTagType(EnumTagType.METAL_UNRESISTANT.getValue()); // 标签类型
                materialFactoryDTO.setIsSingle(EnumRealYn.FALSE.getIntValue()); // 单品/批次
                materialFactoryDTO.setShelfLife(0); // 保质期
                materialFactoryDTO.setRemindDay(0); // 临期预警天数
                materialFactoryDTO.setSecurityQty(BigDecimal.ZERO); // 安全库存数量
                materialFactoryDTO.setOrderPointQty(BigDecimal.ZERO); // 订货点数量
                materialFactoryDTO.setIsBatchErpEnabled(EnumRealYn.FALSE.getIntValue()); // 是否启用ERP批次
                materialFactoryDTO.setIsBatchProductEnabled(EnumRealYn.FALSE.getIntValue()); // 是否启用生产批次
                materialFactoryDTO.setIsPackageEnabled(EnumRealYn.FALSE.getIntValue()); // 是否启用包装物
                materialFactoryDTO.setShelfLifeMax(maraObj.getInteger("MHDHB")); // 总货架寿命
                materialFactoryDTO.setShelfLifeMin(maraObj.getInteger("MHDRZ")); // 最小货架寿命
                materialFactoryDTO.setPackageType(pkgType); // 包装方式
                materialFactoryDTO.setDepositType(depositType); // 存放方式

                String stockGroup = maraObj.getString("EPRIO");  // 库存确定组
                materialFactoryDTO.setStockGroup(stockGroup);
                if (StringUtils.isNotBlank(stockGroup)) {
                    Long locationId = dictionaryService.getLocationIdCacheByCode(factoryCode, stockGroup);
                    if (locationId != null) {
                        materialFactoryDTO.setStockGroupId(locationId);
                    }
                }

                List<DicMaterialFactoryDTO> maraFtyList = maraFtyMap.get(maraCode);
                if (maraFtyList == null) {
                    maraFtyList = new ArrayList<>();
                }
                maraFtyList.add(materialFactoryDTO);
                // 这里原有代码意图为：暂存到map变量中，后续待处理完评估价格数据结构后，一并进行数据持久化，不在循环内处理
                maraFtyMap.put(maraCode, maraFtyList);


                // 计量单位
                String meins = maraObj.getString("MEINS");
                if (UtilString.isNotNullOrEmpty(meins) && UtilNumber.isEmpty(getUnitId(unitCodeMap, meins))) {
                    // 缓存中不存在此计量单位数据，需要新增
                    DicUnit unit = new DicUnit();
                    unit.setUnitCode(meins); // 计量单位编码
                    unit.setUnitName(maraObj.getString("MSEHL")); // 计量单位描述
                    unitSet.add(unit);
                }
                // 物料组
                String matkl = maraObj.getString("MATKL");
                if (UtilString.isNotNullOrEmpty(matkl) && UtilNumber.isEmpty(getMaraGroupId(groupCodeMap, matkl))) {
                    // 缓存中不存在此物料组数据，需要新增
                    DicMaterialGroup materialGroup = new DicMaterialGroup();
                    materialGroup.setMatGroupCode(maraObj.getString("MATKL")); // 物料组编码
                    materialGroup.setMatGroupName(maraObj.getString("WGBEZ")); // 物料组描述
                    materialGroupSet.add(materialGroup);
                }
                // 物料类型
                String mtart = maraObj.getString("MTART");
                if (UtilString.isNotNullOrEmpty(mtart) && UtilNumber.isEmpty(getMaraTypeId(typeCodeMap, mtart))) {
                    // 缓存中不存在此物料类型数据，需要新增
                    DicMaterialType materialType = new DicMaterialType();
                    materialType.setMatTypeCode(maraObj.getString("MTART")); // 物料类型编码
                    materialType.setMatTypeName(maraObj.getString("MTBEZ")); // 物料类型描述
                    materialTypeSet.add(materialType);
                }
            } // # end for paramsOfMara loop



            // 物料评估价格（T_MBEW 多值结构）
            for (int i = 0; i < paramsOfMbew.size(); i++) {
                JSONObject mbewObj = paramsOfMbew.getJSONObject(i);
                String mbewMaraCode = mbewObj.getString("MATNR");
                List<DicMaterialFactoryDTO> maraFtyList = maraFtyMap.get(mbewMaraCode);
                for (DicMaterialFactoryDTO materialFactoryDTO : maraFtyList) {
                    // 这里借由变量引用，修改原map内的字段值
                    materialFactoryDTO.setMoveAvgPrice(UtilObject.getBigDecimalOrZero(mbewObj.getBigDecimal("VERPR"))); // 移动平均价
                    materialFactoryDTO.setPriceUnit(mbewObj.getString("PEINH")); // 价格单位
                    materialFactoryDTO.setPriceMethod(UtilNumber.isNotEmpty(mbewObj.getBigDecimal("VERPR")) ? "V" : "S"); // 计价方式
                    materialFactoryDTO.setStandardPrice(mbewObj.getBigDecimal("STPRS")); // 标准价格
                }
            }

            // ############### SAP响应参数处理结束↑↑↑ ################
            // ############### 数据持久化过程开始↓↓↓ #################

            // 在更新物料主数据、工厂物料主数据、项目工厂物料主数据之前，先对基础主数据进行保存/更新
            if (UtilCollection.isNotEmpty(unitSet)) {
                // 保存计量单位（仅缓存中不存在时）
                dicUnitDataWrap.saveBatch(unitSet);
                // 刷新缓存
                editCacheService.refreshUnitCache();
            }
            if (UtilCollection.isNotEmpty(materialGroupSet)) {
                // 保存物料组（仅缓存中不存在时）
                dicMaterialGroupDataWrap.saveBatch(materialGroupSet);
                // 刷新缓存
                editCacheService.refreshDicMaterialGroupCache();
            }
            if (UtilCollection.isNotEmpty(materialTypeSet)) {
                // 保存物料类型（仅缓存中不存在时）
                dicMaterialTypeDataWrap.saveBatch(materialTypeSet);
                // 刷新缓存
                editCacheService.refreshDicMaterialTypeCache();
            }


            // 批量保存和更新物料主数据
            // 此处手工设置写入分片和滑动窗口的方式并不推荐，分批处理已通过底层saveBatch方法实现，这里暂时不做修改
            if (UtilCollection.isNotEmpty(materialDTOList)) {
                // 回填 物料-记录单位id 物料组id 物料类型id
                materialDTOList.forEach(p -> {
                    Long unitId = getUnitId(unitCodeMap, p.getUnitCode());
                    p.setUnitId(UtilNumber.isNotEmpty(unitId) ? unitId : 0L); // 计量单位id
                    Long maraGroupId = getMaraGroupId(groupCodeMap, p.getMatGroupCode());
                    p.setMatGroupId(UtilNumber.isNotEmpty(maraGroupId) ? maraGroupId : 0L); // 物料组id
                    Long maraTypeId = getMaraTypeId(typeCodeMap, p.getMatTypeCode());
                    p.setMatTypeId(UtilNumber.isNotEmpty(maraTypeId) ? maraTypeId : 0L); // 物料类型id
                    Integer pkgType = pkgTypeMap.get(p.getMatCode());
                    if (pkgType != null) {
                        p.setPackageType(pkgType);
                    }
                });
                int size = materialDTOList.size();
                int pageSize = 200;
                int page = size / pageSize;
                int remainder = size % pageSize;
                if (remainder > 0) {
                    page++;
                }
                boolean checkPkgType = false;
                if ("J046".equalsIgnoreCase(ftyCode)) {
                    checkPkgType = true;
                }
                List<DicMaterialDTO> materialSaveList = new ArrayList<>();
                for (int i = 0; i < page; i++) {
                    int startIdx = i * pageSize;
                    int endIdx = (i + 1) * pageSize;
                    if (endIdx > size) {
                        endIdx = size;
                    }
                    List<String> maraCodeList = new ArrayList<>(pageSize);
                    for (int j = startIdx; j < endIdx; j++) {
                        maraCodeList.add(materialDTOList.get(j).getMatCode());
                    }
                    // 根据物料编码查询已有物料
                    QueryWrapper<DicMaterial> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().in(DicMaterial::getMatCode, maraCodeList);
                    List<DicMaterial> materialList = dicMaterialDataWrap.list(queryWrapper);
                    if (CollectionUtils.isNotEmpty(materialList)) {
                        Map<String, DicMaterial> dicMaraMap = new HashMap<>();
                        for (DicMaterial dicMaterial : materialList) {
                            dicMaraMap.put(dicMaterial.getMatCode(), dicMaterial);
                        }
                        for (int j = startIdx; j < endIdx; j++) {
                            DicMaterialDTO maraDTO = materialDTOList.get(j);
                            String maraCode = maraDTO.getMatCode();
                            DicMaterial dicMara = dicMaraMap.get(maraCode);
                            if (dicMara != null) {
                                StringBuilder existKeyBuilder = new StringBuilder();
                                if (checkPkgType) {
                                    existKeyBuilder.append(dicMara.getPackageType());
                                }
                                existKeyBuilder.append(dicMara.getShelfLifeMin())
                                        .append(dicMara.getShelfLifeMax())
                                        .append(dicMara.getMatTypeId())
                                        .append(dicMara.getMatGroupId())
                                        .append(dicMara.getUnitId())
                                        .append(dicMara.getMatName())
                                        .append(dicMara.getMatCode())
                                        .append(dicMara.getExtEvaluationClassification())
                                        .append(dicMara.getExtEvaluationClassificationDesc());
                                // 唯一字符串拼接逻辑 = 最小货架寿命+最大货架寿命+物料分类id+物料组id+计量单位id+物料名称+物料编码+评估类+评估类描述
                                // 缺点：没有分隔符，字段值直接拼接在极少数情况可能出现混淆，二期项目工期限制，暂不做处理
                                String existKey = existKeyBuilder.toString();
                                Integer synPackageType = maraDTO.getPackageType();
                                if (synPackageType == null) {
                                    synPackageType = 0;
                                }
                                StringBuilder synKeyBuilder = new StringBuilder();
                                if (checkPkgType) {
                                    synKeyBuilder.append(synPackageType);
                                }
                                synKeyBuilder.append(maraDTO.getShelfLifeMin())
                                        .append(maraDTO.getShelfLifeMax())
                                        .append(maraDTO.getMatTypeId())
                                        .append(maraDTO.getMatGroupId())
                                        .append(maraDTO.getUnitId())
                                        .append(maraDTO.getMatName())
                                        .append(maraDTO.getMatCode())
                                        .append(maraDTO.getExtEvaluationClassification())
                                        .append(maraDTO.getExtEvaluationClassificationDesc());
                                String synKey = synKeyBuilder.toString();

                                if (!synKey.equals(existKey)) {
                                    // 复制属性值 这些字段不让更新 还用数据库里的值
                                    maraDTO.setId(dicMara.getId());
                                    maraDTO.setMainFlag(dicMara.getMainFlag());
                                    maraDTO.setCreateTime(dicMara.getCreateTime());
                                    maraDTO.setIsDelete(dicMara.getIsDelete());
                                    maraDTO.setIsDangerous(dicMara.getIsDangerous());
                                    maraDTO.setIsFreeze(dicMara.getIsFreeze());
                                    maraDTO.setIsShelfLife(dicMara.getIsShelfLife());
                                    maraDTO.setUnitShelfLife(dicMara.getUnitShelfLife());
                                    maraDTO.setShelfLife(dicMara.getShelfLife());
                                    maraDTO.setUnitVolume(dicMara.getUnitVolume());
                                    maraDTO.setVolume(dicMara.getVolume());
                                    maraDTO.setWeightTolerance(dicMara.getWeightTolerance());
                                    maraDTO.setUnitWeight(dicMara.getUnitWeight());
                                    maraDTO.setNetWeight(dicMara.getNetWeight());
                                    maraDTO.setGrossWeight(dicMara.getGrossWeight());
                                    maraDTO.setUnitLength(dicMara.getUnitLength());
                                    maraDTO.setHeight(dicMara.getHeight());
                                    maraDTO.setWidth(dicMara.getWidth());
                                    maraDTO.setLength(dicMara.getLength());
                                    maraDTO.setDepositType(dicMara.getDepositType());
                                    maraDTO.setParentMatId(dicMara.getParentMatId());
                                    maraDTO.setEprio(dicMara.getEprio());
                                    if (!checkPkgType) {
                                        maraDTO.setPackageType(dicMara.getPackageType());
                                    }
                                    materialSaveList.add(maraDTO);
                                }
                            } else {
                                materialSaveList.add(maraDTO);
                            }
                        }
                    } else {
                        for (int j = startIdx; j < endIdx; j++) {
                            DicMaterialDTO maraDTO = materialDTOList.get(j);
                            materialSaveList.add(maraDTO);
                        }
                    }
                }
                int maraSaveSize = materialSaveList.size();
                log.info("synchronization material num in factory({}): {}", ftyCode, maraSaveSize);
                if (maraSaveSize > 0) {
                    // 保存物料
                    dicMaterialDataWrap.saveOrUpdateBatchDto(materialSaveList);
                    // 刷新缓存
                    editCacheService.refreshMatCacheByMatIdList(materialSaveList.stream().map(p -> p.getId()).collect(Collectors.toList()));
                }
            }

            // 批量保存和更新“工厂物料主数据”，以及批量保存和更新“项目工厂物料主数据”
            // 此处手工设置写入分片和滑动窗口的方式并不推荐，分批处理已通过底层saveBatch方法实现，这里暂时不做修改
            List<DicMaterialFactoryDTO> materialFactoryDTOList = new ArrayList<>();
            for (List<DicMaterialFactoryDTO> itemList : maraFtyMap.values()) {
                materialFactoryDTOList.addAll(itemList);
            }

            if (UtilCollection.isNotEmpty(materialFactoryDTOList)) {
                // 回填 物料工厂-物料id
                materialFactoryDTOList.forEach(p -> {
                    p.setMatId(dictionaryService.getMatIdByMatCode(p.getMatCode()));
                });
                int size = materialFactoryDTOList.size();
                int pageSize = 200;
                int page = size / pageSize;
                int remainder = size % pageSize;
                if (remainder > 0) {
                    page++;
                }
                List<DicMaterialFactoryDTO> maraFtySaveList = new ArrayList<>();
                List<DicMaterialFactoryDTO> maraFtyBatchList = new ArrayList<>();
                for (int i = 0; i < page; i++) {
                    int startIdx = i * pageSize;
                    int endIdx = (i + 1) * pageSize;
                    if (endIdx > size) {
                        endIdx = size;
                    }
                    List<Long> ftyIdList = new ArrayList<>(pageSize);
                    List<Long> maraIdList = new ArrayList<>(pageSize);
                    for (int j = startIdx; j < endIdx; j++) {
                        DicMaterialFactoryDTO maraFtyDTO = materialFactoryDTOList.get(j);
                        ftyIdList.add(maraFtyDTO.getFtyId());
                        maraIdList.add(maraFtyDTO.getMatId());
                    }
                    // 根据工厂+物料查询已有工厂物料
                    QueryWrapper<DicMaterialFactory> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().in(DicMaterialFactory::getFtyId, ftyIdList).in(DicMaterialFactory::getMatId, maraIdList);
                    List<DicMaterialFactory> materialFactoryList = dicMaterialFactoryDataWrap.list(queryWrapper);
                    if (CollectionUtils.isNotEmpty(materialFactoryList)) {
                        Map<String, DicMaterialFactory> maraFtyEntityMap = new HashMap<>();
                        for (DicMaterialFactory dicMaraFty : materialFactoryList) {
                            String key = dicMaraFty.getFtyId() + "-" + dicMaraFty.getMatId();
                            maraFtyEntityMap.put(key, dicMaraFty);
                        }
                        for (int j = startIdx; j < endIdx; j++) {
                            DicMaterialFactoryDTO maraFtyDTO = materialFactoryDTOList.get(j);
                            String key = maraFtyDTO.getFtyId() + "-" + maraFtyDTO.getMatId();
                            DicMaterialFactory dicMaraFty = maraFtyEntityMap.get(key);
                            if (dicMaraFty != null) {
                                BigDecimal existStandardPrice = dicMaraFty.getStandardPrice();
                                BigDecimal existMoveAvgPrice = dicMaraFty.getMoveAvgPrice();
                                StringBuilder existKeyBuilder = new StringBuilder();
                                Integer existLifeMax = dicMaraFty.getShelfLifeMax();
                                existKeyBuilder.append(dicMaraFty.getDepositType()).append(dicMaraFty.getPackageType()).append(dicMaraFty.getShelfLifeMin()).append(existLifeMax);
                                existKeyBuilder.append(dicMaraFty.getPriceMethod()).append(dicMaraFty.getPriceUnit()).append(dicMaraFty.getFtyId()).append(dicMaraFty.getMatId()).append(dicMaraFty.getStockGroup());
                                // 唯一字符串拼接逻辑 = 存放方式（值）+包装方式（值）+最小货架寿命+最大货架寿命+计价方式+价格单位+工厂id+物料id+库存确定组
                                // 缺点：没有分隔符，字段值直接拼接在极少数情况可能出现混淆，二期项目工期限制，暂不做处理
                                String existKey = existKeyBuilder.toString();
                                BigDecimal synStandardPrice = maraFtyDTO.getStandardPrice();
                                BigDecimal synMoveAvgPrice = maraFtyDTO.getMoveAvgPrice();
                                String synPriceMethod = maraFtyDTO.getPriceMethod();
                                if (synPriceMethod == null) {
                                    synPriceMethod = "";
                                }
                                String synPriceUnit = maraFtyDTO.getPriceUnit();
                                if (synPriceUnit == null) {
                                    synPriceUnit = "";
                                }
                                if (synStandardPrice == null) {
                                    synStandardPrice = BigDecimal.ZERO;
                                }
                                if (synMoveAvgPrice == null) {
                                    synMoveAvgPrice = BigDecimal.ZERO;
                                }
                                StringBuilder synKeyBuilder = new StringBuilder();
                                Integer syncLifeMax = maraFtyDTO.getShelfLifeMax();
                                synKeyBuilder.append(maraFtyDTO.getDepositType()).append(maraFtyDTO.getPackageType()).append(maraFtyDTO.getShelfLifeMin()).append(syncLifeMax);
                                synKeyBuilder.append(synPriceMethod).append(synPriceUnit).append(maraFtyDTO.getFtyId()).append(maraFtyDTO.getMatId()).append(maraFtyDTO.getStockGroup());
                                String synKey = synKeyBuilder.toString();
                                if ((synStandardPrice != null && existStandardPrice.compareTo(synStandardPrice) != 0) || (synMoveAvgPrice != null && existMoveAvgPrice.compareTo(synMoveAvgPrice) != 0) || !synKey.equals(existKey)) {
                                    // 复制属性值 这些字段不让更新 还用数据库里的值
                                    maraFtyDTO.setId(dicMaraFty.getId());
                                    maraFtyDTO.setUnitizedFlag(dicMaraFty.getUnitizedFlag());
                                    maraFtyDTO.setCreateTime(dicMaraFty.getCreateTime());
                                    maraFtyDTO.setIsPackageEnabled(dicMaraFty.getIsPackageEnabled());
                                    maraFtyDTO.setIsBatchProductEnabled(dicMaraFty.getIsBatchProductEnabled());
                                    maraFtyDTO.setIsBatchErpEnabled(dicMaraFty.getIsBatchErpEnabled());
                                    maraFtyDTO.setOrderPointQty(dicMaraFty.getOrderPointQty());
                                    maraFtyDTO.setSecurityQty(dicMaraFty.getSecurityQty());
                                    maraFtyDTO.setRemindDay(dicMaraFty.getRemindDay());
                                    maraFtyDTO.setShelfLife(dicMaraFty.getShelfLife());
                                    maraFtyDTO.setIsSingle(dicMaraFty.getIsSingle());
                                    maraFtyDTO.setTagType(dicMaraFty.getTagType());
                                    maraFtyDTO.setMaterialClassifyId(dicMaraFty.getMaterialClassifyId());
                                    maraFtyDTO.setInspectClassifyId(dicMaraFty.getInspectClassifyId());
                                    maraFtyDTO.setMaintainProFlag(dicMaraFty.getMaintainProFlag());

                                    maraFtySaveList.add(maraFtyDTO);
                                    if (syncLifeMax != null && existLifeMax != null && !existLifeMax.equals(syncLifeMax)) {
                                        maraFtyBatchList.add(maraFtyDTO);
                                    }
                                }
                            } else {
                                maraFtySaveList.add(maraFtyDTO);
                            }
                        }
                    } else {
                        for (int j = startIdx; j < endIdx; j++) {
                            DicMaterialFactoryDTO maraFtyDTO = materialFactoryDTOList.get(j);
                            maraFtySaveList.add(maraFtyDTO);
                        }
                    }
                }
                int maraFtySaveSize = maraFtySaveList.size();
                log.info("synchronization material factory num in factory({}): {}", ftyCode, maraFtySaveSize);
                if (maraFtySaveSize > 0) {
                    // 保存物料工厂
                    dicMaterialFactoryDataWrap.saveOrUpdateBatchDto(maraFtySaveList);
                    dataFillService.fillAttr(maraFtySaveList);
                    // 刷新缓存
                    dictionaryService.refreshDicMaterialFactoryByUniqueKey(maraFtySaveList);
                }

                // 注意!!  在这里更新了 项目工厂物料主数据！！
                // dic_material_facotry_wbs表 数据更新
                // 刷新项目库存评估价格
                this.saveOrUpdateMatWbsNew(paramsOfQbew);


                // 刷新批次信息的寿期到期日期（注意！！应仅针对没有做过寿期业务的物料进行此处理）
                int maraFtyBatchSize = maraFtyBatchList.size();
                log.info("synchronization material batch num in factory({}): {}", ftyCode, maraFtyBatchSize);
                if (maraFtyBatchSize > 0) {
                    updateBatchDate(maraFtyBatchList);
                }
            }
        } else {
            //接口调用失败
            log.info("synchronization material elapsed(second): {}", System.currentTimeMillis() - startTime);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, UtilObject.getStringOrEmpty(paramsOfReturn.getString("MES")));
        }
        log.debug("synchronization material elapsed(second): {}", System.currentTimeMillis() - startTime);
        log.info("{}工厂下物料信息同步成功，耗时：{}秒", ftyCode, (System.currentTimeMillis() - startTime) / 1000);
    }

    private Long getFtyId(Map<String, Long> ftyCodeMap, String ftyCode) {
        Long id = ftyCodeMap.get(ftyCode);
        if (id == null) {
            id = dictionaryService.getFtyIdCacheByCode(ftyCode);
            ftyCodeMap.put(ftyCode, id);
        }
        return id;
    }

    private Long getUnitId(Map<String, Long> unitCodeMap, String unitCode) {
        Long id = unitCodeMap.get(unitCode);
        if (id == null) {
            id = dictionaryService.getUnitIdCacheByCode(unitCode);
            unitCodeMap.put(unitCode, id);
        }
        return id;
    }

    private Long getMaraGroupId(Map<String, Long> groupCodeMap, String maraGroupCode) {
        Long id = groupCodeMap.get(maraGroupCode);
        if (id == null) {
            id = dictionaryService.getMatGroupIdByMatGroupCode(maraGroupCode);
            groupCodeMap.put(maraGroupCode, id);
        }
        return id;
    }

    private Long getMaraTypeId(Map<String, Long> typeCodeMap, String typeCode) {
        Long id = typeCodeMap.get(typeCode);
        if (id == null) {
            id = dictionaryService.getMatTypeIdByMatTypeCode(typeCode);
            typeCodeMap.put(typeCode, id);
        }
        return id;
    }

    public void saveOrUpdateMatWbsNew(JSONArray paramsOfQbew){
        if (paramsOfQbew !=null && !paramsOfQbew.isEmpty()) {
            List<DicMaterialFacotryWbs> mWbsList = new ArrayList<>();
            for (int i=0 ; i < paramsOfQbew.size(); i++) {
                JSONObject qbew = paramsOfQbew.getJSONObject(i);
                String matCode = qbew.getString("MATNR");
                String ftyCode = qbew.getString("BWKEY");
                String specStock = qbew.getString("SOBKZ");
                String wbs = qbew.getString("POSID");
                BigDecimal moveAvgPrice = UtilObject.getBigDecimalOrZero(qbew.getString("VERPR"));
                BigDecimal totalPrice = UtilObject.getBigDecimalOrZero(qbew.getString("SALK3"));
                BigDecimal totalQty = UtilObject.getBigDecimalOrZero(qbew.getString("LBKUM"));
                Long matId = dictionaryService.getMatIdByMatCode(matCode);
                Long ftyId = dictionaryService.getFtyIdCacheByCode(ftyCode);
                DicMaterialFacotryWbs mwbs = new DicMaterialFacotryWbs();
                mwbs.setMatId(matId);
                mwbs.setFtyId(ftyId);
                mwbs.setSpecStock(specStock);
                mwbs.setSpecStockCode(wbs);
                mwbs.setMoveAvgPrice(moveAvgPrice);
                mwbs.setTotalPrice(totalPrice);
                mwbs.setTotalQty(totalQty);
                mWbsList.add(mwbs);
            }
            if (UtilCollection.isNotEmpty(mWbsList)) {
                int size = mWbsList.size();
                int pageSize = 200;
                int page = size / pageSize;
                int remainder = size % pageSize;
                if (remainder > 0) {
                    page++;
                }
                List<DicMaterialFacotryWbs> wbsSaveList = new ArrayList<>();
                for (int i = 0; i < page; i++) {
                    int startIdx = i * pageSize;
                    int endIdx = (i + 1) * pageSize;
                    if (endIdx > size) {
                        endIdx = size;
                    }
                    List<Long> maraIdList = new ArrayList<>(pageSize);
                    Set<Long> maraIdSet = new HashSet<>(pageSize);
                    for (int j = startIdx; j < endIdx; j++) {
                        DicMaterialFacotryWbs wbsDTO = mWbsList.get(j);
                        Long maraId = wbsDTO.getMatId();
                        if (maraIdSet.add(maraId)) {
                            maraIdList.add(maraId);
                        }
                    }
                    // 反写 id 用于更新
                    QueryWrapper<DicMaterialFacotryWbs> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().in(DicMaterialFacotryWbs::getMatId, maraIdList);
                    List<DicMaterialFacotryWbs> findList = dicMaterialFacotryWbsDataWrap.list(queryWrapper);
                    if (UtilCollection.isNotEmpty(findList)) {
                        Map<String,DicMaterialFacotryWbs> wbsMap = findList.stream().collect(Collectors.toMap(e->e.getMatId()+"-"+e.getFtyId()+"-"+e.getSpecStock()+"-"+e.getSpecStockCode(),e->e,(k1,k2)->k2));
                        for (int j = startIdx; j < endIdx; j++) {
                            DicMaterialFacotryWbs wbsDTO = mWbsList.get(j);
                            String key = wbsDTO.getMatId() + "-" + wbsDTO.getFtyId() + "-" + wbsDTO.getSpecStock() + "-" + wbsDTO.getSpecStockCode();
                            DicMaterialFacotryWbs find = wbsMap.get(key);
                            if (find != null) {
                                wbsDTO.setId(find.getId());
                                BigDecimal existAvgPrice = find.getMoveAvgPrice();
                                BigDecimal existTotalPrice = find.getTotalPrice();
                                BigDecimal existTotalQty = find.getTotalQty();
                                BigDecimal synAvgPrice = wbsDTO.getMoveAvgPrice();
                                BigDecimal synTotalPrice = wbsDTO.getTotalPrice();
                                BigDecimal synTotalQty = wbsDTO.getTotalQty();
                                if ((synAvgPrice != null && existAvgPrice.compareTo(synAvgPrice) !=0) || (synTotalPrice != null && existTotalPrice.compareTo(synTotalPrice) !=0) || (synTotalQty != null && existTotalQty.compareTo(synTotalQty) !=0)) {
                                    wbsSaveList.add(wbsDTO);
                                }
                            } else {
                                wbsSaveList.add(wbsDTO);
                            }
                        }
                    } else {
                        for (int j = startIdx; j < endIdx; j++) {
                            DicMaterialFacotryWbs wbsDTO = mWbsList.get(j);
                            wbsSaveList.add(wbsDTO);
                        }
                    }
                }
                int wbsSaveSize = wbsSaveList.size();
                log.info("synchronization material wbs num: {}", wbsSaveSize);
                if (wbsSaveSize > 0) {
                    dicMaterialFacotryWbsDataWrap.saveOrUpdateBatchOptimize(wbsSaveList,200);
                    // 更新缓存数据
                    editCacheService.refreshDicMaterialFacotryWbsCache(wbsSaveList);
                }
            }
        }
    }

    /**
     * <p>
     * 2023-05-15 由于现有方案设计时，同步数据时未对已有数据进行比对，会导致货架寿命变更后重算物料到期时间引起寿期业务回退
     * 故在同步物料主数据时，暂时不更新工厂物料主数据中的【到期时间】，总货架寿命在SAP变更后，由人工处理数据不一致的问题
     * </p><br/>
     * <p>
     * 2024-07-30 石岛湾二期项目，修改此部分逻辑
     * 针对WMS中未做过寿期检定及延期业务的物料进行批次信息的到期日期进行更新
     * </p>
     */
    private void updateBatchDate(List<DicMaterialFactoryDTO> maraFtyBatchList) {
        // 针对WMS中未做过寿期检定及延期业务的物料进行批次信息的到期日期进行更新
        int size = maraFtyBatchList.size();
        int pageSize = 200;
        int page = size / pageSize;
        int remainder = size % pageSize;
        if (remainder > 0) {
            page++;
        }
        for (int i = 0; i < page; i++) {
            int startIdx = i * pageSize;
            int endIdx = (i + 1) * pageSize;
            if (endIdx > size) {
                endIdx = size;
            }

            List<BizBatchInfo> updateBatchInfoList = new ArrayList<>(pageSize);
            List<Long> maraIdList = new ArrayList<>(pageSize);
            Set<Long> maraIdSet = new HashSet<>(pageSize);
            List<Long> ftyIdList = new ArrayList<>(pageSize);
            Set<Long> ftyIdSet = new HashSet<>(pageSize);
            Map<String, Integer> shelfLifeMap = new HashMap<>();
            List<DicMaterialFactoryDTO> maraFtyBatchSearchList = new ArrayList<>();
            for (int j = startIdx; j < endIdx; j++) {
                DicMaterialFactoryDTO dto = maraFtyBatchList.get(j);
                Long maraId = dto.getMatId();
                if (maraIdSet.add(maraId)) {
                    maraIdList.add(maraId);
                }
                Long ftyId = dto.getFtyId();
                if (ftyIdSet.add(ftyId)) {
                    ftyIdList.add(ftyId);
                }
                String key = ftyId + "-" + maraId;
                shelfLifeMap.put(key, dto.getShelfLifeMax());
                maraFtyBatchSearchList.add(dto);
            }

            // 查询物料工厂中所有做过寿期延期的批次，更新寿期到期日期将跳过这些批次
            List<Long> lifetimeBatchList = bizBatchInfoDataWrap.selectLifetimeBatchIdList(maraFtyBatchSearchList);

            QueryWrapper<BizBatchInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(BizBatchInfo::getFtyId, ftyIdList).in(BizBatchInfo::getMatId, maraIdList);
            List<BizBatchInfo> batchInfoList = bizBatchInfoDataWrap.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(batchInfoList)) {
                for (BizBatchInfo bizBatchInfo : batchInfoList) {
                    Date productionDate = bizBatchInfo.getProductionDate();
                    if (productionDate == null) {
                        continue;
                    }
                    String key = bizBatchInfo.getFtyId() + "-" + bizBatchInfo.getMatId();
                    Integer lifeMax = shelfLifeMap.get(key);
                    if (lifeMax == null) {
                        continue;
                    }
                    // 跳过做过寿期延期的批次
                    if (lifetimeBatchList.contains(bizBatchInfo.getId())) {
                        continue;
                    }
                    Date expireDate = UtilDate.plusMonths(productionDate, lifeMax);
                    updateBatchInfoList.add(new BizBatchInfo().setId(bizBatchInfo.getId()).setLifetimeDate(expireDate));
                }
            }
            bizBatchInfoDataWrap.updateBatchById(updateBatchInfoList);
        }
    }


    /**
     * 调用sap保存收发存库存
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject stocktakingPostStock(CurrentUser user,String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.stocktakingPostStockBySap(user,postingItem, erpReturnObj);
        } else {
            erpPostingService.stocktakingPostStockByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 调用sap传输附件
     *
     * @param postingItem 行项目json串
     * @return ErpReturnObject
     */
    public ErpReturnObject attSyn(CurrentUser user,String postingItem) {
        ErpReturnObject erpReturnObj = new ErpReturnObject();
        if (UtilString.isNullOrEmpty(postingItem)) {
            return erpReturnObj;
        }
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            this.attSynBySap( postingItem, erpReturnObj);
        } else {
            erpPostingService.attSynByNotSap(postingItem, erpReturnObj);
        }
        return erpReturnObj;
    }

    /**
     * 调用sap传输附件
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    private void attSynBySap( String postingItem, ErpReturnObject erpReturnObj) {
        JSONObject params=SapInterfaceOtherUtil.getAttSynRequestParams(postingItem);
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_ATT_SYN);
        log.debug("调用sap传输附件入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("调用sap传输附件出参：" + returnObject);
        if(returnObject!=null){
            JSONObject paramsOfReturn = returnObject.getJSONObject("I_RETURN");
            if(Const.ERP_RETURN_TYPE_S.equals(paramsOfReturn.getString("CODE"))) {

            }
            erpReturnObj.setSuccess(paramsOfReturn.getString("CODE"));
            erpReturnObj.setReturnMessage(paramsOfReturn.getString("MES"));
        }else {
            erpReturnObj.setSuccess(Const.ERP_RETURN_TYPE_E);
        }
    }

    /**
     * 调用sap保存收发存库存
     *
     * @param postingItem  行项目json串
     * @param erpReturnObj ERP返回对象
     */
    private void stocktakingPostStockBySap(CurrentUser user,String postingItem, ErpReturnObject erpReturnObj) {
        JSONObject jsonObject  = JSONObject.parseObject(postingItem, JSONObject.class);
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_TWO);
        params.put("callerLong", "1");
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IT_MATNR", new JSONArray());
        params.put("IV_WERKS", jsonObject.getString("ftyCode"));
        params.put("IV_SPMON", jsonObject.getString("postTime"));
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_JXCBB);
        log.debug("SAP收发存库存入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP收发存库存出参：" + params);
        List<BizReceiptStocktakingDocItemDTO> itemList = Lists.newArrayList();
        if(returnObject!=null){
            JSONObject paramsOfReturn = returnObject.getJSONObject("I_RETURN");
            if(Const.ERP_RETURN_TYPE_S.equals(paramsOfReturn.getString("CODE"))) {
                JSONArray paramsOfData = returnObject.getJSONArray("T_DATA");
                if(paramsOfData!=null){
                    paramsOfData.forEach(e -> {
                        JSONObject obj=(JSONObject)e;
                        BizReceiptStocktakingDocItemDTO item = new BizReceiptStocktakingDocItemDTO();
                        item.setId(null);
                        item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                        item.setCreateUserId(user.getId());
                        item.setModifyUserId(user.getId());
                        item.setCreateTime(UtilDate.getNow());
                        item.setModifyTime(UtilDate.getNow());
                        item.setHeadId(Long.valueOf(jsonObject.getString("id")));
                        item.setFtyCode(jsonObject.getString("ftyCode"));
                        item.setMatGroupCode(obj.getString("MATKL"));
                        item.setMatGroupName(obj.getString("WGBEZ"));
                        item.setMatCode(obj.getString("MATNR"));
                        item.setMatName(obj.getString("MAKTX"));
                        item.setUnitCode(obj.getString("MEINS"));
                        item.setUnitName(obj.getString("MSEHL"));
                        item.setQty(UtilObject.getBigDecimalOrZero(obj.getString("QMSL")));
                        item.setPrice(UtilObject.getBigDecimalOrZero(obj.getString("QMJE")));
                        itemList.add(item);
                    });
                }
            }
            erpReturnObj.setList(itemList);
            erpReturnObj.setSuccess(paramsOfReturn.getString("CODE"));
            erpReturnObj.setReturnMessage(paramsOfReturn.getString("MES"));
        }else {
            erpReturnObj.setSuccess(Const.ERP_RETURN_TYPE_E);
        }
    }

    /**
     * 记录sap模拟接口日志
     * @param url
     * @param params
     * @param returnObj
     */
    public  void saveSapInterfaceLog(String url,JSONObject params, JSONObject returnObj) {
        if(params!=null){
            params.remove("callerReceiptType");
            params.remove("callReceiptCode");
        }
    }


}
