<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.stock.dao.StockBatchMapper">
    <!--根据batchId 修改 qty_freeze-->
    <update id="updateByBatchId">
        UPDATE
            `stock_batch`
        SET
            `stock_batch`.`qty_freeze` =
                (SELECT
                     t1.`qty_freeze` + #{freezeNum}
                 FROM
                     (SELECT
                          `qty_freeze`
                      FROM
                          `stock_batch`
                      WHERE `batch_id` = #{batchId}) AS t1),
            `stock_batch`.`qty` =
                (SELECT
                     t2.`qty` - #{freezeNum}
                 FROM
                     (SELECT
                          `qty`
                      FROM
                          `stock_batch`
                      WHERE `batch_id` = #{batchId}) AS t2)
        WHERE `batch_id` = #{batchId}
    </update>

    <!-- 根据ins凭证查询所有相关批次库存 -->
    <select id="selectNegativeStockBatchAfterModifyStockBatch" resultType="com.inossem.wms.common.model.stock.dto.StockBatchDTO">
        SELECT a.id,
               a.mat_id,
               a.batch_id,
               a.fty_id,
               a.location_id,
               a.qty,
               a.qty_transfer,
               a.qty_inspection,
               a.qty_freeze,
               a.qty_haste,
               a.qty_temp,
               a.create_time,
               a.modify_time,
               a.create_user_id,
               a.modify_user_id
        FROM stock_batch a
                 INNER JOIN biz_batch_info bm ON a.id = bm.id
                 INNER JOIN stock_ins_doc_batch b
                            ON b.ins_doc_code = #{insDocCode}
                                AND a.mat_id = b.mat_id
                                AND a.fty_id = b.fty_id
                                AND a.location_id = b.location_id
                                AND bm.id = b.batch_id
                                AND (a.qty <![CDATA[<]]> 0
                                    OR a.qty_transfer <![CDATA[<]]> 0
                                    OR a.qty_inspection <![CDATA[<]]> 0
                                    OR a.qty_freeze <![CDATA[<]]> 0
                                    OR a.qty_haste + a.qty <![CDATA[<]]> 0
                                    OR a.qty_temp + a.qty <![CDATA[<]]> 0
                                    OR a.qty + a.qty_haste + a.qty_temp <![CDATA[<]]> 0)
    </select>

    <!-- 根据唯一索引查询所有相关批次库存 -->
    <select id="selectStockBatchByStockBatchKeyList" resultType="com.inossem.wms.common.model.stock.dto.StockBatchDTO">
        SELECT
        s.mat_id, s.batch_id, s.fty_id, s.location_id,
        s.qty, s.qty_transfer, s.qty_inspection,
        s.qty_freeze, s.qty_haste, s.qty_temp
        FROM stock_batch s
        INNER JOIN biz_batch_info bm ON s.batch_id = bm.id AND bm.is_delete = 0
        INNER JOIN
        <foreach collection="list" item="item" open="(" separator=" UNION ALL" close=")" index="index">
            SELECT #{item.ftyId} f,#{item.locationId} l,#{item.matId} m,#{item.batchId} b
        </foreach>
        k
        ON s.mat_id = k.m AND s.batch_id = k.b AND s.fty_id = k.f AND s.location_id = k.l
    </select>


    <!-- 根据唯一索引批量查询所有相关批次库存的主键 -->
    <select id="selectStockBatchIdByStockBatchKeys" resultType="java.lang.Long">
        SELECT s.id FROM stock_batch s
        INNER JOIN
        <foreach collection="list" item="item" open="(" separator=" UNION ALL" close=")" index="index">
            SELECT #{item.matId} mat_id, #{item.batchId} batch_id, #{item.ftyId} fty_id, #{item.locationId} loc_id
        </foreach>
        t
        ON s.mat_id = t.mat_id AND s.batch_id = t.batch_id AND s.fty_id = t.fty_id AND s.location_id = t.loc_id
    </select>

    <!-- 根据主键查询批次库存数量，并加X锁 -->
    <select id="selectStockBatchQtyByIdForUpdate" resultType="com.inossem.wms.common.model.stock.entity.StockBatch">
        SELECT
        s.id, s.mat_id, s.batch_id, s.fty_id, s.location_id,
        s.qty, s.qty_transfer, s.qty_inspection,
        s.qty_freeze, s.qty_haste, s.qty_temp
        FROM stock_batch s
        WHERE
        <foreach collection="idList" item="item" open="s.id in (" separator="," close=")">
            #{item}
        </foreach>
        FOR UPDATE
    </select>
    

    <!-- 批量 根据list和type自定义查询库存通用方法 -->
    <select id="selectStockBatchByPoListAndType" resultType="com.inossem.wms.common.model.stock.dto.StockBatchDTO">
        SELECT
        <!-- 库存库存主键 -->
        stock.fty_id,stock.location_id,stock.mat_id,stock.batch_id,
        <!-- 库存六数量 -->
        stock.qty,stock.qty_transfer,stock.qty_inspection,stock.qty_freeze,stock.qty_haste,stock.qty_temp,
        <!-- 库存信息 -->
        stock.create_time create_time,
        <!-- 工厂配置 -->
        fty.fty_name,
        <!-- 库存地点配置 -->
        location.location_name,
        <!-- 批次信息 -->
        stock.batch_id,bm.spec_stock,bm.spec_stock_code,bm.spec_stock_name,
        bm.batch_erp,bm.input_date input_date,
        bm.supplier_code,bm.supplier_name,bm.input_date,
        <!-- 物料信息 -->
        m.mat_name,m.mat_type_id,
        <!-- 单位信息 -->
        m.unit_id,u.unit_name,u.decimal_place
        FROM stock_batch stock
        INNER JOIN dic_material m ON m.id = stock.mat_id AND m.is_delete = 0
        <!-- type=1 根据工厂+物料查询库存 -->
        <if test="type != null and type == 1 ">
            AND (stock.fty_id,stock.mat_id) IN
            <foreach collection="list" open="(" close=")" separator="," item="item">
                (#{item.ftyId},#{item.matId})
            </foreach>
        </if>
        <!-- type=2 根据工厂+物料+库存地点查询库存 -->
        <if test="type != null and type == 2 ">
            AND (stock.fty_id,stock.mat_id,stock.location_id) IN
            <foreach collection="list" open="(" close=")" separator="," item="item">
                (#{item.ftyId},#{item.matId},#{item.locationId})
            </foreach>
        </if>
        INNER JOIN biz_batch_info bm ON stock.batch_id = bm.id AND bm.is_delete = 0
        INNER JOIN dic_unit u ON u.id = m.unit_id AND u.is_delete = 0
        INNER JOIN dic_factory fty ON fty.id = stock.fty_id AND fty.is_delete = 0
        INNER JOIN dic_stock_location location ON stock.fty_id = location.fty_id AND stock.location_id = location.id AND location.is_delete = 0
        ORDER BY stock.create_time
    </select>

    <!-- 查询库存通用方法 -->
    <select id="selectStockBatchByStockBatchPo" parameterType="com.inossem.wms.common.model.stock.po.StockBinPO" resultType="com.inossem.wms.common.model.stock.dto.StockBatchDTO">
        SELECT
        <!-- 库存库存主键 -->
        stock.fty_id,stock.location_id,stock.mat_id,stock.batch_id,
        <!-- 库存六数量 -->
        stock.qty,stock.qty_transfer,stock.qty_inspection,stock.qty_freeze,stock.qty_haste,stock.qty_temp,
        <!-- 库存信息 -->
        stock.create_time create_time,
        <!-- 工厂配置 -->
        fty.fty_name,
        <!-- 库存地点配置 -->
        location.location_name,
        <!-- 批次信息 -->
        stock.batch_id,bm.spec_stock,bm.spec_stock_code,bm.spec_stock_name,
        bm.batch_erp,bm.input_date input_date,
        bm.supplier_code,bm.supplier_name,bm.input_date,
        <!-- 物料信息 -->
        m.mat_name,m.mat_type_id,
        <!-- 单位信息 -->
        m.unit_id,u.unit_name,u.decimal_place
        FROM stock_batch stock
        INNER JOIN dic_material m ON m.id = stock.mat_id
        AND m.is_delete = 0
        <if test="ftyId != null">
            AND stock.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock.mat_id = #{matId}
        </if>
        <if test="stockStatusSet != null">
            <foreach collection="stockStatusSet" open="" separator="" close="" index="index" item="item">
                <choose>
                    <when test="item == 10">
                        AND stock.qty > 0
                    </when>
                    <when test="item == 20">
                        AND stock.qty_transfer > 0
                    </when>
                    <when test="item == 30">
                        AND stock.qty_inspection > 0
                    </when>
                    <when test="item == 40">
                        AND stock.qty_freeze > 0
                    </when>
                    <when test="item == 50">
                        AND stock.qty_haste > 0
                    </when>
                    <when test="item == 60">
                        AND stock.qty_temp > 0
                    </when>
                    <when test="item == 110">
                        AND stock.qty + stock.qty_haste > 0
                    </when>
                    <when test="item == 120">
                        AND stock.qty + stock.qty_temp > 0
                    </when>
                    <when test="item == 127">
                        AND stock.qty + stock.qty_haste + stock.qty_temp > 0
                    </when>
                    <otherwise>

                    </otherwise>
                </choose>
            </foreach>
        </if>
        INNER JOIN biz_batch_info bm ON stock.batch_id = bm.id AND bm.is_delete = 0
        <if test="batchId != null and batchId !=''">
            AND bm.id = #{batchId,jdbcType=VARCHAR}
        </if>
        <if test="specStock != null">
            AND bm.spec_stock = #{specStock,jdbcType=VARCHAR}
            <if test="specStockCode != null">
                AND bm.spec_stock_code = #{specStockCode,jdbcType=VARCHAR}
            </if>
        </if>
        <if test="batchErp != null">
            AND bm.batch_erp = #{batchErp}
        </if>
        INNER JOIN dic_unit u ON u.id = m.unit_id AND u.is_delete = 0
        INNER JOIN dic_factory fty ON fty.id = stock.fty_id AND fty.is_delete = 0
        INNER JOIN dic_stock_location location ON stock.fty_id = location.fty_id AND stock.location_id = location.id AND location.is_delete = 0
        ORDER BY stock.create_time
    </select>

    <!-- 批量  查询库存通用方法 -->
    <select id="selectStockBatchByStockBatchPoList" resultType="com.inossem.wms.common.model.stock.dto.StockBatchDTO">
        SELECT
        <!-- 库存库存主键 -->
        stock.fty_id,stock.location_id,stock.mat_id,stock.batch_id,
        <!-- 库存六数量 -->
        stock.qty,stock.qty_transfer,stock.qty_inspection,stock.qty_freeze,stock.qty_haste,stock.qty_temp,
        <!-- 库存信息 -->
        stock.create_time create_time,
        <!-- 工厂配置 -->
        fty.fty_name,
        <!-- 库存地点配置 -->
        location.location_name,
        <!-- 批次信息 -->
        stock.batch_id,bm.spec_stock,bm.spec_stock_code,bm.spec_stock_name,
        bm.batch_erp,bm.input_date input_date,
        bm.supplier_code,bm.supplier_name,bm.input_date,
        <!-- 物料信息 -->
        m.mat_name,m.mat_type_id,
        <!-- 单位信息 -->
        m.unit_id,u.unit_name,u.decimal_place
        FROM stock_batch stock
        INNER JOIN dic_material m ON m.id = stock.mat_id AND m.is_delete = 0
        <if test="list != null and list.size()>0">
            and
            <foreach collection="list" open="(" separator=" or " close=")" index="index" item="item">
                (1=1
                <if test="item.ftyId != null">
                    AND stock.fty_id = #{ftyId}
                </if>
                <if test="item.locationId != null">
                    AND stock.location_id = #{locationId}
                </if>
                <if test="item.matId != null">
                    AND stock.mat_id = #{matId}
                </if>
                <if test="item.batchId != null">
                    AND stock.batch_id = #{batchId,jdbcType=INTEGER}
                </if>
                <if test="item.stockStatusSet != null">
                    <foreach collection="item.stockStatusSet" open="" separator="" close="" index="index" item="item">
                        <choose>
                            <when test="item == 10">
                                AND stock.qty > 0
                            </when>
                            <when test="item == 20">
                                AND stock.qty_transfer > 0
                            </when>
                            <when test="item == 30">
                                AND stock.qty_inspection > 0
                            </when>
                            <when test="item == 40">
                                AND stock.qty_freeze > 0
                            </when>
                            <when test="item == 50">
                                AND stock.qty_haste > 0
                            </when>
                            <when test="item == 60">
                                AND stock.qty_temp > 0
                            </when>
                            <when test="item == 110">
                                AND stock.qty + stock.qty_haste > 0
                            </when>
                            <when test="item == 120">
                                AND stock.qty + stock.qty_temp > 0
                            </when>
                            <when test="item == 127">
                                AND stock.qty + stock.qty_haste + stock.qty_temp > 0
                            </when>
                            <otherwise>

                            </otherwise>
                        </choose>
                    </foreach>
                </if>
            </foreach>
        </if>
        INNER JOIN biz_batch_info bm ON stock.batch_id = bm.id AND bm.is_delete = 0
        <if test="batchId != null">
            AND stock.batch_id = #{batchId}
        </if>
        <if test="specStock != null">
            AND bm.spec_stock = #{specStock,jdbcType=VARCHAR}
            <if test="specStockCode != null">
                AND bm.spec_stock_code = #{specStockCode,jdbcType=VARCHAR}
            </if>
        </if>
        <if test="batchErp != null">
            AND bm.batch_erp = #{batchErp}
        </if>
        INNER JOIN dic_unit u ON u.id = m.unit_id AND u.is_delete = 0
        INNER JOIN dic_factory fty ON fty.id = stock.fty_id AND fty.is_delete = 0
        INNER JOIN dic_stock_location location ON stock.fty_id = location.fty_id AND stock.location_id = location.id AND location.is_delete = 0
        ORDER BY stock.create_time
    </select>

    <!-- 根据特性查询库存通用方法 -->
    <select id="getStockBySpecFeature" parameterType="com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO">
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        dic_wh_storage_type.is_default,
        biz_batch_info.spec_stock_code,
        biz_batch_info.is_danger,
        <if test="featureCode != null and featureCode != '' ">
            #{featureCode} spec_code,
        </if>
        <if test="featureCodeList != null and featureCodeList.size()>0">
            <foreach collection="featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
                when 20=#{stockStatus} then sum(stock_bin.qty_transfer)
                when 30=#{stockStatus} then sum(stock_bin.qty_inspection)
                when 40=#{stockStatus} then sum(stock_bin.qty_freeze)
                when 50=#{stockStatus} then sum(stock_bin.qty_haste)
                when 60=#{stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND (dic_wh_storage_type.is_default = 0
        <!--
          OR dic_wh_storage_type.type_code = '804'
          OR dic_wh_storage_type.type_code = '805'
          OR dic_wh_storage_type.type_code = '806'
          OR dic_wh_storage_type.type_code = '809'
         -->
            <if test="isUseTemporaryBin == 1">
                OR dic_wh_storage_type.type_code in ('800','801','802','803','804','805','806','807','808','809','811','812','813')
            </if>
            OR 20=#{stockStatus})
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
            AND stock_batch.fty_id = stock_bin.fty_id
            AND stock_batch.location_id = stock_bin.location_id
            AND stock_batch.mat_id = stock_bin.mat_id
        <if test="isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
             ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="specStock != null">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="assembleListList != null and assembleListList.size()>0">
            AND (
            <foreach collection="assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="stockStatus == 10">
                <if test="isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
            stock_bin.fty_id,
            stock_bin.location_id,
            stock_bin.mat_id
        <if test="featureCode != null and featureCode != ''">
            ,${featureCode}
        </if>
    </select>

    <!-- 根据特性查询工器具库存通用方法 -->
    <select id="getToolStockBySpecFeature" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO">
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        <if test="receiptType !=null and receiptType == '9000'">
            <!-- 工器具维保业务 -->
            biz_batch_info.maintenance_date,
            biz_batch_info.maintenance_cycle,
            <!-- 维保有效期 -->
            biz_batch_info.maintenance_valid_date,
            biz_batch_info.maintenance_program,
        </if>
        dic_wh_storage_type.is_default,
        <if test="featureCode != null and featureCode != '' ">
            #{featureCode} spec_code,
        </if>
        dic_wh_storage_bin.is_nuclear_island_tool_room,
        <if test="featureCodeList != null and featureCodeList.size()>0">
            <foreach collection="featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
                when 20=#{stockStatus} then sum(stock_bin.qty_transfer)
                when 30=#{stockStatus} then sum(stock_bin.qty_inspection)
                when 40=#{stockStatus} then sum(stock_bin.qty_freeze)
                when 50=#{stockStatus} then sum(stock_bin.qty_haste)
                when 60=#{stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND (dic_wh_storage_type.is_default = 0
        <if test="isUseTemporaryBin == 1">
            OR dic_wh_storage_type.type_code in ('800','801','802','803','804','805','806','807','808','809','811','812','813')
        </if>
        OR 20=#{stockStatus})
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        <if test="isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
            ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_material ON stock_bin.mat_id = dic_material.id AND dic_material.is_delete = 0
        INNER JOIN dic_wh_storage_bin ON stock_bin.bin_id = dic_wh_storage_bin.id AND dic_wh_storage_bin.is_delete = 0
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="matName != null and matName != ''">
            AND dic_material.mat_name LIKE concat( '%',#{matName}, '%')
        </if>
        <if test="binId != null">
            AND stock_bin.bin_id = #{binId}
        </if>
        <if test="batchId != null">
            AND biz_batch_info.id = #{batchId}
        </if>
        <if test="outFtyCode != null and outFtyCode != ''">
            AND biz_batch_info.out_fty_code = #{outFtyCode}
        </if>
        <if test="toolTypeId != null and toolTypeId != ''">
            AND biz_batch_info.tool_type_id = #{toolTypeId}
        </if>
        <if test="formatCode != null and formatCode != ''">
            AND biz_batch_info.format_code LIKE concat( '%',#{formatCode}, '%')
        </if>
        <if test="maintainInDate != null and maintainOutDate != null">
            AND DATE_ADD(biz_batch_info.maintenance_date,INTERVAL biz_batch_info.maintenance_cycle MONTH) BETWEEN
            #{maintainInDate, jdbcType=TIMESTAMP} AND #{maintainOutDate, jdbcType=TIMESTAMP}
        </if>
<!--        <if test="receiptType !=null and receiptType == '9000'">-->
<!--            &lt;!&ndash; 工器具维保业务，需要根据维保周期有效期作为条件给用户查询  &ndash;&gt;-->
<!--            &lt;!&ndash; 当前时间+预警有效期 大于等于维保有效期。说明此时需要给用户预警，并且维保有效期大于等于当前时间，说明还没有过维保有效期  &ndash;&gt;-->
<!--            AND DATE_ADD(NOW(),INTERVAL biz_batch_info.maintenance_warning_period DAY) <![CDATA[ >= ]]>-->
<!--            DATE_ADD(biz_batch_info.maintenance_date,INTERVAL biz_batch_info.maintenance_cycle MONTH)-->
<!--            AND DATE_ADD(biz_batch_info.maintenance_date,INTERVAL biz_batch_info.maintenance_cycle MONTH) <![CDATA[ >= ]]> NOW()-->
<!--        </if>-->
        <if test="specStock != null">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="isNuclearIslandToolRoom != null">
            AND dic_wh_storage_bin.is_nuclear_island_tool_room = #{isNuclearIslandToolRoom}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="assembleListList != null and assembleListList.size()>0">
            AND (
            <foreach collection="assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="stockStatus == 10">
                <if test="isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id
        <if test="featureCode != null and featureCode != ''">
            ,${featureCode}
        </if>
    </select>

    <!-- 根据特性查询库存通用方法（石岛湾） -->
    <select id="getStockBySpecFeatureBySdw" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO">
        SELECT
        t.*
        FROM(
        SELECT
        stock_bin.id,
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 start -->
        stock_bin.type_id,
        stock_bin.bin_id,
        stock_bin.type_id as type_id_temp,
        stock_bin.bin_id as bin_id_temp,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 end -->
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 start-->
        biz_batch_info.`purchase_receipt_code`,
        biz_batch_info.spec_stock,
        biz_batch_info.spec_stock_code,
        biz_batch_info.is_danger,
        biz_batch_info.spec_stock_name,
        biz_batch_info.demand_plan_code,
        biz_batch_info.demand_user_name,
        dic_material.ext_evaluation_classification as outputValType,
        dic_material.ext_evaluation_classification_desc as outputValTypeDes,
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 end-->
        <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30  属性绑定在批次信息上 故无需配置规则表 start -->
        <if test="po.receiptType != null  and po.receiptType == 812 ">

            biz_batch_info.batch_code,
            biz_batch_info.production_date,
            biz_batch_info.apply_user_code,
            biz_batch_info.apply_user_name,
            biz_batch_info.apply_user_dept_code,
            biz_batch_info.apply_user_dept_name,
            dic_material.shelf_life_max,
            DATEDIFF(
            #{po.now},
            DATE_ADD(DATE_FORMAT( biz_batch_info.production_date, '%Y%m%d' ), interval dic_material.shelf_life_max month)) AS days,
        </if>
        <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30 属性绑定在批次信息上 故无需配置规则表 end -->
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="po.receiptType != null  and po.receiptType == 810 ">
            biz_batch_info.batch_code,
            dic_material.package_type,
            dic_material.deposit_type,
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        dic_wh_storage_type.is_default,
        <!-- 暂存物资领用 暂存部门 start -->
        <if test="po.receiptType != null  and po.receiptType == 8171 ">
            biz_batch_info.temp_store_dept_id,
        </if>
        <!-- 暂存物资领用 暂存部门 end -->
        <if test="po.featureCode != null and po.featureCode != '' ">
            #{po.featureCode} spec_code,
        </if>
        <if test="po.featureCodeList != null and po.featureCodeList.size() > 0">
            <foreach collection="po.featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{po.stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
        when 20=#{po.stockStatus} then sum(stock_bin.qty_transfer)
        when 30=#{po.stockStatus} then sum(stock_bin.qty_inspection)
        when 40=#{po.stockStatus} then sum(stock_bin.qty_freeze)
        when 50=#{po.stockStatus} then sum(stock_bin.qty_haste)
        when 60=#{po.stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="po.isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND (dic_wh_storage_type.is_default = 0
        <if test="po.isUseTemporaryBin == 1">
            OR dic_wh_storage_type.type_code in ('800','801','802','803','804','805','806','807','808','809','811','812','813')
        </if>
        OR 20=#{po.stockStatus})
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        <if test="po.isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
            ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_material ON stock_bin.mat_id = dic_material.id
        INNER JOIN dic_material_group ON dic_material_group.id = dic_material.mat_group_id
        <!-- 领料申请特有的业务按照物料描述取模糊查询  2023-04-18修改为其他业务也进行物料描述模糊搜索-->
        <if test="po.matName != null and po.matName != ''">
            AND dic_material.mat_name LIKE concat( '%',#{po.matName}, '%')
        </if>
        <if test="po.receiveType != null">
            <if test="po.receiveType == 1 or po.receiveType == 2 or po.receiveType == 4">
                AND ( dic_material_group.mat_group_code != 'Y110' or  dic_material.mat_code like 'Y11042%')
            </if>
            <if test="po.receiveType == 3">
                AND dic_material_group.mat_group_code = 'Y110'
            </if>
        </if>
        <if test="po.ftyId != null">
            AND stock_bin.fty_id = #{po.ftyId}
        </if>
        <if test="po.locationId != null">
            AND stock_bin.location_id = #{po.locationId}
        </if>
        <if test="po.nqLocationId != null">
            AND stock_bin.location_id != #{po.nqLocationId}
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            and stock_bin.location_id in
            <foreach collection="po.locationIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="po.matId != null">
            AND stock_bin.mat_id = #{po.matId}
        </if>
        <if test="po.preMatCode != null and po.preMatCode != ''">
            AND dic_material.mat_code like concat(#{po.preMatCode}, '%')
        </if>
        <if test="po.extManufacturerPartNumber != null and po.extManufacturerPartNumber != ''">
            AND dic_material.ext_manufacturer_part_number like concat('%', #{po.extManufacturerPartNumber}, '%')
        </if>
        <if test="po.extMainMaterial != null and po.extMainMaterial != ''">
            AND dic_material.ext_main_material like concat('%', #{po.extMainMaterial}, '%')
        </if>
        <if test="po.extIndustryStandardDesc != null and po.extIndustryStandardDesc != ''">
            AND dic_material.ext_industry_standard_desc like concat('%', #{po.extIndustryStandardDesc}, '%')
        </if>
        <if test="po.matIdSet != null and po.matIdSet.size()>0">
            AND stock_bin.mat_id in
            <foreach collection="po.matIdSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.typeId != null">
            AND stock_bin.type_id = #{po.typeId}
        </if>
        <if test="po.binId != null">
            AND stock_bin.bin_id = #{po.binId}
        </if>
        <if test="po.specStock != null  ">
            AND biz_batch_info.spec_stock = #{po.specStock}
        </if>
        <if test="po.specStockCode != null">
            AND biz_batch_info.spec_stock_code = #{po.specStockCode}
        </if>
        <if test="po.batchCode != null and po.batchCode != '' ">
            AND biz_batch_info.batch_code = #{po.batchCode}
        </if>
        <if test="po.functionalLocationCode != null and po.functionalLocationCode != '' ">
            AND biz_batch_info.functional_location_code = #{po.functionalLocationCode}
        </if>
        <if test="po.extend29 != null and po.extend29 != '' ">
            AND biz_batch_info.extend29 = #{po.extend29}
        </if>
        <if test="po.extend20 != null and po.extend20 != '' ">
            AND biz_batch_info.extend20 = #{po.extend20}
        </if>
        <if test="po.extend28 != null and po.extend28 != '' ">
            AND biz_batch_info.extend28 = #{po.extend28}
        </if>
        <if test="po.batchId != null and po.batchId != '' ">
            AND biz_batch_info.id = #{po.batchId}
        </if>
        <if test="po.isUnitized != null and po.isUnitized == true">
            and dic_material.mat_code like 'CT%'
        </if>
        <if test="po.isUnitized == null or po.isUnitized == false ">
            and dic_material.mat_code not like 'CT%'
        </if>

       <if test="po.referReceiptCodePara != null and po.referReceiptCodePara != '' ">
           AND biz_batch_info.purchase_code = #{po.referReceiptCodePara}
       </if>
       <if test="po.referReceiptRidPara != null and po.referReceiptRidPara != '' ">
           AND biz_batch_info.purchase_rid = #{po.referReceiptRidPara}
       </if>    

   <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="po.maintenanceValidDateStart !=null and po.maintenanceValidDateEnd != null ">
            AND DATE_FORMAT(DATE_ADD(ifnull(biz_batch_info.maintenance_date, '2020-08-31'),
            INTERVAL
            case
            when 0=dic_material.package_type then 0
            when 1=dic_material.package_type then 5
            when 2=dic_material.package_type then 5
            when 3=dic_material.package_type then 2
            when 4=dic_material.package_type then 3
            when 5=dic_material.package_type then 1
            when 6=dic_material.package_type then 0
            when 7=dic_material.package_type then 5
            when 8=dic_material.package_type then 5
            end
            YEAR
            ),'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.maintenanceValidDateEnd},'%Y-%m-%d')
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        <if test="po.isLeisure != null">
            AND biz_batch_info.is_leisure = #{po.isLeisure}
        </if>
        <if test="po.tempStoreUser != null and po.tempStoreUser != ''">
            AND biz_batch_info.temp_store_user LIKE concat( '%',#{po.tempStoreUser}, '%')
        </if>
        <if test="po.tempStoreExpireDateStart != null and po.tempStoreExpireDateEnd != null ">
            AND biz_batch_info.temp_store_expire_date BETWEEN DATE_FORMAT(#{po.tempStoreExpireDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{po.tempStoreExpireDateEnd},'%Y-%m-%d')
        </if>
        <if test="po.tempStorePreReceiptCode != null and po.tempStorePreReceiptCode != '' ">
            AND biz_batch_info.temp_store_pre_receipt_code = #{po.tempStorePreReceiptCode}
        </if>
        <if test="po.assembleList != null and po.assembleList.size()>0">
            <foreach collection="po.assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="po.assembleListList != null and po.assembleListList.size()>0">
            AND (
            <foreach collection="po.assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="po.stockStatus == 10">
                <if test="po.isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="po.stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="po.stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="po.stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="po.stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="po.stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id
        <if test="po.featureCode != null and po.featureCode != ''">
            ,${po.featureCode}
        </if>
        )t
        <if test="po.receiptType != null and po.receiptType != '' and po.receiptType = 812 and po.now != null">
            WHERE 0 <![CDATA[<=]]> t.days AND t.days <![CDATA[<=]]> 30
        </if>
    </select>

    <!-- 根据唯一索引查询所有相关工器具批次库存 -->
    <select id="selectToolStockBatchByStockBatchKeyList" resultType="com.inossem.wms.common.model.stock.dto.StockBatchDTO">
        SELECT
        s.id,s.mat_id, s.batch_id, s.fty_id, s.location_id,
        s.qty, s.qty_transfer, s.qty_inspection,
        s.qty_freeze, s.qty_haste, s.qty_temp
        FROM stock_batch s
        INNER JOIN biz_batch_info bm ON s.batch_id = bm.id AND bm.is_delete = 0
        INNER JOIN
        <foreach collection="list" item="item" open="(" separator=" UNION ALL" close=")" index="index">
            SELECT #{item.ftyId} f,#{item.locationId} l,#{item.matId} m,#{item.batchId} b
        </foreach>
        k
        ON s.mat_id = k.m AND s.batch_id = k.b AND s.fty_id = k.f AND s.location_id = k.l
    </select>


    <!-- 根据特性列表查询库存通用方法 -->
    <select id="getOutputStockByBatchList" resultType="com.inossem.wms.common.model.stock.dto.StockBinDTO">
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        biz_batch_info.input_date,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        dic_wh_storage_type.is_default,
        sum(stock_bin.qty+stock_bin.qty_temp) - ifnull(sum(stock_occupy.qty),0) stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND dic_wh_storage_type.is_default = 0
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        LEFT JOIN
        (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
        ON stock_occupy.stock_bin_id = stock_bin.id
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN
        <foreach collection="list" item="item" open="(" separator=" UNION ALL" close=")" index="index">
            SELECT  #{item.ftyId} fty_id,
            #{item.locationId} location_id,
            #{item.matId} mat_id,
            ifNUll(#{item.specStockCode}, '') spec_stock_code,
            ifNUll(#{item.specStock}, '') spec_stock
        </foreach>  po
        ON stock_batch.mat_id = po.mat_id
        AND stock_batch.location_id = po.location_id
        AND stock_batch.fty_id = po.fty_id
        AND biz_batch_info.spec_stock_code = po.spec_stock_code
        AND biz_batch_info.spec_stock = po.spec_stock
        where
        stock_bin.qty > 0
        AND stock_bin.qty_temp = 0
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id,
        stock_bin.batch_id
    </select>


    <!-- 根据特性列表查询库存通用方法 -->
    <select id="getPlotStockByBatchList" resultType="com.inossem.wms.common.model.stock.dto.StockBinDTO">
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        biz_batch_info.input_date,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        dic_wh_storage_type.is_default,
        sum(stock_bin.qty+stock_bin.qty_temp) - ifnull(sum(stock_occupy.qty),0) stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND dic_wh_storage_type.is_default = 0
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        LEFT JOIN
        (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
        ON stock_occupy.stock_bin_id = stock_bin.id
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0

        INNER JOIN dic_material ON stock_bin.mat_id = dic_material.id
        <if test="isUnitized == true">
            and dic_material.mat_code like 'CT%'
        </if>
        <if test="isUnitized == false ">
            and dic_material.mat_code not like 'CT%'
        </if>
        INNER JOIN
        <foreach collection="list" item="item" open="(" separator=" UNION ALL" close=")" index="index">
            SELECT  #{item.ftyId} fty_id,
            #{item.locationId} location_id,
            #{item.matId} mat_id,
            ifNUll(#{item.specStock}, '') spec_stock
        </foreach>  po
        ON stock_batch.mat_id = po.mat_id
        AND stock_batch.location_id = po.location_id
        AND stock_batch.fty_id = po.fty_id
        AND biz_batch_info.spec_stock = po.spec_stock
        where
        stock_bin.qty > 0
        AND stock_bin.qty_temp = 0
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id,
        stock_bin.batch_id
    </select>

    <!-- 根据特性查询库存通用方法（石岛湾） -->
    <select id="getStockBySpecFeatureBySdw4Unitized" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO">
        SELECT
        t.*
        FROM(
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 start -->
        stock_bin.type_id,
        stock_bin.bin_id,
        stock_bin.type_id as type_id_temp,
        stock_bin.bin_id as bin_id_temp,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 end -->
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 start-->
        biz_batch_info.`purchase_receipt_code`,
        biz_batch_info.spec_stock,
        biz_batch_info.spec_stock_code,
        biz_batch_info.is_danger,
        biz_batch_info.spec_stock_name,
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 end-->
        <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30  属性绑定在批次信息上 故无需配置规则表 start -->
        <if test="receiptType != null  and receiptType == 812 ">

            biz_batch_info.batch_code,
            biz_batch_info.production_date,
            biz_batch_info.apply_user_code,
            biz_batch_info.apply_user_name,
            biz_batch_info.apply_user_dept_code,
            biz_batch_info.apply_user_dept_name,
            dic_material.shelf_life_max,
            DATEDIFF(
            #{now},
            DATE_ADD(DATE_FORMAT( biz_batch_info.production_date, '%Y%m%d' ), interval dic_material.shelf_life_max month)) AS days,
        </if>
        <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30 属性绑定在批次信息上 故无需配置规则表 end -->
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="receiptType != null  and receiptType == 810 ">
            biz_batch_info.batch_code,
            dic_material.package_type,
            dic_material.deposit_type,
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        dic_wh_storage_type.is_default,
        <if test="featureCode != null and featureCode != '' ">
            #{featureCode} spec_code,
        </if>
        <if test="featureCodeList != null and featureCodeList.size() > 0">
            <foreach collection="featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
        when 20=#{stockStatus} then sum(stock_bin.qty_transfer)
        when 30=#{stockStatus} then sum(stock_bin.qty_inspection)
        when 40=#{stockStatus} then sum(stock_bin.qty_freeze)
        when 50=#{stockStatus} then sum(stock_bin.qty_haste)
        when 60=#{stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND (dic_wh_storage_type.is_default = 0
        <if test="isUseTemporaryBin == 1">
            OR dic_wh_storage_type.type_code in ('800','801','802','803','804','805','806','807','808','809','811','812','813')
        </if>
        OR 20=#{stockStatus})
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        <if test="isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
            ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_material ON stock_bin.mat_id = dic_material.id
        <!-- 领料申请特有的业务按照物料描述取模糊查询-->
        <if test="matName != null and matName != '' and isApplyFlag != null  and isApplyFlag == 1 ">
            AND dic_material.mat_name LIKE concat( '%',#{matName}, '%')
        </if>
        <if test="mMatName != null and mMatName != '' and isApplyFlag != null  and isApplyFlag == 1 ">
            AND dic_material.mat_code LIKE 'CT%' AND dic_material.mat_name LIKE concat( '%',#{mMatName}, '%')
        </if>
        <if test="isUnitized != null and isUnitized == true">
            and dic_material.mat_code like 'CT%'
        </if>
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="matIdSet != null and matIdSet.size()>0">
            AND stock_bin.mat_id in
            <foreach collection="matIdSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND stock_bin.type_id = #{typeId}
        </if>
        <if test="binId != null">
            AND stock_bin.bin_id = #{binId}
        </if>
        <if test="specStock != null  ">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="specStockCode != null">
            AND biz_batch_info.spec_stock_code = #{specStockCode}
        </if>
        <if test="batchCode != null and batchCode != '' ">
            AND biz_batch_info.batch_code = #{batchCode}
        </if>
        <if test="batchId != null and batchId != '' ">
            AND biz_batch_info.id = #{batchId}
        </if>
        <if test="functionalLocationCode != null and functionalLocationCode != '' ">
            AND biz_batch_info.functional_location_code = #{functionalLocationCode}
        </if>
        <if test="functionalLocationCodeList !=null and functionalLocationCodeList.size() > 0 ">
            AND biz_batch_info.functional_location_code in
            <foreach collection="functionalLocationCodeList" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="extend29 != null and extend29 != '' ">
            AND biz_batch_info.extend29 = #{extend29}
        </if>
        <if test="extend20 != null and extend20 != '' ">
            AND biz_batch_info.extend20 = #{extend20}
        </if>
        <if test="extend20List !=null and extend20List.size() > 0 ">
            AND biz_batch_info.extend20 in
            <foreach collection="extend20List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="extend24 != null and extend24 != '' ">
            AND biz_batch_info.extend24 LIKE concat( '%',#{extend24}, '%')
        </if>
        <if test="extend24List !=null and extend24List.size() > 0 ">
            AND biz_batch_info.extend24 in
            <foreach collection="extend24List" open="(" close=")" index="index" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="isExpired != null and isExpired == 1 and now != null">
            AND biz_batch_info.lifetime_date &lt; #{now}
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="maintenanceValidDateStart !=null and maintenanceValidDateEnd != null ">
            AND DATE_FORMAT(DATE_ADD(ifnull(biz_batch_info.maintenance_date, '2020-08-31'),
            INTERVAL
            case
            when 0=dic_material.package_type then 0
            when 1=dic_material.package_type then 5
            when 2=dic_material.package_type then 5
            when 3=dic_material.package_type then 2
            when 4=dic_material.package_type then 3
            when 5=dic_material.package_type then 1
            when 6=dic_material.package_type then 0
            when 7=dic_material.package_type then 5
            when 8=dic_material.package_type then 5
            end
            YEAR
            ),'%Y-%m-%d') BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        <if test="isLeisure != null">
            AND biz_batch_info.is_leisure = #{isLeisure}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="assembleListList != null and assembleListList.size()>0">
            AND (
            <foreach collection="assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="stockStatus == 10">
                <if test="isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id
        <if test="featureCode != null and featureCode != ''">
            ,${featureCode}
        </if>
         limit 500)t
        <if test="receiptType != null and receiptType != '' and receiptType == 812 and now != null">
            WHERE 0 <![CDATA[<=]]> t.days AND t.days <![CDATA[<=]]> 30
        </if>
    </select>

    <!-- 根据特性查询库存通用方法（石岛湾） -->
    <select id="getStockByPkgTypeBySdw" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleLifetimeDTO">
        SELECT
        t.*
        FROM(
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 start -->
        stock_bin.type_id,
        stock_bin.bin_id,
        stock_bin.type_id as type_id_temp,
        stock_bin.bin_id as bin_id_temp,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 end -->
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 start-->
        biz_batch_info.`purchase_receipt_code`,
        biz_batch_info.spec_stock,
        biz_batch_info.spec_stock_code,
        biz_batch_info.is_danger,
        biz_batch_info.spec_stock_name,
        biz_batch_info.lifetime_date as expire_date,
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 end-->

            <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30  属性绑定在批次信息上 故无需配置规则表 start -->
            <if test="receiptType != null  and receiptType == 812 ">
                biz_batch_info.batch_code,
                biz_batch_info.production_date,
                biz_batch_info.apply_user_code,
                biz_batch_info.apply_user_name,
                biz_batch_info.apply_user_dept_code,
                biz_batch_info.apply_user_dept_name,
                dmf.shelf_life_max,
            </if>
            <if test="receiptType != null  and   receiptType == 822">
                biz_batch_info.batch_code,
                biz_batch_info.production_date,
                biz_batch_info.apply_user_code,
                biz_batch_info.apply_user_name,
                biz_batch_info.apply_user_dept_code,
                biz_batch_info.apply_user_dept_name,
                dic_material.shelf_life_max,
            </if>
            <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30 属性绑定在批次信息上 故无需配置规则表 end -->
        dic_wh_storage_type.is_default,
        <if test="featureCode != null and featureCode != '' ">
            #{featureCode} spec_code,
        </if>
        <if test="featureCodeList != null and featureCodeList.size() > 0">
            <foreach collection="featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
        when 20=#{stockStatus} then sum(stock_bin.qty_transfer)
        when 30=#{stockStatus} then sum(stock_bin.qty_inspection)
        when 40=#{stockStatus} then sum(stock_bin.qty_freeze)
        when 50=#{stockStatus} then sum(stock_bin.qty_haste)
        when 60=#{stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND (dic_wh_storage_type.is_default = 0
        <if test="isUseTemporaryBin == 1">
            OR dic_wh_storage_type.type_code in ('800','801','802','803','804','805','806','807','808','809','811','812','813')
        </if>
        OR 20=#{stockStatus})
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        <if test="isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
            ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_material ON dic_material.id = stock_bin.mat_id
        <if test="isUnitized == true">
            and dic_material.mat_code like 'CT%'
        </if>
        <if test="isUnitized == false ">
            and dic_material.mat_code not like 'CT%'
        </if>

        left JOIN dic_material_factory dmf ON stock_bin.mat_id = dmf.mat_id and stock_bin.fty_id=dmf.fty_id
            where 1=1
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="matIdSet != null and matIdSet.size()>0">
            AND stock_bin.mat_id in
            <foreach collection="matIdSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND stock_bin.type_id = #{typeId}
        </if>
        <if test="binId != null">
            AND stock_bin.bin_id = #{binId}
        </if>
        <if test="specStock != null  ">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="specStockCode != null">
            AND biz_batch_info.spec_stock_code = #{specStockCode}
        </if>
        <if test="batchCode != null and batchCode != '' ">
            AND biz_batch_info.batch_code = #{batchCode}
        </if>
        <if test="batchId != null and batchId != '' ">
            AND biz_batch_info.id = #{batchId}
        </if>
        <if test="isLeisure != null">
            AND biz_batch_info.is_leisure = #{isLeisure}
        </if>
        <if test="maintenanceValidDateStart !=null and maintenanceValidDateEnd != null ">
            AND biz_batch_info.lifetime_date <![CDATA[>=]]> #{maintenanceValidDateStart} AND biz_batch_info.lifetime_date <![CDATA[<=]]> #{maintenanceValidDateEnd}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="assembleListList != null and assembleListList.size()>0">
            AND (
            <foreach collection="assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="stockStatus == 10">
                <if test="isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id
        <if test="featureCode != null and featureCode != ''">
            ,${featureCode}
        </if>
        )t
    </select>

    <select id="getMaintainStockBySdw" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleMaintainDTO">
        SELECT
        t.*
        FROM(
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 start -->
        stock_bin.type_id,
        stock_bin.bin_id,
        stock_bin.type_id as type_id_temp,
        stock_bin.bin_id as bin_id_temp,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 end -->
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 start-->
        biz_batch_info.`purchase_receipt_code`,
        biz_batch_info.spec_stock,
        biz_batch_info.spec_stock_code,
        biz_batch_info.is_danger,
        biz_batch_info.spec_stock_name,
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 end-->
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="receiptType != null  and receiptType == 810 ">
            biz_batch_info.batch_code,
            dmf.package_type,
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        dic_wh_storage_type.is_default,
        <if test="featureCode != null and featureCode != '' ">
            #{featureCode} spec_code,
        </if>
        <if test="featureCodeList != null and featureCodeList.size() > 0">
            <foreach collection="featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
        when 20=#{stockStatus} then sum(stock_bin.qty_transfer)
        when 30=#{stockStatus} then sum(stock_bin.qty_inspection)
        when 40=#{stockStatus} then sum(stock_bin.qty_freeze)
        when 50=#{stockStatus} then sum(stock_bin.qty_haste)
        when 60=#{stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND (dic_wh_storage_type.is_default = 0
        <if test="isUseTemporaryBin == 1">
            OR dic_wh_storage_type.type_code in ('800','801','802','803','804','805','806','807','808','809','811','812','813')
        </if>
        OR 20=#{stockStatus})
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        <if test="isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
            ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        <if test="maintainProFlag != null and maintenanceValidDateStart != null and maintenanceValidDateEnd != null ">
            AND (
                exists (
                    select 1
                    from biz_batch_info_maintain
                    where stock_bin.batch_id = biz_batch_info_maintain.batch_id
                    and biz_batch_info_maintain.maintenance_date BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')
                )
                OR biz_batch_info.maintenance_date_pro BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')
            )
        </if>
        INNER JOIN dic_wh_storage_bin ON stock_bin.bin_id = dic_wh_storage_bin.id
        INNER JOIN dic_material ON dic_material.id = stock_bin.mat_id
        <if test="isUnitized == true">
            and dic_material.mat_code like 'CT%'
        </if>
        <if test="isUnitized == false ">
            and dic_material.mat_code not like 'CT%'
        </if>
        INNER JOIN dic_material_factory dmf ON stock_bin.mat_id = dmf.mat_id and stock_bin.fty_id=dmf.fty_id
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="matIdSet != null and matIdSet.size()>0">
            AND stock_bin.mat_id in
            <foreach collection="matIdSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND stock_bin.type_id = #{typeId}
        </if>
        <if test="binId != null">
            AND stock_bin.bin_id = #{binId}
        </if>
        <if test="specStock != null  ">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="specStockCode != null">
            AND biz_batch_info.spec_stock_code = #{specStockCode}
        </if>
        <if test="batchCode != null and batchCode != '' ">
            AND biz_batch_info.batch_code = #{batchCode}
        </if>
        <if test="batchId != null and batchId != '' ">
            AND biz_batch_info.id = #{batchId}
        </if>
    <!-- 维保特有业务 日常维保计算维护有效期 start -->

    <!-- TODO BY YU 取运单信息中AW (biz_receipt_waybill.extend61) 列作为维保周期
    <if test="isUnitized == true">
            INNER JOIN biz_receipt_waybill brw on brw.mat_id = stock_bin.mat_id
        </if>
    -->
<!--        <if test="maintainProFlag ==null and maintenanceValidDateStart !=null and maintenanceValidDateEnd != null ">-->
<!--            AND DATE_FORMAT(DATE_ADD(ifnull(biz_batch_info.maintenance_date, '2020-08-31'),-->
<!--            INTERVAL-->
<!--            case-->
<!--            when 0=dmf.package_type then 0-->
<!--            when 1=dmf.package_type then 5-->
<!--            when 2=dmf.package_type then 5-->
<!--            when 3=dmf.package_type then 2-->
<!--            when 4=dmf.package_type then 3-->
<!--            when 5=dmf.package_type then 1-->
<!--            when 6=dmf.package_type then 0-->
<!--            when 7=dmf.package_type then 5-->
<!--            when 8=dmf.package_type then 5-->
<!--            end-->
<!--            YEAR-->
<!--            ),'%Y-%m-%d') BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')-->
<!--        </if>-->
<!--        <if test="maintainProFlag !=null and maintenanceValidDateStart !=null and maintenanceValidDateEnd != null ">-->
<!--            AND biz_batch_info.maintenance_date_pro BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')-->
<!--        </if>-->
        <if test="maintainProFlag !=null">
            AND dmf.maintain_pro_flag = #{maintainProFlag}
        </if>
        <if test="maintenanceType != null and maintenanceType == 1">
            AND dmf.package_type not in (0,6)
        </if>

    <!-- 维保特有业务 日常维保计算维护有效期 end -->
        <if test="isLeisure != null">
            AND biz_batch_info.is_leisure = #{isLeisure}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="assembleListList != null and assembleListList.size()>0">
            AND (
            <foreach collection="assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="stockStatus == 10">
                <if test="isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id
        <if test="featureCode != null and featureCode != ''">
            ,${featureCode}
        </if>
        )t
    </select>


    <select id="getMaintainStockBySdwUnitized" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleMaintainDTO">
        SELECT
        t.*
        FROM(
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 start -->
        stock_bin.type_id,
        stock_bin.bin_id,
        stock_bin.type_id as type_id_temp,
        stock_bin.bin_id as bin_id_temp,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 end -->
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 start-->
        biz_batch_info.`purchase_receipt_code`,
        biz_batch_info.spec_stock,
        biz_batch_info.spec_stock_code,
        biz_batch_info.is_danger,
        biz_batch_info.spec_stock_name,
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 end-->
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="receiptType != null and receiptType == 819 ">
            biz_batch_info.batch_code,
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        dic_wh_storage_type.is_default,
        <if test="featureCode != null and featureCode != '' ">
            #{featureCode} spec_code,
        </if>
        <if test="featureCodeList != null and featureCodeList.size() > 0">
            <foreach collection="featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
        when 20=#{stockStatus} then sum(stock_bin.qty_transfer)
        when 30=#{stockStatus} then sum(stock_bin.qty_inspection)
        when 40=#{stockStatus} then sum(stock_bin.qty_freeze)
        when 50=#{stockStatus} then sum(stock_bin.qty_haste)
        when 60=#{stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        AND (dic_wh_storage_type.is_default = 0
        <if test="isUseTemporaryBin == 1">
            OR dic_wh_storage_type.type_code in ('800','801','802','803','804','805','806','807','808','809','811','812','813')
        </if>
        OR 20=#{stockStatus})
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        <if test="isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
            ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_wh_storage_bin ON stock_bin.bin_id = dic_wh_storage_bin.id
        INNER JOIN dic_material ON dic_material.id = stock_bin.mat_id
        <if test="isUnitized != null and isUnitized == true">
            and dic_material.mat_code like 'CT%'
        </if>
        <if test="isUnitized != null and isUnitized == false ">
            and dic_material.mat_code not like 'CT%'
        </if>
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="matIdSet != null and matIdSet.size()>0">
            AND stock_bin.mat_id in
            <foreach collection="matIdSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND stock_bin.type_id = #{typeId}
        </if>
        <if test="binId != null">
            AND stock_bin.bin_id = #{binId}
        </if>
        <if test="specStock != null  ">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="specStockCode != null">
            AND biz_batch_info.spec_stock_code = #{specStockCode}
        </if>
        <if test="batchCode != null and batchCode != '' ">
            AND biz_batch_info.batch_code = #{batchCode}
        </if>
        <if test="batchId != null and batchId != '' ">
            AND biz_batch_info.id = #{batchId}
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="maintainProFlag ==null and maintenanceValidDateStart !=null and maintenanceValidDateEnd != null ">
            AND DATE_FORMAT(DATE_ADD(ifnull(biz_batch_info.maintenance_date, '2020-08-31'),
            INTERVAL
            ifnull(biz_batch_info.extend61,0)
            YEAR
            ),'%Y-%m-%d') BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')
        </if>
        <if test="maintainProFlag !=null and maintenanceValidDateStart !=null and maintenanceValidDateEnd != null ">
            AND biz_batch_info.maintenance_date_pro BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        <if test="isLeisure != null">
            AND biz_batch_info.is_leisure = #{isLeisure}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="assembleListList != null and assembleListList.size()>0">
            AND (
            <foreach collection="assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="stockStatus == 10">
                <if test="isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id
        <if test="featureCode != null and featureCode != ''">
            ,${featureCode}
        </if>
        )t
    </select>



    <!-- 根据特性查询库存通用方法  复制getStockBySpecFeatureBySdw 去掉存储类型的判断   -->
    <select id="getStockBySpecFeatureBySdwAll" resultType="com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO">
        SELECT
        t.*
        FROM(
        SELECT
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.wh_id,
        stock_bin.batch_id,
        stock_bin.mat_id,
        stock_bin.create_user_id,
        stock_bin.create_time,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 start -->
        stock_bin.type_id,
        stock_bin.bin_id,
        stock_bin.type_id as type_id_temp,
        stock_bin.bin_id as bin_id_temp,
        <!-- 规则表featureCode配置了typeId和binId时生效 其余情况数据不准 不可使用 end -->
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 start-->
        biz_batch_info.`purchase_receipt_code`,
        biz_batch_info.spec_stock,
        biz_batch_info.spec_stock_code,
        biz_batch_info.is_danger,
        biz_batch_info.spec_stock_name,
        <!-- 项目特有wbs业务 属性绑定在批次信息上 故无需配置规则表 end-->
        <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30  属性绑定在批次信息上 故无需配置规则表 start -->
        <if test="receiptType != null  and receiptType == 812 ">

            biz_batch_info.batch_code,
            biz_batch_info.production_date,
            biz_batch_info.apply_user_code,
            biz_batch_info.apply_user_name,
            biz_batch_info.apply_user_dept_code,
            biz_batch_info.apply_user_dept_name,
            dic_material.shelf_life_max,
            DATEDIFF(
            #{now},
            DATE_ADD(DATE_FORMAT( biz_batch_info.production_date, '%Y%m%d' ), interval dic_material.shelf_life_max month)) AS days,
        </if>
        <!-- 寿期特有业务 0 ≤ 选择的日期-(生产日期+寿期)得出的天数 ≤ 30 属性绑定在批次信息上 故无需配置规则表 end -->
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="receiptType != null  and receiptType == 810 ">
            biz_batch_info.batch_code,
            dic_material.package_type,
            dic_material.deposit_type,
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        dic_wh_storage_type.is_default,
        <if test="featureCode != null and featureCode != '' ">
            #{featureCode} spec_code,
        </if>
        <if test="featureCodeList != null and featureCodeList.size() > 0">
            <foreach collection="featureCodeList" open="concat (" separator=",','," close=") spec_value, " item="item">
                ${item}
            </foreach>
        </if>
        case    when 10=#{stockStatus} then sum(stock_bin.qty+stock_bin.qty_temp)
        when 20=#{stockStatus} then sum(stock_bin.qty_transfer)
        when 30=#{stockStatus} then sum(stock_bin.qty_inspection)
        when 40=#{stockStatus} then sum(stock_bin.qty_freeze)
        when 50=#{stockStatus} then sum(stock_bin.qty_haste)
        when 60=#{stockStatus} then sum(stock_bin.qty_temp)
        end
        <if test="isOccupy">
            - ifnull(sum(stock_occupy.qty),0)
        </if>
        stock_qty
        FROM stock_bin
        INNER JOIN dic_wh_storage_type on stock_bin.type_id = dic_wh_storage_type.id
        AND dic_wh_storage_type.is_delete = 0
        INNER JOIN stock_batch ON stock_batch.batch_id = stock_bin.batch_id
        AND stock_batch.fty_id = stock_bin.fty_id
        AND stock_batch.location_id = stock_bin.location_id
        AND stock_batch.mat_id = stock_bin.mat_id
        <if test="isOccupy">
            LEFT JOIN
            (select stock_bin_id, sum(qty) qty from stock_occupy group by stock_bin_id) stock_occupy
            ON stock_occupy.stock_bin_id = stock_bin.id
        </if>
        INNER JOIN biz_batch_info ON stock_bin.batch_id = biz_batch_info.id AND biz_batch_info.is_delete = 0
        INNER JOIN dic_material ON stock_bin.mat_id = dic_material.id
        <!-- 领料申请特有的业务按照物料描述取模糊查询  2023-04-18修改为其他业务也进行物料描述模糊搜索-->
        <if test="matName != null and matName != ''">
            AND dic_material.mat_name LIKE concat( '%',#{matName}, '%')
        </if>
        <if test="isUnitized == true">
            and dic_material.mat_code like 'CT%'
        </if>
        <if test="isUnitized == false ">
            and dic_material.mat_code not like 'CT%'
        </if>
        <if test="ftyId != null">
            AND stock_bin.fty_id = #{ftyId}
        </if>
        <if test="locationId != null">
            AND stock_bin.location_id = #{locationId}
        </if>
        <if test="matId != null">
            AND stock_bin.mat_id = #{matId}
        </if>
        <if test="matIdSet != null and matIdSet.size()>0">
            AND stock_bin.mat_id in
            <foreach collection="matIdSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeId != null">
            AND stock_bin.type_id = #{typeId}
        </if>
        <if test="binId != null">
            AND stock_bin.bin_id = #{binId}
        </if>
        <if test="specStock != null  ">
            AND biz_batch_info.spec_stock = #{specStock}
        </if>
        <if test="specStockCode != null">
            AND biz_batch_info.spec_stock_code = #{specStockCode}
        </if>
        <if test="batchCode != null and batchCode != '' ">
            AND biz_batch_info.batch_code = #{batchCode}
        </if>
        <if test="batchId != null and batchId != '' ">
            AND biz_batch_info.id = #{batchId}
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 start -->
        <if test="maintenanceValidDateStart !=null and maintenanceValidDateEnd != null ">
            AND DATE_FORMAT(DATE_ADD(ifnull(biz_batch_info.maintenance_date, '2020-08-31'),
            INTERVAL
            case
            when 0=dic_material.package_type then 0
            when 1=dic_material.package_type then 5
            when 2=dic_material.package_type then 5
            when 3=dic_material.package_type then 2
            when 4=dic_material.package_type then 3
            when 5=dic_material.package_type then 1
            when 6=dic_material.package_type then 0
            when 7=dic_material.package_type then 5
            when 8=dic_material.package_type then 5
            end
            YEAR
            ),'%Y-%m-%d') BETWEEN DATE_FORMAT(#{maintenanceValidDateStart},'%Y-%m-%d') AND DATE_FORMAT(#{maintenanceValidDateEnd},'%Y-%m-%d')
        </if>
        <!-- 维保特有业务 日常维保计算维护有效期 end -->
        <if test="isLeisure != null">
            AND biz_batch_info.is_leisure = #{isLeisure}
        </if>
        <if test="assembleList != null and assembleList.size()>0">
            <foreach collection="assembleList" item="item">
                AND ${item.specCode} = #{item.specValue}
            </foreach>
        </if>
        <if test="assembleListList != null and assembleListList.size()>0">
            AND (
            <foreach collection="assembleListList" item="assembleList">
                (
                <foreach collection="assembleList" item="item">
                    ${item.specCode} = #{item.specValue} AND
                </foreach>
                1 = 1
                ) OR
            </foreach>
            1 = 0
            )
        </if>
        <choose>
            <when test="stockStatus == 10">
                <if test="isUseTemporaryBin == 0">
                    AND stock_bin.qty > 0
                </if>
                AND stock_bin.qty_temp = 0
            </when>
            <when test="stockStatus == 20">
                AND stock_bin.qty_transfer > 0
            </when>
            <when test="stockStatus == 30">
                AND stock_bin.qty_inspection > 0
            </when>
            <when test="stockStatus == 40">
                AND stock_bin.qty_freeze > 0
            </when>
            <when test="stockStatus == 50">
                AND stock_bin.qty_haste > 0
            </when>
            <when test="stockStatus == 60">
                AND stock_bin.qty_temp > 0
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by
        stock_bin.fty_id,
        stock_bin.location_id,
        stock_bin.mat_id
        <if test="featureCode != null and featureCode != ''">
            ,${featureCode}
        </if>
        )t
        <if test="receiptType != null and receiptType != '' and receiptType = 812 and now != null">
            WHERE 0 <![CDATA[<=]]> t.days AND t.days <![CDATA[<=]]> 30
        </if>
    </select>
    <select id="selectCtMatStockBatchItemCount" resultType="int">
        select count(sb.id)
        from stock_batch sb
                 left join dic_material dm on sb.mat_id = dm.id
        where dm.is_ct_code = 1

    </select>

    <select id="selectCtMatStockBatchReserveCount" resultType="int">
        select sum(id)
        from (select count(dm.id) id
              from dic_material dm
                       inner join stock_batch sb on sb.mat_id = dm.id
                       inner join stock_occupy so on sb.id = so.stock_batch_id
              where dm.is_ct_code = 1
              group by dm.id, sb.batch_id) t

    </select>

    <select id="selectCtMatStockBatchFreezeCount" resultType="int">
        select sum(id)
        from (select count(dm.id) id
              from dic_material dm
                       inner join stock_batch sb on sb.mat_id = dm.id
              where dm.is_ct_code = 1
                And sb.qty_freeze > 0
              group by dm.id, sb.batch_id) t

    </select>

    <select id="getStockByPurchase" resultType="com.inossem.wms.common.model.stock.dto.StockBatchDTO">
        select bi.purchase_code,bi.purchase_rid, sum(sbb.qty) qty
        from stock_batch sb
        inner join stock_bin sbb on sb.batch_id = sbb.batch_id and sb.mat_id = sbb.mat_id and sb.fty_id = sbb.fty_id and sb.location_id = sbb.location_id
        inner join dic_wh_storage_type dwsb on sbb.type_id = dwsb.id
        inner join biz_batch_info bi on sb.batch_id = bi.id
        where dwsb.is_default = 0 and  (bi.purchase_code, bi.purchase_rid) in
        <foreach collection="itemDTOList" item="item" open="(" separator="," close=")">
            (#{item.receiptCode}, #{item.rid})
        </foreach>
        group by bi.purchase_code, bi.purchase_rid
    </select>
</mapper>
