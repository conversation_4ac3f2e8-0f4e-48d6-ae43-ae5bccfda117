package com.inossem.wms.bizdomain.transport.service.datawrap;

import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.vo.BizReceiptTransportExportVO;
import org.springframework.stereotype.Service;

import com.inossem.wms.bizdomain.transport.dao.BizReceiptTransportItemMapper;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;

import java.util.List;

/**
 * <p>
 * 转储单明细表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
@Service
public class BizReceiptTransportItemDataWrap extends BaseDataWrap<BizReceiptTransportItemMapper, BizReceiptTransportItem> {

    public void deleteByHeadId(Long headId) {
        this.getBaseMapper().deleteByHeadId(headId);
    }

    /**
     * 查询导出数据
     */
    public List<BizReceiptTransportExportVO> selectExportItemList(BizReceiptTransportHeadSearchPO po) {
        return this.baseMapper.selectExportItemList(po);
    }
}
