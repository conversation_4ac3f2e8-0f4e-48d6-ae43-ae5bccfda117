package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.lifetime.service.biz.LifetimeAppraisalService;
import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedLifetimeAppraisalService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.lifetime.dto.BizReceiptLifetimeHeadDTO;
import com.inossem.wms.common.model.bizdomain.lifetime.po.BizReceiptLifetimeExportPO;
import com.inossem.wms.common.model.bizdomain.lifetime.po.BizReceiptLifetimeSearchPO;
import com.inossem.wms.common.model.bizdomain.lifetime.po.BizReceiptLifetimeSearchStockPO;
import com.inossem.wms.common.model.bizdomain.lifetime.vo.BizReceiptLifetimePageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 寿期检定 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@RestController
public class UnitizedLifetimeAppraisalController {

    @Autowired
    protected UnitizedLifetimeAppraisalService lifetimeAppraisalService;

    /**
     * 寿期检定-初始化
     *
     * @return 寿期检定单
     */
    @ApiOperation(value = "采购退货申请-初始化", tags = {"寿期管理-寿期检定"})
    @GetMapping(value = "/unitized/lifetime/lifetime-appraisal/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptLifetimeHeadDTO>> init(BizContext ctx) {
        lifetimeAppraisalService.init(ctx);
        BizResultVO<BizReceiptLifetimeHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询寿期检定列表-分页
     *
     * @param po 寿期分页查询入参
     * @return 寿期检定单列表
     */
    @ApiOperation(value = "查询寿期检定列表-分页", tags = {"寿期管理-寿期检定"})
    @PostMapping(value = "/unitized/lifetime/lifetime-appraisal/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptLifetimePageVO>> getPage(@RequestBody BizReceiptLifetimeSearchPO po, BizContext ctx) {
        lifetimeAppraisalService.getPage(ctx);
        PageObjectVO<BizReceiptLifetimePageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询寿期检定单详情
     *
     * @param id 寿期检定单抬头表主键
     * @return 寿期检定单详情
     */
    @ApiOperation(value = "查询寿期检定单详情", tags = {"寿期管理-寿期检定"})
    @GetMapping(value = "/unitized/lifetime/lifetime-appraisal/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptLifetimeHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        lifetimeAppraisalService.getInfo(ctx);
        BizResultVO<BizReceiptLifetimeHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 寿期检定-保存
     *
     * @param po 保存寿期检定表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "寿期检定-保存", tags = {"寿期管理-寿期检定"})
    @PostMapping(value = "/unitized/lifetime/lifetime-appraisal/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptLifetimeHeadDTO po, BizContext ctx) {
        lifetimeAppraisalService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 寿期检定-提交
     *
     * @param po 提交寿期检定表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "寿期检定-提交", tags = {"寿期管理-寿期检定"})
    @PostMapping(value = "/unitized/lifetime/lifetime-appraisal/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptLifetimeHeadDTO po, BizContext ctx) {
        lifetimeAppraisalService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 寿期检定-删除
     *
     * @param id 寿期检定单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "寿期检定-删除", tags = {"寿期管理-寿期检定"})
    @DeleteMapping(value = "/unitized/lifetime/lifetime-appraisal/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        lifetimeAppraisalService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_DELETE_SUCCESS, receiptCode);
    }

    /**
     * 查询物料批次库存
     *
     * @param po 寿期查询物料批次库存入参
     * @return 物料批次库存
     */
    @ApiOperation(value = "查询物料批次库存", tags = {"寿期管理-寿期检定"})
    @PostMapping(value = "/unitized/lifetime/lifetime-appraisal/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptLifetimeSearchStockPO po, BizContext ctx) {
        lifetimeAppraisalService.getMatStock(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    /**
     * 查询物料批次库存
     *
     * @param po 寿期查询物料批次库存入参
     * @return 物料批次库存
     */
    @ApiOperation(value = "寿期行项目导出", tags = {"寿期管理-寿期行项目导出"})
    @PostMapping(value = "/unitized/lifetime/lifetime-appraisal/export")
    public BaseResult export(@RequestBody List<BizReceiptLifetimeExportPO> po, BizContext ctx) {
        lifetimeAppraisalService.export(ctx);
        return BaseResult.success();
    }
}
