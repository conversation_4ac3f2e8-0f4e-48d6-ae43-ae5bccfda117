package com.inossem.wms.bizdomain.transport.service.biz;

import com.inossem.wms.bizdomain.transport.service.component.TransferComponent;
import com.inossem.wms.bizdomain.transport.service.component.TransportComponent;
import com.inossem.wms.bizdomain.transport.service.component.TransportExpireFreezeComponent;
import com.inossem.wms.bizdomain.workflow.service.CusIWorkFlowService;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 过期冻结
 */

@Service
@Slf4j
public class TransportExpireFreezeService implements CusIWorkFlowService {

    @Autowired
    private TransportExpireFreezeComponent transportExpireFreezeComponent;

    @Autowired
    private TransportComponent transportComponent;


    @Autowired
    private TransferComponent transferComponent;


    /**
     * 页面初始化
     */
    @Entrance(call = {"transferScrapFreezeComponent#init", "transferScrapFreezeComponent#setExtendWf",
        "transferScrapFreezeComponent#setExtendAttachment", "transferScrapFreezeComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportExpireFreezeComponent.init(ctx);

        // 开启审批
        transportExpireFreezeComponent.setExtendWf(ctx);

        // 开启附件
        transportExpireFreezeComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportExpireFreezeComponent.setExtendOperationLog(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transferScrapFreezeComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportExpireFreezeComponent.getStock(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transferScrapFreezeComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportExpireFreezeComponent.getPage(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transferScrapFreezeComponent#getInfo", "transferScrapFreezeComponent#setExtendWf",
        "transferScrapFreezeComponent#setExtendAttachment", "transferScrapFreezeComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 查询单据详情,包含按钮组和扩展功能
        transportExpireFreezeComponent.getInfo(ctx);

        // 开启附件
        transportExpireFreezeComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportExpireFreezeComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transferScrapFreezeComponent#checkSaveData", "transferScrapFreezeComponent#save",
        "transferScrapFreezeComponent#saveBizReceiptAttachment", "transferScrapFreezeComponent#saveBizReceiptOperationLog"})
    public void save(BizContext ctx) {

        // 保存时校验数据
        transportExpireFreezeComponent.checkSaveData(ctx);

        // 保存
        transportExpireFreezeComponent.save(ctx);

        // 保存附件
        transportExpireFreezeComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportExpireFreezeComponent.saveBizReceiptOperationLog(ctx);
    }

    @Entrance(call = {"transferScrapFreezeComponent#checkSaveData", "transferScrapFreezeComponent#save",
            "transferScrapFreezeComponent#saveBizReceiptAttachment", "transferScrapFreezeComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 保存时校验数据
        transportExpireFreezeComponent.checkSubmitData(ctx);

        // 保存
        transportExpireFreezeComponent.submit(ctx);

        // 寿期流转时-保存单据流
        transportExpireFreezeComponent.saveReceiptTree(ctx);

        // 保存附件
        transportExpireFreezeComponent.saveBizReceiptAttachment(ctx);

        // 根据移动类型保存bin
        transportExpireFreezeComponent.saveOutputBinByMoveType(ctx);

        //生成凭证
        transportExpireFreezeComponent.generateInsMoveTypeAndCheck(ctx);

        // 状态变更-未同步
        transportComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transferComponent.post(ctx);

        transportComponent.modifyStock(ctx);

        // 状态变更-已完成
        transportComponent.updateStatusCompleted(ctx);

        // 保存操作日志
        transportExpireFreezeComponent.saveBizReceiptOperationLog(ctx);


    }

    /**
     * 过账
     */
    @Entrance(call = {"transferScrapFreezeComponent#checkSubmitData", "transferScrapFreezeComponent#submit",
        "transferScrapFreezeComponent#saveBizReceiptAttachment", "transferScrapFreezeComponent#saveBizReceiptOperationLog",
        "transferScrapFreezeComponent#saveOutputBinByMoveType", "transferScrapFreezeComponent#generateInsDocToPost",
        "transferScrapFreezeComponent#checkAndComputeForModifyStock", "transferScrapFreezeComponent#updateStatusUnsync",
        "transferScrapFreezeComponent#post", "transferScrapFreezeComponent#modifyStock", "transferScrapFreezeComponent#modifyLabel",
        "transferScrapFreezeComponent#updateStatusCompleted"})
    public void post(BizContext ctx) {

        //生成凭证
        transportExpireFreezeComponent.generateInsMoveTypeAndCheck(ctx);

        // 状态变更-未同步
        transportComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transferComponent.post(ctx);

        transportComponent.modifyStock(ctx);

        // 状态变更-已完成
        transportComponent.updateStatusCompleted(ctx);

        // 保存操作日志
        transportExpireFreezeComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 删除
     */
    @Entrance(call = {"transferScrapFreezeComponent#delete", "transferScrapFreezeComponent#deleteBizReceiptAttachment"})
    public void delete(BizContext ctx) {

        // 删除
        transportExpireFreezeComponent.delete(ctx);

        // 逻辑删除附件
        transportExpireFreezeComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 移动类型列表
     */
    @Entrance(call = {"transportComponent#getMoveTypeList"})
    public void getMoveTypeList(BizContext ctx) {

        // 移动类型列表
        transportExpireFreezeComponent.getMoveTypeList(ctx);
    }



    /**
     * 冲销
     */
    @Entrance(call = {"transferScrapFreezeComponent#checkWriteOffData", "transferScrapFreezeComponent#generateInsDocToPostWriteOff",
        "transferScrapFreezeComponent#checkAndComputeForModifyStock", "transferScrapFreezeComponent#writeOff",
        "transferScrapFreezeComponent#modifyStock", "transferScrapFreezeComponent#modifyLabel", "transferScrapFreezeComponent#updateStatusWriteOff"})
    public void writeOff(BizContext ctx) {

        // 冲销时校验数据
        transportExpireFreezeComponent.checkWriteOffData(ctx);

        //生成凭证
        transportExpireFreezeComponent.generateInsMoveTypeAndCheck(ctx);

        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
        transportComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账  343 343Q
        transferComponent.writeOff(ctx);

        //修改库存
        transportComponent.modifyStock(ctx);

        // 行项目状态变更-已冲销
        transportExpireFreezeComponent.updateStatusWriteOff(ctx);

        // 保存操作日志
        transportExpireFreezeComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 获取WBS集合
     */
    @Entrance(call = {"transferScrapFreezeComponent#getWbsList"})
    public void getWbsList(BizContext ctx) {

        // 获取WBS集合
        transportExpireFreezeComponent.getWbsList(ctx);
    }

    @Override
    public void taskNotify(BizContext ctx) {
        transportExpireFreezeComponent.getTransferInfo(ctx);
        // 状态变更已完成
        this.post(ctx);
    }

    @Override
    public void refuse(BizContext ctx) {
        transportExpireFreezeComponent.getTransferInfo(ctx);
        // 状态变更已完成
        transportExpireFreezeComponent.updateStatusRejected(ctx);
    }

    /**
     * 根据寿期维护创建过期冻结单
     *
     * @param ctx 上下文
     */
    @WmsMQListener(tags = TagConst.GEN_SCRAP_FREEZE_STOCK)
    public void genScrapFreeze(BizContext ctx) {

        this.submit(ctx);

    }

    /**
     * 界面物料创建导入
     * @param ctx
     */
    public void getStockImport(BizContext ctx) {
        transportExpireFreezeComponent.getStockImport(ctx);
    }
}