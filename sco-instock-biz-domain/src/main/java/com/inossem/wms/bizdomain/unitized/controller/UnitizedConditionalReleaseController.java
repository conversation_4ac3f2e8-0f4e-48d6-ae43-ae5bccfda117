package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedConditionalReleaseService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成套设备-有条件放行
 * </p>
 *
 * <AUTHOR>
 */
@RestController
public class UnitizedConditionalReleaseController {

    @Autowired
    protected UnitizedConditionalReleaseService unitizedConditionalReleaseService;

    /**
     * 初始化
     */
    @ApiOperation(value = "初始化", tags = {"成套设备-有条件放行"})
    @PostMapping(value = "/unitized/conditional-release/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> init(BizContext ctx) {
        unitizedConditionalReleaseService.init(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 分页
     */
    @ApiOperation(value = "分页", tags = {"成套设备-有条件放行"})
    @PostMapping(value = "/unitized/conditional-release/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInconformityPageVO>> getPage(@RequestBody BizReceiptInconformitySearchPO po, BizContext ctx) {
        unitizedConditionalReleaseService.getPage(ctx);
        PageObjectVO<BizReceiptInconformityPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     */
    @ApiOperation(value = "详情", tags = {"成套设备-有条件放行"})
    @GetMapping(value = "/unitized/conditional-release/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedConditionalReleaseService.getInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     */
    @ApiOperation(value = "保存", tags = {"成套设备-有条件放行"})
    @PostMapping(value = "/unitized/conditional-release/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedConditionalReleaseService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 提交
     */
    @ApiOperation(value = "提交", tags = {"成套设备-有条件放行"})
    @PostMapping(value = "/unitized/conditional-release/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedConditionalReleaseService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 前续单据-查询列表
     */
    @ApiOperation(value = "前续单据-查询列表", tags = {"成套设备-有条件放行"})
    @PostMapping(value = "/unitized/conditional-release/pre-receipt", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getPreReceipt(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        unitizedConditionalReleaseService.getPreReceipt(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 前续单据-填充详细
     */
    @ApiOperation(value = "前续单据-填充详细", tags = {"成套设备-有条件放行"})
    @PostMapping(value = "/unitized/conditional-release/fillEntity", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> fillEntity(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedConditionalReleaseService.fillEntity(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}

