package com.inossem.wms.bizdomain.transport.service.biz;

import com.inossem.wms.bizdomain.transport.service.component.TransferComponent;
import com.inossem.wms.bizdomain.transport.service.component.TransportComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransferService {

    @Autowired
    @Lazy
    private TransferComponent transferComponent;


    /**
     * 移动类型列表
     */
    @Entrance(call = {"transferComponent#getMoveTypeList"})
    public void getMoveTypeList(BizContext ctx) {

        // 移动类型列表
        transferComponent.getMoveTypeList(ctx);
    }

    /**
     * 页面初始化
     */
    @Entrance(call = {"transferComponent#init", "transferComponent#setExtendWf",
        "transferComponent#setExtendAttachment", "transferComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 页面初始化
        transferComponent.init(ctx);

        // 开启审批
        //transferComponent.setExtendWf(ctx);

        // 开启附件
        transferComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transferComponent.setExtendOperationLog(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transferComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transferComponent.getStock(ctx);
    }

    /**
     * 转性物料导入
     */
    @Transactional(rollbackFor = Exception.class)
    public void importMaterial(BizContext ctx) {

        // 查询库存
        transferComponent.importMaterial(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transferComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transferComponent.getPage(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transferComponent#getInfo", "transferComponent#setExtendWf",
        "transferComponent#setExtendAttachment", "transferComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 查询单据详情,包含按钮组和扩展功能
        transferComponent.getInfo(ctx);

        // 开启审批
        transferComponent.setExtendWf(ctx);

        // 开启附件
        transferComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transferComponent.setExtendOperationLog(ctx);

        // 开启审批
        transferComponent.setExtendWf(ctx);

    }

    /**
     * 保存
     */
    @Entrance(call = {"transferComponent#checkSaveData", "transferComponent#save",
        "transferComponent#saveBizReceiptAttachment", "transferComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存时校验数据
        transferComponent.checkSaveData(ctx);

        // 保存
        transferComponent.save(ctx);

        // 保存附件
        transferComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transferComponent.saveBizReceiptOperationLog(ctx);
    }

    @Entrance(call = {"transferComponent#checkSaveData", "transferComponent#save",
            "transferComponent#saveBizReceiptAttachment", "transferComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 保存时校验数据
        transferComponent.checkSubmitData(ctx);

        // 保存
        transferComponent.submit(ctx);

        // 保存附件
        transferComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transferComponent.saveBizReceiptOperationLog(ctx);

        // 【先作业模式】状态变更已提交
        transferComponent.updateStatusSubmitted(ctx);

        // 根据移动类型保存bin
        transferComponent.saveOutputBinByMoveType(ctx);

        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO!=null && Const.MOVE_TYPE_411.equals(headDTO.getMoveTypeCode())) {
            this.post(ctx);
            // 更新单据已完成
            transferComponent.updateStatusCompleted(ctx);
        }else{
            //石岛湾 【物料转性】非限制转Q的单据无需审批
            if ("415".equals(headDTO.getMoveTypeCode())&&
                    "Q".equals(headDTO.getInputSpecStock())&&
                    "".equals(headDTO.getOutSpecStock())) {
                //过账
                post(ctx);
            } else {
                // 发起审批
                transferComponent.startWorkFlow(ctx);
            }

        }
    }

    /**
     * 过账
     */
    @Entrance(call = {"transferComponent#checkSubmitData", "transferComponent#submit",
        "transferComponent#saveBizReceiptAttachment", "transferComponent#saveBizReceiptOperationLog",
        "transferComponent#saveOutputBinByMoveType", "transferComponent#generateInsDocToPost",
        "transferComponent#checkAndComputeForModifyStock", "transferComponent#updateStatusUnsync",
        "transferComponent#post", "transferComponent#modifyStock", "transferComponent#modifyLabel",
        "transferComponent#updateStatusCompleted"})
    public void post(BizContext ctx) {

//        // 提交时校验数据
//        transferComponent.checkSubmitData(ctx);
//
//        // 提交
//        transferComponent.submit(ctx);


        // 生成ins凭证 - 转储接收发出一次过账
        transferComponent.generateInsDocToPost(ctx);

        // 状态变更-未同步
        transferComponent.updateStatusUnsync(ctx);

        // 过账前校验和数量计算
        transferComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账
        transferComponent.post(ctx);

        transferComponent.modifyStock(ctx);

        // 修改标签
        transferComponent.modifyLabel(ctx);

        // 状态变更-已完成
        transferComponent.updateStatusCompleted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transferComponent#delete", "transferComponent#deleteBizReceiptAttachment"})
    public void delete(BizContext ctx) {

        // 删除
        transferComponent.delete(ctx);

        // 逻辑删除附件
        transferComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 冲销
     */
    @Entrance(call = {"transferComponent#checkWriteOffData", "transferComponent#generateInsDocToPostWriteOff",
        "transferComponent#checkAndComputeForModifyStock", "transferComponent#writeOff",
        "transferComponent#modifyStock", "transferComponent#modifyLabel", "transferComponent#updateStatusWriteOff"})
    public void writeOff(BizContext ctx) {

        // 冲销时校验数据
        transferComponent.checkWriteOffData(ctx);

        // 生成ins凭证 - 冲销
        transferComponent.generateInsDocToPostWriteOff(ctx);

        // 过账前校验和数量计算
        transferComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账-冲销
        transferComponent.writeOff(ctx);

        // 修改库存
        transferComponent.modifyStock(ctx);

        // 修改标签
        transferComponent.modifyLabel(ctx);

        // 行项目状态变更-已冲销
        transferComponent.updateStatusWriteOff(ctx);
    }

    /**
     * 获取WBS集合
     */
    @Entrance(call = {"transferComponent#getWbsList"})
    public void getWbsList(BizContext ctx) {

        // 获取WBS集合
        transferComponent.getWbsList(ctx);
    }


    /**
     * 审批回调
     *
     * @param wfReceiptCo BizApprovalReceiptInstanceRelDTO
     */
    @WmsMQListener(tags = TagConst.APPROVAL_TRANSFER_NATURE_APPLY)
//    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        if (EnumApprovalStatus.FINISH.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批通过
            transferComponent.doTransferPost(wfReceiptCo);
        } else if (EnumApprovalStatus.REJECT.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批拒绝
            transferComponent.updateStatus(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }

    }



}
