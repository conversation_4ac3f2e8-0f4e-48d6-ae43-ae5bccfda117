<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.logistics.dao.BizReceiptLogisticsHeadMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, receipt_code, receipt_type, receipt_status,plan_arrival_date,real_delivery_time,remark, is_delete, create_time, modify_time, create_user_id, modify_user_id
    </sql>

    <!--物流清关费用单 - 分页-->
    <select id="selectLogisticsPageVo"
            resultType="com.inossem.wms.common.model.bizdomain.logistics.vo.BizReceiptLogisticsListVo">
        SELECT
            biz_receipt_logistics_head.*,
            biz_receipt_contract_head.receipt_code contract_code,
            biz_receipt_contract_head.create_user_name purchaser_name,
            dic_factory.fty_code,
            dic_factory.fty_name,
            sys_user.user_name AS create_user_name
        FROM
            biz_receipt_logistics_head
            INNER JOIN biz_receipt_logistics_item ON biz_receipt_logistics_head.id = biz_receipt_logistics_item.head_id
            LEFT JOIN dic_material ON biz_receipt_logistics_item.mat_id = dic_material.id AND dic_material.is_delete = 0
            LEFT JOIN dic_factory ON biz_receipt_logistics_item.fty_id = dic_factory.id AND dic_factory.is_delete = 0
            INNER JOIN sys_user ON biz_receipt_logistics_head.create_user_id = sys_user.id
            Left Join biz_receipt_contract_head biz_receipt_contract_head on       biz_receipt_contract_head.id =  biz_receipt_logistics_head.contract_id
            AND  sys_user.is_delete = 0
            AND biz_receipt_logistics_head.is_delete = 0
        ${ew.customSqlSegment}
        group by biz_receipt_logistics_head.id
    </select>

    <!--物流清关费用单 - 分页-->
    <select id="selectLogisticsPageVoUnitized"
            resultType="com.inossem.wms.common.model.bizdomain.logistics.vo.BizReceiptLogisticsListVo">
        SELECT
            biz_receipt_logistics_head.*,
            dic_factory.fty_code,
            dic_factory.fty_name,
            sys_user.user_name AS create_user_name
        FROM
            biz_receipt_logistics_head
                INNER JOIN biz_receipt_logistics_item ON biz_receipt_logistics_head.id = biz_receipt_logistics_item.head_id
                LEFT JOIN biz_receipt_waybill  on biz_receipt_logistics_head.id=biz_receipt_waybill.logistics_head_id and  biz_receipt_logistics_item.id=biz_receipt_waybill.logistics_item_id
                LEFT JOIN dic_material ON biz_receipt_logistics_item.mat_id = dic_material.id AND dic_material.is_delete = 0
                LEFT JOIN dic_factory ON biz_receipt_logistics_item.fty_id = dic_factory.id AND dic_factory.is_delete = 0
                INNER JOIN sys_user ON biz_receipt_logistics_head.create_user_id = sys_user.id
                AND  sys_user.is_delete = 0
                AND biz_receipt_logistics_head.is_delete = 0
            ${ew.customSqlSegment}
        group by biz_receipt_logistics_head.id
    </select>

    <select id="selectLogistics" parameterType="com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsSearchPO" resultType="com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsHead">
        SELECT
            h.* 
        FROM
            biz_receipt_logistics_head h
            inner join biz_receipt_logistics_item i on h.id = i.head_id
            left join biz_receipt_contract_head contract on contract.receipt_code = i.contract_code
            left join biz_receipt_contract_head contracth on contract.id = h.contract_id
        where 1 = 1
        <if test="receiptType !=null ">
            and h.receipt_type = #{receiptType}
        </if>
        <if test="receiptStatusList !=null and receiptStatusList.size()>0">
            and h.receipt_status in
            <foreach collection="receiptStatusList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="contractCode !=null and contractCode !=''">
            and (contracth.receipt_code = #{contractCode} or i.contract_code = #{contractCode})
        </if>
        <if test="contractName !=null and contractName !=''">
            and (contracth.contract_name like concat ( '%',#{contractName} ,'%') or contract.contract_name like concat ( '%',#{contractName} ,'%'))
        </if>
        <if test="sendType !=null and sendType !=0 ">
            and h.send_type = #{sendType}
        </if>
        <if test="demandPlanCode !=null and demandPlanCode !=''">
            and i.demand_plan_code = #{demandPlanCode}
        </if>
        <if test="receiptCode !=null and receiptCode !=''">
            and h.receipt_code = #{receiptCode}
        </if>
        <if test="purchaseCode !=null and purchaseCode !=''">
            and h.purchase_code = #{purchaseCode}
        </if>
        group by h.id
    </select>
</mapper>
