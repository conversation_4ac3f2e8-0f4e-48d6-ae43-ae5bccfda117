package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedInconformityNumberNoticeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.enums.CheckResultMapVO;
import com.inossem.wms.common.model.common.enums.inconformity.DifferentTypeMapVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成套设备数量差异通知 前端控制器
 * </p>
 */
@RestController
public class UnitizedInconformityNumberNoticeController {

    @Autowired
    protected UnitizedInconformityNumberNoticeService unitizedInconformityNumberNoticeService;

    /**
     * 查询差异类型下拉
     *
     * @return 差异类型下拉框
     */
    @ApiOperation(value = "查询差异类型下拉", tags = {"验收管理-成套设备数量差异通知"})
    @GetMapping(path = "/unitized/inconformity/inconformity-number-notice/different-type-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DifferentTypeMapVO>> getDifferentTypeDown(BizContext ctx) {
        unitizedInconformityNumberNoticeService.getDifferentTypeDown(ctx);
        MultiResultVO<DifferentTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询成套设备数量差异通知列表-分页
     *
     * @param po 分页查询入参
     * @return 单据列表
     */
    @ApiOperation(value = "查询成套设备数量差异通知列表-分页", tags = {"验收管理-成套设备数量差异通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-notice/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInconformityPageVO>> getPage(@RequestBody BizReceiptInconformitySearchPO po, BizContext ctx) {
        unitizedInconformityNumberNoticeService.getPage(ctx);
        PageObjectVO<BizReceiptInconformityPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询成套设备数量差异通知详情
     *
     * @param id 成套设备数量差异通知抬头表主键
     * @return 成套设备数量差异通知详情
     */
    @ApiOperation(value = "查询成套设备数量差异通知详情", tags = {"验收管理-成套设备数量差异通知"})
    @GetMapping(value = "/unitized/inconformity/inconformity-number-notice/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedInconformityNumberNoticeService.getInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 成套设备数量差异通知-保存
     *
     * @param po 保存成套设备数量差异通知表单参数;l
     * @return 国际化提示
     */
    @ApiOperation(value = "成套设备数量差异通知-保存", tags = {"验收管理-成套设备数量差异通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-notice/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNumberNoticeService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 成套设备数量差异通知-提交
     *
     * @param po 提交成套设备数量差异通知表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "成套设备数量差异通知-提交", tags = {"验收管理-成套设备数量差异通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-notice/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNumberNoticeService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 成套设备数量差异通知-冲销
     *
     * @param po 提交成套设备数量差异处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "成套设备数量差异通知-冲销", tags = {"成套设备管理-成套设备数量差异通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-notice/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult writeOff(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNumberNoticeService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_WRITEOFF_SUCCESS, po.getReceiptCode());
    }
    /**
     * 成套设备数量差异通知-过账
     *
     * @param po 提交成套设备数量差异处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "成套设备数量差异通知-过账", tags = {"成套设备管理-成套设备数量差异通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-notice/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult post(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNumberNoticeService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 成套设备数量差异通知-前续单据
     *
     * @param po 查询条件
     * @return 质检会签单
     */
    @ApiOperation(value = "成套设备数量差异通知-前续单据", tags = {"验收管理-成套设备数量差异通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-notice/pre-receipt", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getPreReceipt(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        unitizedInconformityNumberNoticeService.getPreReceipt(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 查询供应商处理意见
     *
     * @return 查询供应商处理意见
     */
    @ApiOperation(value = "查询供应商处理意见", tags = {"验收管理-不符合项处置"})
    @GetMapping(path = "/unitized/inconformity/inconformity-number-notice/supplier-solve-reason-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<CheckResultMapVO>> getSupplierSolveReasonDown(BizContext ctx) {
        unitizedInconformityNumberNoticeService.getSupplierSolveReasonDown(ctx);
        MultiResultVO<CheckResultMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}

