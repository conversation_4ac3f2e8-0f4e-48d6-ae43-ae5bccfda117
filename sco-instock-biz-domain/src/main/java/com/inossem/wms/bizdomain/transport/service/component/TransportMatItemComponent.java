package com.inossem.wms.bizdomain.transport.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.todo.service.biz.StWftaskService;
import com.inossem.wms.bizbasis.todo.service.datawrap.StWftaskDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportBinDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportRuleDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportBin;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportRule;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料转码
 */

@Service
@Slf4j
public class TransportMatItemComponent {

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected ReceiptRelationService receiptRelationService;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    protected ReceiptAttachmentService bizReceiptAttachmentService;
    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;
    @Autowired
    private BizReceiptTransportBinDataWrap bizReceiptTransportBinDataWrap;
    @Autowired
    private BizReceiptTransportRuleDataWrap bizReceiptTransportRuleDataWrap;
    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;
    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private BatchInfoService batchInfoService;
    @Autowired
    protected BizBatchInfoDataWrap bizBatchInfoDataWrap;
    @Autowired
    private TransportMoveTypeComponent transportMoveTypeComponent;
    @Autowired
    protected SapInterfaceService sapInterfaceService;
    @Autowired
    private LabelReceiptRelService labelReceiptRelService;
    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    protected ApprovalService approvalService;
    @Autowired
    protected StWftaskDataWrap stWftaskDataWrap;
    @Autowired
    protected StWftaskService stWftaskService;
    @Autowired
    private TransportMatComponent transportMatComponent;
    @Autowired
    private BizLabelDataDataWrap bizLabelDataDataWrap;

    /**
     * 新增移动类型为 Y81/Y82 和 Y81Q/Y82Q保存
     */
    public void saveCT(BizReceiptTransportHeadDTO headDTO, CurrentUser currentUser) {
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setCreateTime(new Date());
            itemDto.setModifyTime(new Date());
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        }
        bizReceiptTransportItemDataWrap.saveBatchDto(itemDTOList);
        List<BizReceiptTransportItemDTO> saveSplitItemList = new ArrayList<>();
        //Y81/Y82和Y81Q/Y82Q为不相同计量单位转码 ，存在物料拆分，保存拆分出的行项目
        for (BizReceiptTransportItemDTO bizReceiptTransportItemDTO : itemDTOList) {
            bizReceiptTransportItemDTO.getSplitItemVOList().forEach(
                    e -> e.setSourceItemId(bizReceiptTransportItemDTO.getId())
                            .setHeadId(headDTO.getId())
                            .setId(null)
                            .setRid(String.valueOf(rid.getAndIncrement())));
            saveSplitItemList.addAll(bizReceiptTransportItemDTO.getSplitItemVOList());
        }
        bizReceiptTransportItemDataWrap.saveBatchDto(saveSplitItemList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            BizReceiptTransportItemDTO bizReceiptTransportItemDTO = itemDto.getSplitItemVOList().get(0);
            Long inputTypeId = bizReceiptTransportItemDTO.getInputTypeId();
            Long inputBinId = bizReceiptTransportItemDTO.getInputBinId();
            for (BizReceiptTransportItemDTO splitItem : itemDto.getSplitItemVOList()) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : splitItem.getAssembleDTOList()) {
                    bizReceiptAssembleDTO.setReceiptType(headDTO.getReceiptType());
                    bizReceiptAssembleDTO.setReceiptHeadId(headDTO.getId());
                    bizReceiptAssembleDTO.setReceiptItemId(splitItem.getId());
                    bizReceiptAssembleDTO.setQty(splitItem.getQty());
                    bizReceiptAssembleDTO.setMatId(splitItem.getInputMatId());
                    bizReceiptAssembleDTO.setId(null);
                    if (UtilNumber.isEmpty(bizReceiptAssembleDTO.getInputBinId())) {
                        bizReceiptAssembleDTO.setInputTypeId(inputTypeId);
                        bizReceiptAssembleDTO.setInputBinId(inputBinId);
                    }
                    bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                            ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                            : bizReceiptAssembleDTO.getSpecType());
                    assembleDTOList.add(bizReceiptAssembleDTO);
                }
            }
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                bizReceiptAssembleDTO.setReceiptType(headDTO.getReceiptType());
                bizReceiptAssembleDTO.setReceiptHeadId(headDTO.getId());
                bizReceiptAssembleDTO.setReceiptItemId(itemDto.getId());
                bizReceiptAssembleDTO.setId(null);
                bizReceiptAssembleDTO.setInputTypeId(inputTypeId);
                bizReceiptAssembleDTO.setInputBinId(inputBinId);
                bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                        ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                        : bizReceiptAssembleDTO.getSpecType());
                assembleDTOList.add(bizReceiptAssembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        if (UtilCollection.isNotEmpty(labelDataList)) {
            bizLabelDataDataWrap.saveBatchDto(labelDataList);
        }

        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(itemDto.getHeadId());
                        labelReceiptRel.setReceiptItemId(itemDto.getId());
                        labelReceiptRel.setReceiptBinId(bizReceiptAssembleDTO.getId());
                        bizLabelReceiptRelList.add(labelReceiptRel);
                        labelRelIdList.add(labelReceiptRelDTO.getId());
                    }
                }
            }
        }

        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }

    /**
     * 不同计量单位
     * 【先过账模式】设置批次id-用于生成接收方批次
     */
    public void setCTAssembleInputBatchId(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        List<BizBatchInfoDTO> batchInfoDtoList = new ArrayList<>();
        // 批量查询批次信息
        Map<Long, BizBatchInfoDTO> batchInfoDTOMap = transportMatComponent.getBatchMapByAssmbleList(headDTO);
        // 单据移动类型-取发出特殊库存类型
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(headDTO.getMoveTypeId());
        List<BizBatchInfoDTO> copyBatchImgList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                for (BizReceiptTransportItemDTO splitItemDto : itemDto.getSplitItemVOList()) {
                    BizReceiptAssembleDTO bizReceiptAssembleDTO = UtilBean.newInstance(assembleDTO, BizReceiptAssembleDTO.class);
                    bizReceiptAssembleDTO.setId(null);
                    bizReceiptAssembleDTO.setQty(splitItemDto.getQty());
                    String uk = splitItemDto.getInputFtyId() + "-" + splitItemDto.getInputMatId() + "-" + headDTO.getInputSpecStock()
                            + "-" + assembleDTO.getSpecStockCode() + "-" + transportMatComponent.getBatchCode(assembleDTO);
                    Long batchId = transportMatComponent.getBatchId(assembleDTO);
                    if (batchMap.containsKey(uk)) {
                        // 已有批次
                        BizBatchInfoDTO batchInfoDTO = batchMap.get(uk);
                        bizReceiptAssembleDTO.setInputBatchInfoDTO(batchInfoDTO);
                    } else {
                        String outputSpecStockCode = transportMatComponent.getSpecStockCode(assembleDTO);
                        QueryWrapper<BizReceiptTransportRule> queryWrapper = new QueryWrapper<>();
                        queryWrapper.lambda().eq(BizReceiptTransportRule::getMoveTypeId, headDTO.getMoveTypeId());
                        BizReceiptTransportRule rule = bizReceiptTransportRuleDataWrap.getOne(queryWrapper);
                        String inputSpecStockCode = transportMatComponent.checkRule(rule.getInputSpecStockCode(), outputSpecStockCode,
                                splitItemDto.getInputSpecStockCode());
                        BizBatchInfoDTO batchInfoDTO = UtilBean.newInstance(batchInfoDTOMap.get(batchId), BizBatchInfoDTO.class);
                        // 判断批次唯一索引是否变更:工厂/物料/特殊库存类型/代码变更
                        String outputKey = itemDto.getOutputFtyId() + "-" + itemDto.getOutputMatId() + "-"
                                + dicMoveType.getSpecStock() + "-" + outputSpecStockCode;
                        String inputKey = splitItemDto.getInputFtyId() + "-" + splitItemDto.getInputMatId() + "-"
                                + headDTO.getInputSpecStock() + "-" + inputSpecStockCode;
                        if (!outputKey.equals(inputKey)) {
                            batchInfoDTO.setPreBatchId(batchId);
                            batchInfoDTO.setPreFtyId(itemDto.getOutputFtyId());
                            batchInfoDTO.setPreMatId(itemDto.getOutputMatId());
                            batchInfoDTO.setFtyId(splitItemDto.getInputFtyId());
                            batchInfoDTO.setMatId(splitItemDto.getInputMatId());
                            // 特殊库存类型变更
                            batchInfoDTO.setSpecStock(headDTO.getSpecStock());
                            // 特殊库存代码变更
                            batchInfoDTO.setCreateTime(new Date());
                            batchInfoDTO.setModifyTime(new Date());
                            batchInfoDTO.setSpecStockCode(assembleDTO.getSpecStockCode());
                            batchInfoDTO.setSpecStockName(assembleDTO.getSpecStockName());
                            batchInfoDTO.setId(null);
                            batchInfoDtoList.add(batchInfoDTO);
                        }
                        bizReceiptAssembleDTO.setInputBatchInfoDTO(batchInfoDTO);
                        batchMap.put(uk, batchInfoDTO);
                        copyBatchImgList.add(batchInfoDTO);
                    }
                    List<BizReceiptAssembleDTO> splitAssembleList = Collections.singletonList(bizReceiptAssembleDTO);
                    splitItemDto.setAssembleDTOList(splitAssembleList);
                }
            }
        }
        if (UtilCollection.isNotEmpty(batchInfoDtoList)) {
            // 批次信息批量保存 - 唯一索引,存在则取id,不存在则新增
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDtoList);
            // 复制物料批次图片
            transportMatComponent.saveCopyBatchImg(batchInfoDtoList, ctx.getCurrentUser());
        }
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptTransportItemDTO splitItem : itemDto.getSplitItemVOList()) {
                for (BizReceiptAssembleDTO assembleDTO : splitItem.getAssembleDTOList()) {
                    // 回填接收批次id
                    assembleDTO.setInputBatchId(assembleDTO.getInputBatchInfoDTO().getId());
                }
            }
        }
        // TODO: 2021/4/28 批次特性转移
    }


    /**
     * 创建标签信息
     */
    public BizLabelDataDTO createLabel(BizReceiptTransportItemDTO splitItem) {
        // 物料主数据
        String labelCode =
                bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());
        BizLabelDataDTO label = BizLabelDataDTO.builder()
                .id(UtilSequence.nextId())
                .matId(splitItem.getInputMatId())
                .ftyId(splitItem.getInputFtyId())
                .locationId(splitItem.getInputLocationId())
                .batchId(splitItem.getAssembleDTOList().get(0).getInputBatchId())
                .binId(splitItem.getInputBinId())
                .whId(splitItem.getInputWhId())
                .typeId(splitItem.getInputTypeId())
                .labelCode(labelCode)
                .snCode(labelCode)
                .qty(splitItem.getQty())
                .labelType(splitItem.getAssembleDTOList().get(0).getInputBatchInfoDTO().getTagType())
                .receiptHeadId(splitItem.getHeadId())
                .receiptItemId(splitItem.getId())
                .receiptType(EnumReceiptType.STOCK_TRANSPORT_MAT.getValue())
                .build();
        return label;
    }

    /**
     * 创建标签关联关系
     */
    public BizLabelReceiptRel createLabelRel(BizReceiptTransportItemDTO splitItem, BizLabelDataDTO label, Long userId) {
        BizLabelReceiptRel rel = new BizLabelReceiptRel();
        rel.setId(UtilSequence.nextId());
        rel.setReceiptHeadId(splitItem.getHeadId());
        rel.setReceiptItemId(splitItem.getId());
        rel.setLabelId(label.getId());
        rel.setReceiptType(EnumReceiptType.STOCK_TRANSPORT_MAT.getValue());
        rel.setReceiptBinId(splitItem.getAssembleDTOList().get(0).getInputBatchInfoDTO().getBinId());
        rel.setCreateUserId(userId);
        rel.setCreateTime(new Date());
        rel.setModifyTime(new Date());
        rel.setModifyUserId(userId);
        return rel;
    }

    /**
     * 生成bin表
     */
    public void saveOutputBinQuickModel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        QueryWrapper<BizReceiptTransportBin> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(BizReceiptTransportBin::getHeadId,headDTO.getId());
        bizReceiptTransportBinDataWrap.remove(deleteWrapper);
        // bin处理
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
            List<BizReceiptTransportBinDTO> transportBinList = this.buildTransportBinList(headDTO.getId(), itemDTO.getSplitItemVOList());
            transportBinDTOList.addAll(transportBinList);
        }
        List<BizReceiptTransportBinDTO> transportBinList = this.buildTransportBinList(headDTO.getId(), headDTO.getItemDTOList());
        transportBinDTOList.addAll(transportBinList);
        dataFillService.fillAttr(transportBinDTOList);
        // bin表保存
        List<BizReceiptTransportBin> list = UtilCollection.toList(transportBinDTOList, BizReceiptTransportBin.class);
        bizReceiptTransportBinDataWrap.saveBatch(list);
        this.handleLabel(headDTO);
    }

    /**
     * 为接收物料创建新的标签 与标签关系
     */
    public void handleLabel(BizReceiptTransportHeadDTO headDTO) {
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizLabelDataDTO> labelNewList = new ArrayList<>();
        List<BizLabelReceiptRel> relNewList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDTO : itemDTOList) {
            for (BizReceiptTransportItemDTO splitItemDTO : itemDTO.getSplitItemVOList()) {
                for (BizReceiptAssembleDTO assembleDTO : splitItemDTO.getAssembleDTOList()) {
                    //接收物料创建新的标签
                    BizLabelDataDTO label = this.createLabel(splitItemDTO);
                    labelNewList.add(label);
                    //接收物料创建新的标签关联关系
                    BizLabelReceiptRel labelRel = this.createLabelRel(splitItemDTO, label, headDTO.getCreateUserId());
                    labelRel.setReceiptBinId(assembleDTO.getId());
                    relNewList.add(labelRel);
                }
            }
        }
        labelDataService.saveBatchDto(labelNewList);
        labelReceiptRelService.saveBatch(relNewList);
    }


    /**
     * 构建转码 bin 数据
     */
    public List<BizReceiptTransportBinDTO> buildTransportBinList(Long headId, List<BizReceiptTransportItemDTO> itemDTOList) {
        List<BizReceiptTransportBinDTO> transportBinDTOList = new ArrayList<>();
        for (BizReceiptTransportItemDTO splitItem : itemDTOList) {
            int bid = 1;
            for (BizReceiptAssembleDTO assembleDTO : splitItem.getAssembleDTOList()) {
                BizReceiptTransportBinDTO transportBinDTO = new BizReceiptTransportBinDTO();
                transportBinDTO.setId(null);
                transportBinDTO.setBid(Integer.toString(bid));
                transportBinDTO.setHeadId(headId);
                transportBinDTO.setItemId(splitItem.getId());
                transportBinDTO.setTaskItemId((long) 0);
                transportBinDTO.setOutputBatchId(transportMatComponent.getBatchId(assembleDTO));
                transportBinDTO.setOutputTypeId(assembleDTO.getInputTypeId());
                transportBinDTO.setOutputBinId(assembleDTO.getInputBinId());
                transportBinDTO.setOutputCellId((long) 0);
                transportBinDTO.setOutputSpecStockCode(assembleDTO.getSpecStockCode());
                transportBinDTO.setOutputSpecStockName(assembleDTO.getSpecStockName());
                transportBinDTO.setQty(assembleDTO.getQty());
                if (assembleDTO.getInputBatchInfoDTO() == null) {
                    transportBinDTOList.add(transportBinDTO);
                    bid++;
                    continue;
                }
                transportBinDTO.setInputBatchId(assembleDTO.getInputBatchInfoDTO().getId());
                transportBinDTO.setInputBinId(assembleDTO.getInputBinId());
                transportBinDTO.setInputTypeId(assembleDTO.getInputTypeId());
                transportBinDTO.setInputCellId((long) 0);
                transportBinDTO.setInputSpecStockCode(assembleDTO.getSpecStockCode());
                transportBinDTO.setInputSpecStockName(assembleDTO.getSpecStockName());
                if (UtilCollection.isNotEmpty(splitItem.getBinDTOList())) {
                    splitItem.getBinDTOList().add(transportBinDTO);
                } else {
                    List<BizReceiptTransportBinDTO> binDTOList = new ArrayList<>();
                    binDTOList.add(transportBinDTO);
                    splitItem.setBinDTOList(binDTOList);
                }
                transportBinDTOList.add(transportBinDTO);
                bid++;
            }
        }
        return transportBinDTOList;
    }

    /**
     * 修改标签
     */
    public void modifyLabel(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null) {
            return;
        }
        // 同时模式,在页面选择标签
        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
        labelReceiptRel.setReceiptHeadId(headDTO.getId());
        List<BizLabelReceiptRel> relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
        if (UtilCollection.isEmpty(relList)) {
            // 先作业模式,在下架生成标签关联信息
            labelReceiptRel = new BizLabelReceiptRel();
            labelReceiptRel.setReceiptType(EnumReceiptType.PALLET_SORTING_INPUT.getValue());
            labelReceiptRel.setPreReceiptHeadId(headDTO.getId());
            relList = labelReceiptRelService.getList(null, null, null, labelReceiptRel);
            if (UtilCollection.isEmpty(relList)) {
                // 未查询到对应的标签信息则不修改
                return;
            }
        }
        List<BizLabelData> labelDataList = new ArrayList<>();
        for (BizLabelReceiptRel receiptRel : relList) {
            for (BizReceiptTransportItemDTO itemDTO : headDTO.getItemDTOList()) {
                for (BizReceiptTransportItemDTO splitItem : itemDTO.getSplitItemVOList()) {
                    for (BizReceiptTransportBinDTO binDTO : splitItem.getBinDTOList()) {
                        if (receiptRel.getReceiptBinId().equals(binDTO.getId())
                                || receiptRel.getPreReceiptBinId().equals(binDTO.getId())) {
                            // id一致
                            BizLabelData labelData = new BizLabelData();
                            labelData.setId(receiptRel.getLabelId());
                            if (splitItem.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
                                // 冲销回发出
                                labelData.setFtyId(itemDTO.getOutputFtyId());
                                labelData.setLocationId(itemDTO.getOutputLocationId());
                                labelData.setWhId(itemDTO.getOutputWhId());
                                labelData.setMatId(itemDTO.getOutputMatId());
                                labelData.setBatchId(binDTO.getOutputBatchId());
                                labelData.setTypeId(binDTO.getOutputTypeId());
                                labelData.setBinId(binDTO.getOutputBinId());
                                labelData.setCellId(binDTO.getOutputCellId());
                            } else {
                                // 批次信息更新为接收
                                labelData.setFtyId(splitItem.getInputFtyId());
                                labelData.setLocationId(splitItem.getInputLocationId());
                                labelData.setWhId(splitItem.getInputWhId());
                                labelData.setMatId(splitItem.getInputMatId());
                                labelData.setBatchId(binDTO.getInputBatchId());
                                if (!UtilNumber.isEmpty(binDTO.getInputTypeId())) {
                                    labelData.setTypeId(binDTO.getInputTypeId());
                                }
                                if (!UtilNumber.isEmpty(binDTO.getInputBinId())) {
                                    labelData.setBinId(binDTO.getInputBinId());
                                }
                                if (!UtilNumber.isEmpty(binDTO.getInputCellId())) {
                                    labelData.setCellId(binDTO.getInputCellId());
                                }
                            }
                            labelDataList.add(labelData);
                        }
                    }
                }
            }
        }
        labelDataService.multiUpdateLabelData(labelDataList);
    }

    /**
     * 构建 移动类型 Y81和Y82库存凭证
     */
    public StockInsMoveTypeDTO generateY81AndY82InsDocToPostQuickModel(BizReceiptTransportHeadDTO headDTO, Long userId) {
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        int brid = 1;
        int rid = 1;
        String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptTransportBinDTO binDTO : itemDto.getBinDTOList()) {
                // 批次库存-发出方扣减
                insDocBatchList.add(transportMoveTypeComponent.getOutputInsBatchQuickModel(headDTO, itemDto, binDTO, brid++, code, userId));
                // 仓位库存-发出方扣减
                insDocBinList.add(transportMoveTypeComponent.getOutputInsBinQuickModel(headDTO, itemDto, binDTO, rid++, code, userId));
            }
            for (BizReceiptTransportItemDTO splitItemDTO : itemDto.getSplitItemVOList()) {
                for (BizReceiptTransportBinDTO splitBinDTO : splitItemDTO.getBinDTOList()) {
                    // 批次库存-接收方新增
                    insDocBatchList.add(transportMoveTypeComponent.getInputInsBatchQuickModel(headDTO, splitItemDTO, splitBinDTO, brid++, code, userId));
                    // 仓位库存-接收方新增
                    insDocBinList.add(transportMoveTypeComponent.getInputInsBinQuickModel(headDTO, splitItemDTO, splitBinDTO, rid++, code, userId));
                }
            }
        }
        StockInsMoveTypeDTO insMoveTypeDTO = new StockInsMoveTypeDTO();
        insMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        insMoveTypeDTO.setInsDocBinList(insDocBinList);
        return insMoveTypeDTO;
    }

    /**
     * 过账
     * <p>
     * 针对移动类型 Y81/Y82 和 Y81Q/Y82Q
     * </p>
     */
    public void postY81AndY82(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        // 未同步sap行项目
        this.setTransTransportBin(headDTO);
        // 过滤已过帐子行项目
        List<BizReceiptTransportItemDTO> itemListNotSync = UtilBean.deepCopy(headDTO.getItemDTOList());
        for (BizReceiptTransportItemDTO itemDTO : itemListNotSync) {
            itemDTO.getSplitItemVOList().removeIf(e -> UtilNumber.isNotEmpty(e.getIsPost()));
        }
        itemListNotSync.removeIf(e -> UtilCollection.isEmpty(e.getSplitItemVOList()));
        /* ******** 设置入库单账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        itemListNotSync.forEach(p -> p.setReceiptType(headDTO.getReceiptType()));
        //以单层了列表的结构 收集 层级结构的行项目
        List<BizReceiptTransportItemDTO> updateListNotSync = new ArrayList<>();
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.postingTransportMatY81AndY82New(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
            if (UtilCollection.isNotEmpty(returnObjectItems)) {
                Map<Long, ErpReturnObjectItem> returnItemMapList = returnObjectItems.stream()
                        .filter(e -> UtilNumber.isNotEmpty(e.getWmsItemId()))
                        .collect(Collectors.toMap(ErpReturnObjectItem::getWmsItemId, Function.identity()));
                for (BizReceiptTransportItemDTO itemDTO : itemListNotSync) {
                    ErpReturnObjectItem returnObjectItem = returnItemMapList.get(itemDTO.getId());
                    if (UtilObject.isNotNull(returnObjectItem)) {
                        // 反写【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                        this.assignmentPostValue(returnObjectItem, itemDTO, insMoveTypeDTO);
                        updateListNotSync.add(itemDTO);
                    }
                    for (BizReceiptTransportItemDTO splitItemDTO : itemDTO.getSplitItemVOList()) {
                        ErpReturnObjectItem returnObjectSplitItem = returnItemMapList.get(splitItemDTO.getId());
                        if (UtilObject.isNotNull(returnObjectSplitItem)) {
                            // 反写【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                            this.assignmentPostValue(returnObjectSplitItem, splitItemDTO, insMoveTypeDTO);
                            updateListNotSync.add(splitItemDTO);
                        }
                    }
                }
                // 更新入库单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                transportMatComponent.updateItem(updateListNotSync);
            }
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新入库单状态 - 已记账
                transportMatComponent.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            } else {
                log.error("入库单{}SAP过账失败", headDTO.getReceiptCode());
                // 更新入库单head、item状态-未同步
                transportMatComponent.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync = headDTO.getItemDTOList().stream().map(BizReceiptTransportItemDTO::getMatDocCode)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    /**
     * 反写【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
     *
     * @param returnObjectItem SAP出参
     * @param itemDTO 行项目、子行项目
     * @param insMoveTypeDTO ins凭证
     */
    private void assignmentPostValue(ErpReturnObjectItem returnObjectItem, BizReceiptTransportItemDTO itemDTO, StockInsMoveTypeDTO insMoveTypeDTO) {
        // 反写凭证信息
        itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(returnObjectItem.getMatDocYear()));
        itemDTO.setMatDocCode(UtilObject.getStringOrEmpty(returnObjectItem.getMatDocCode()));
        itemDTO.setMatDocRid(UtilObject.getStringOrEmpty(returnObjectItem.getMatDocRid()));
        itemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
        // 补全ins凭证
        if (UtilObject.isNotNull(insMoveTypeDTO)) {
            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                if (insDocBatch.getPreReceiptItemId().equals(itemDTO.getId())) {
                    insDocBatch.setMatDocCode(returnObjectItem.getMatDocCode());
                    insDocBatch.setMatDocRid(returnObjectItem.getMatDocRid());
                    insDocBatch.setPostingDate(itemDTO.getPostingDate());
                    insDocBatch.setDocDate(itemDTO.getDocDate());
                    insDocBatch.setMatDocYear(UtilObject.getStringOrEmpty(returnObjectItem.getMatDocYear()));
                }
            }
            for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                if (insDocBin.getPreReceiptItemId().equals(itemDTO.getId())) {
                    insDocBin.setMatDocCode(returnObjectItem.getMatDocCode());
                    insDocBin.setMatDocRid(returnObjectItem.getMatDocRid());
                    insDocBin.setMatDocYear(UtilObject.getStringOrEmpty(returnObjectItem.getMatDocYear()));
                }
            }
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemList 未同步sap入库单行项目
     * @param user     当前用户
     */
    private void setInPostDate(List<BizReceiptTransportItemDTO> itemList, CurrentUser user) {
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        for (BizReceiptTransportItemDTO item : itemList) {
            for (BizReceiptTransportItemDTO splitItem : item.getSplitItemVOList()) {
                splitItem.setDocDate(UtilDate.getNow());
                splitItem.setPostingDate(postingDate);
            }
            item.setDocDate(UtilDate.getNow());
            item.setPostingDate(postingDate);
        }
    }

    /**
     * 填充转码bin信息
     */
    private void setTransTransportBin(BizReceiptTransportHeadDTO headDTO) {
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(headDTO.getMoveTypeId());
        // 未同步sap行项目
        for (BizReceiptTransportItemDTO item : headDTO.getItemDTOList()) {
            if (CollectionUtils.isNotEmpty(item.getBinDTOList())) {
                item.getBinDTOList().stream().forEach(bin -> {
                    bin.setBatchCode(bin.getOutputBatchInfoDTO().getBatchCode());
                    bin.setOutputSpecStock(bin.getOutputBatchInfoDTO().getSpecStock());
                    bin.setOutSpecStock(bin.getOutputSpecStock());
                    bin.setOutputSpecStockCode(bin.getOutputBatchInfoDTO().getSpecStockCode());
                });
            }
            item.setReceiptCode(headDTO.getReceiptCode());
            item.setMoveTypeCode(Const.MOVE_TYPE_Y81 + dicMoveType.getSpecStock());
            for (BizReceiptTransportItemDTO splitItem : item.getSplitItemVOList()) {
                splitItem.setReceiptCode(headDTO.getReceiptCode());
                splitItem.setMoveTypeCode(Const.MOVE_TYPE_Y82 + dicMoveType.getSpecStock());
                if (CollectionUtils.isNotEmpty(splitItem.getBinDTOList())) {
                    splitItem.getBinDTOList().stream().forEach(bin -> {
                        bin.setBatchCode(bin.getInputBatchInfoDTO().getBatchCode());
                        bin.setOutputSpecStock(bin.getOutputBatchInfoDTO().getSpecStock());
                        bin.setOutputSpecStockCode(bin.getOutputBatchInfoDTO().getSpecStockCode());
                    });
                }
            }
        }
    }

}
