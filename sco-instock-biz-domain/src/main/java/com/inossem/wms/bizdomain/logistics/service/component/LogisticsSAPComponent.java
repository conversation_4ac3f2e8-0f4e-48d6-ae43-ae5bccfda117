package com.inossem.wms.bizdomain.logistics.service.component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.inossem.wms.common.model.common.base.BizContext;

@Component
public class LogisticsSAPComponent {

    @Autowired
    private LogisticsComponent logisticsComponent;


    @Transactional
    public void submitBeforePurchase(BizContext ctx){
        // 提交-校验物流清关费用入参
        logisticsComponent.checkSubmitData(ctx);

        // 提交物流清关费用
        logisticsComponent.submitDeliveryNotice(ctx);

        // 保存操作日志
        logisticsComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        logisticsComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        logisticsComponent.saveReceiptTree(ctx);
        

        
    }

    @Transactional
    public void submitAfterPurchase(BizContext ctx){
        // 回写采购单信息
        logisticsComponent.writeBackPurchase(ctx);  

        logisticsComponent.autoApproval(ctx);
    }


}
