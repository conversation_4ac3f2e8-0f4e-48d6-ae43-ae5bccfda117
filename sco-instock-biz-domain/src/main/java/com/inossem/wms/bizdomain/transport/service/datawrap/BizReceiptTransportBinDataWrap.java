package com.inossem.wms.bizdomain.transport.service.datawrap;

import org.springframework.stereotype.Service;

import com.inossem.wms.bizdomain.transport.dao.BizReceiptTransportBinMapper;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportBin;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;

/**
 * <p>
 * 转储单配货明细表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
@Service
public class BizReceiptTransportBinDataWrap extends BaseDataWrap<BizReceiptTransportBinMapper, BizReceiptTransportBin> {

    public void deleteByHeadId(Long headId) {
        this.getBaseMapper().deleteByHeadId(headId);
    }
}
