package com.inossem.wms.bizdomain.transport.service.component;

//import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.print.label.LabelTransportLeisureBox;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 闲置转储
 *
 * <AUTHOR>
 */

@Service
@Slf4j
@Deprecated
public class TransportLeisureComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService bizReceiptAttachmentService;

    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;

    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;

    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    protected BizBatchInfoDataWrap bizBatchInfoDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        headDTO.setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName());
        ButtonVO buttonVO = new ButtonVO();
        // 草稿状态,按钮保存、提交、删除
        buttonVO.setButtonSave(true);
        buttonVO.setButtonSubmit(true);
        buttonVO.setButtonDelete(false);
        // tab页签默认全不启用
        ExtendVO extend = new ExtendVO();
        extend.setWfRequired(false);
        extend.setAttachmentRequired(false);
        extend.setOperationLogRequired(false);
        extend.setRelationRequired(false);
        // 返回空行项目对象
        BizResultVO<BizReceiptTransportHeadDTO> vo = new BizResultVO<>(headDTO, extend, buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 开启审批
     */
    public void setExtendWf(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setWfRequired(false);
    }

    /**
     * 开启附件
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setAttachmentRequired(true);
    }

    /**
     * 开启操作日志
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setOperationLogRequired(true);
    }

    /**
     * 开启单据流
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setRelationRequired(true);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    private void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportHead head = new BizReceiptTransportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportItem::getItemStatus, receiptStatus)
            .eq(BizReceiptTransportItem::getHeadId, id);
        bizReceiptTransportItemDataWrap.update(wrapper);
    }

    /**
     * 查询库存
     */
    public void getStock(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            // 物料编码不是空时, 根据编码查询id
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (UtilNumber.isEmpty(matId)) {
                // 物料不存在
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }
            po.setMatId(matId);
        }
        // 根据单据类型获取特性
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCode(
            EnumReceiptType.LEISURE_APPLY.getValue(), po.getFtyId(), po.getLocationId(), po.getMatId(),
            EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), "");
        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (Collections.isEmpty(assembleDTOList)) {
            // 未查询到库存信息
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        } else {
            if (UtilCollection.isNotEmpty(po.getItemDTOList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptTransportItemDTO itemDTO : po.getItemDTOList()) {
                    for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                        for (BizReceiptAssembleDTO dto : assembleDTOList) {
                            if (dto.getSpecCode().equals(assembleDTO.getSpecCode())
                                && dto.getSpecValue().equals(assembleDTO.getSpecValue())) {
                                dto.setStockQty(dto.getStockQty().subtract(assembleDTO.getQty()));
                            }
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(po.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                            // 存储单元
                            stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                        && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue()))) {
                        stockBinDTOList.add(stockBinDTO);
                    }
                }
            }
            // 返回对象
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
        }
    }

    /**
     * 列表 - 分页
     */
    public void getPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (UtilCollection.isEmpty(currentUser.getLocationList())) {
            return;
        }
        List<Long> locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        po.setLocationIdList(locationIds);
        // 分页查询处理
        IPage<BizReceiptTransportHeadDTO> page = new Page<>(po.getPageIndex(), po.getPageSize());
        bizReceiptTransportHeadDataWrap.getLeisurePageVOList(page, po);

        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 列表 - 没有分页
     */
    public void getList(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询条件设置：单据号精确搜索，状态列表，申请单创建时间范围，创建时间倒序
        QueryWrapper<BizReceiptTransportHead> wrapper = new QueryWrapper<>();
        Date startTime = null;
        Date endTime = null;
        if (UtilObject.isNotNull(po.getApplyStartTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getApplyStartTime());
            endTime = UtilLocalDateTime.getEndTime(po.getApplyEndTime());
        }
        wrapper.lambda()
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportHead::getReceiptCode,
                po.getReceiptCode())
            .in(BizReceiptTransportHead::getReceiptStatus, po.getReceiptStatusList()).between(
                (UtilObject.isNotNull(startTime)), BizReceiptTransportHead::getCreateTime, startTime, endTime);
        // 若无排序则默认按时间倒序
        if (UtilString.isNullOrEmpty(po.getDescSortColumn()) && UtilString.isNullOrEmpty(po.getAscSortColumn())) {
            wrapper.lambda().orderByDesc(BizReceiptTransportHead::getCreateTime);
        }
        // 查询列表
        List<BizReceiptTransportHead> bizReceiptTransportHeadList =
            bizReceiptTransportHeadDataWrap.list(wrapper);
        // 转dto
        List<BizReceiptTransportHeadDTO> dtoList =
            UtilCollection.toList(bizReceiptTransportHeadList, BizReceiptTransportHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dtoList));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHead bizReceiptTransportHead =
            bizReceiptTransportHeadDataWrap.getById(headId);
        BizReceiptTransportHeadDTO bizReceiptTransportHeadDTO =
            UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        // 填充属性
        dataFillService.fillAttr(bizReceiptTransportHeadDTO);
        // 查询库存数量
        if (UtilCollection.isNotEmpty(bizReceiptTransportHeadDTO.getItemDTOList())) {
            BizReceiptTransportItemDTO itemDTO = bizReceiptTransportHeadDTO.getItemDTOList().get(0);
            BizReceiptAssembleRuleDTO bizReceiptAssembleRuleDTO =
                stockCommonService.getStockByFeatureCode(bizReceiptTransportHeadDTO.getReceiptType(), // 发出方工厂id
                    itemDTO.getOutputFtyId(), // 发出方库存地点id
                    itemDTO.getOutputLocationId(), // itemDTO.getOutputMatId(), //发出方物料id
                    null, // 非限制
                    EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), Const.STRING_EMPTY);
            List<BizReceiptAssembleDTO> assembleDTOList = bizReceiptAssembleRuleDTO.getAssembleDTOList();
            Map<Long, List<BizReceiptAssembleDTO>> matQtyMap =
                assembleDTOList.stream().collect(Collectors.groupingBy(BizReceiptAssembleDTO::getMatId));
            for (BizReceiptTransportItemDTO dto : bizReceiptTransportHeadDTO.getItemDTOList()) {
                if (matQtyMap.containsKey(dto.getOutputMatId())) {
                    dto.setStockQty(matQtyMap.get(dto.getOutputMatId()).stream().map(BizReceiptAssembleDTO::getStockQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        }
        // 按钮
        Integer receiptStatus = bizReceiptTransportHeadDTO.getReceiptStatus();
        // 草稿状态与已完成状态都可删除，被调拨出库或调拨入库引用的申请单不可以删除
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonSave(true);
            buttonVO.setButtonSubmit(true);
            buttonVO.setButtonDelete(true);
        }
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            QueryWrapper<BizReceiptTransportItem> wrapper = new QueryWrapper<>();
            // 这里其实应该用Head表里的preReceiptHeadId判断的，但是保存出库、入库单时preReceiptHeadId没传给我，所以才用preReceiptItemId判断的
            wrapper.lambda().in(BizReceiptTransportItem::getPreReceiptItemId, bizReceiptTransportHeadDTO
                .getItemDTOList().stream().map(BizReceiptTransportItemDTO::getId).collect(Collectors.toList()));
            List<BizReceiptTransportItem> bizReceiptTransportItemList = bizReceiptTransportItemDataWrap.list(wrapper);
            if (UtilCollection.isEmpty(bizReceiptTransportItemList)) {
                // 已完成状态并且不被后续引用，可以删除
                buttonVO.setButtonDelete(true);
            }
        }
        buttonVO.setButtonDelete(false);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(bizReceiptTransportHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 校验数据
     */
    public void check(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (headDTO == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // head处理
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(id)) {
            // 根据id更新
            bizReceiptTransportHeadDataWrap.updateDtoById(headDTO);
            // 特征表物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(id);
            // item物理删除
            bizReceiptTransportItemDataWrap.deleteByHeadId(id);
            // 单据日志 - 修改
            receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_SAVE, "", ctx.getCurrentUser().getId());
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LEISURE_TRANSPORT.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptTransportHeadDataWrap.saveDto(headDTO);
            id = headDTO.getId();
            // 单据日志 - 新增
            receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH, "", ctx.getCurrentUser().getId());
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, code);
        // item处理
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(id);
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(ctx.getCurrentUser().getId());
        }
        bizReceiptTransportItemDataWrap.saveBatchDto(itemDTOList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                bizReceiptAssembleDTO.setReceiptType(headDTO.getReceiptType());
                bizReceiptAssembleDTO.setReceiptHeadId(id);
                bizReceiptAssembleDTO.setReceiptItemId(itemDto.getId());
                bizReceiptAssembleDTO.setId(null);
                bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                    ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                    : bizReceiptAssembleDTO.getSpecType());
                assembleDTOList.add(bizReceiptAssembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(itemDto.getHeadId());
                        labelReceiptRel.setReceiptItemId(itemDto.getId());
                        labelReceiptRel.setReceiptBinId(bizReceiptAssembleDTO.getId());
                        bizLabelReceiptRelList.add(labelReceiptRel);
                        labelRelIdList.add(labelReceiptRelDTO.getId());
                    }
                }
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizReceiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 逻辑删除附件
        bizReceiptAttachmentService.deleteBizReceiptAttachment(headId, headDTO.getReceiptType());
    }

    /**
     * 状态变更已完成
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        List<Long> itemIds = new ArrayList<>();
        for (BizReceiptTransportItemDTO itemDto : headDTO.getItemDTOList()) {
            List<Long> binIds = new ArrayList<>();
            if (UtilCollection.isNotEmpty(itemDto.getAssembleDTOList())) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                    binIds.add(bizReceiptAssembleDTO.getId());
                }
            }
            bizReceiptAssembleDataWrap.removeByIds(binIds);
            itemIds.add(itemDto.getId());
        }
        bizReceiptTransportItemDataWrap.removeByIds(itemIds);
        bizReceiptTransportHeadDataWrap.removeById(headId);
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        this.addLeisureMark(ctx);
    }

    /**
     * 添加闲置标记
     */
    public void addLeisureMark(BizContext ctx) {
        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizBatchInfo> list = new ArrayList<>();
        CurrentUser cUser = ctx.getCurrentUser();
        // 更新状态
        for (BizReceiptTransportItemDTO item : headDTO.getItemDTOList()) {
            BizBatchInfo batchInfo = new BizBatchInfo();
            batchInfo.setId(item.getBinDTOList().get(0).getOutputBatchId());
            batchInfo.setMatId(item.getOutputMatId());
            batchInfo.setFtyId(item.getOutputFtyId());
            list.add(batchInfo);
        }
        bizBatchInfoDataWrap.updateBatchSpecStockToLeisure(list,Const.IS_LEISURE,cUser.getId());
    }

    /**
     * 去除闲置标记
     */
    public void removeLeisureMark(BizContext ctx) {
        List<BizBatchInfo> list = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser cUser = ctx.getCurrentUser();
        // 更新状态
        if(Collections.isEmpty(list)){
            log.warn("去除闲置标记错误！数据不能为空！");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        bizBatchInfoDataWrap.updateBatchSpecStockToLeisure(list,Const.IS_NOT_LEISURE,cUser.getId());
    }

    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }


    /**
     * 填充打印数据
     * @param ctx
     */
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptTransportHeadDTO headDTO = (BizReceiptTransportHeadDTO) po.getHeadDTO();
        // 新建领料申请打印实体对象
        List<BizReceiptTransportItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<LabelTransportLeisureBox> transportLeisureBoxes = new ArrayList<>();
        itemDTOList.forEach(itemDTO->{
            LabelTransportLeisureBox labelTransportLeisureBox = UtilBean.newInstance(itemDTO, LabelTransportLeisureBox.class);
            transportLeisureBoxes.add(labelTransportLeisureBox);
        });
        // 填充打印信息
        printInfo.setLabelBoxList(transportLeisureBoxes);
        if (UtilCollection.isNotEmpty(transportLeisureBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,transportLeisureBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_TRANSPORT_LEISURE_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptTransportHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptTransportHead bizReceiptTransportHead = bizReceiptTransportHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptTransportHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptTransportHeadDTO headDTO = UtilBean.newInstance(bizReceiptTransportHead, BizReceiptTransportHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

}
