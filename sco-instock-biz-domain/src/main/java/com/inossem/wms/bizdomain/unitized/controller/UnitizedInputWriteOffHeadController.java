package com.inossem.wms.bizdomain.unitized.controller;


import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedInspectInputWriteOffService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputWaybillDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.po.SignatureImgInfoPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
//import sun.misc.BASE64Decoder;
import org.apache.commons.codec.binary.Base64;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <p>
 * 成套设备入库单抬头表 前端控制器
 * </p>
 */
@RestController
public class UnitizedInputWriteOffHeadController {
    
    @Autowired
    UnitizedInspectInputWriteOffService unitizedInspectInputWriteOffService;

    /**
     * 入库冲销-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "入库冲销-初始化", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        unitizedInspectInputWriteOffService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 入库冲销-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 入库冲销单分页
     */
    @ApiOperation(value = "入库冲销-分页", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 入库冲销-详情
     *
     * @param id 入库冲销主键
     * @param ctx 入参上下文 {"id":"入库冲销主键"}
     */
    @ApiOperation(value = "入库冲销-详情", tags = {"入库管理-入库冲销"})
    @GetMapping(value = "/unitized/input/inputs-write-off/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedInspectInputWriteOffService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 入库冲销-查询入库单
     *
     * @param po
     * @param ctx 入参上下文 {"id":"入库冲销主键"}
     */
    @ApiOperation(value = "入库冲销-详情", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/get-inspect-input", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptInputHeadDTO>> getInsepctInput(@RequestBody BizReceiptInputSearchPO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.getInsepctInput(ctx);
        List<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }
    /**
     * 入库冲销-前续单据
     *
     * @param po 查询条件
     */
    @ApiOperation(value = "入库冲销-前续单据", tags = {"入库冲销-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/fillEntity", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> fillEntity(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.fillEntity(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 入库冲销-保存
     *
     * @param po 保存入库冲销表单参数
     * @param ctx 入参上下文 {"po":"保存入库冲销表单参数"}
     */
    @ApiOperation(value = "入库冲销-保存", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 入库冲销-提交
     *
     * @param po 提交入库冲销表单参数
     * @param ctx 入参上下文 {"po":"提交入库冲销表单参数"}
     */
    @ApiOperation(value = "入库冲销-提交", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_SUBMIT_SUCCESS, code);
    }

    /**
     * 入库冲销-配货
     *
     */
    @ApiOperation(value = "入库冲销-配货", tags = {"入库管理-入库冲销"})
    @GetMapping(value = "/unitized/input/inputs-write-off/distribution/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInputWaybillDTO> distribution(@PathVariable("id") Long id, BizContext ctx) {
        unitizedInspectInputWriteOffService.distribution(ctx);
        BizReceiptInputWaybillDTO itemDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(itemDTO);
    }

    /**
     * 入库冲销-过账
     *
     * @param po 保存入库冲销表单参数
     * @param ctx 入参上下文 {"po":"保存入库冲销表单参数"}
     */
    @ApiOperation(value = "入库冲销-过账", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> post(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 入库冲销-冲销
     *
     * @param po 入库冲销冲销表单参数
     * @param ctx 入参上下文 {"po":"入库冲销冲销表单参数"}
     */
    @ApiOperation(value = "入库冲销-冲销", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInputWriteOffPO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 入库冲销单-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "入库冲销单-删除", tags = {"入库管理-入库冲销"})
    @DeleteMapping("/unitized/input/inputs-write-off/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        unitizedInspectInputWriteOffService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_DELETE_SUCCESS, po.getReceiptCode());
    }


    /**
     * 入库冲销单-物料标签打印-PDA
     * @auth zhibo
     * @param po 打印入参
     * @param ctx 请求上下文
     * @return 领料申请单数据
     */
    @ApiOperation(value = "入库冲销单-物料标签打印-PDA", tags = {"入库管理-入库冲销"})
    @PostMapping(value = "/unitized/input/inputs-write-off/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptInputHeadDTO> po, BizContext ctx) {
        unitizedInspectInputWriteOffService.boxApplyLabelPrint(ctx);
        List<LabelReceiptInputBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }



    /**
     * 签名信息-保存
     *
     * @param po 保存签名信息
     * @param
     */
    @ApiOperation(value = "签名信息-保存", tags = {"签名信息-保存"})
    @PostMapping(value = "/unitized/input/inputs-write-off/save/signature", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> signatureImgUpload(@RequestBody List<SignatureImgInfoPO> po, BizContext ctx) {
        List<SignatureImgInfoPO> poData = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        System.out.println("=="+poData.toString()+"==");
        String imgFilePath = "D:\\work\\img";
        // 获取图片的base64
        List<String> base64List = po.stream().map(SignatureImgInfoPO::getImg).collect(Collectors.toList());
        base64List.forEach(item -> getImg(item, imgFilePath));
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_INPUT_DELETE_SUCCESS, null);
    }

    private static boolean getImg(String imgBase, String imgFilePath) {
        if (imgBase == null) // 图像数据为空
            return false;
//        BASE64Decoder decoder = new BASE64Decoder();
        // 前台在用Ajax传base64值的时候会把base64中的+换成空格，所以需要替换回来。
        String baseValue = imgBase.replaceAll(" ", "+");
        try {
            // Base64解码
            //去除base64中无用的部分
//            byte[] b = decoder.decodeBuffer(baseValue.replace("data:image/jpeg;base64,", ""));
            byte[] b = Base64.decodeBase64(baseValue.replace("data:image/jpeg;base64,", ""));
            imgBase = imgBase.replace("base64,", "");
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            // 生成jpg图片
            Random random = new Random();
            OutputStream out = new FileOutputStream(new File(imgFilePath.toString()+"\\"+ random.nextInt(1000000) +".jpg"));
            out.write(b);
            out.flush();
            out.close();
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    /**
     * 入库冲销-详情
     *
     * @param id 入库冲销主键
     * @param ctx 入参上下文 {"id":"入库冲销主键"}
     */
    @ApiOperation(value = "入库冲销-打印详情", tags = {"入库管理-入库冲销"})
    @GetMapping(value = "/unitized/input/inputs-write-off/print/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getPrintInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedInspectInputWriteOffService.getPrintInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}
