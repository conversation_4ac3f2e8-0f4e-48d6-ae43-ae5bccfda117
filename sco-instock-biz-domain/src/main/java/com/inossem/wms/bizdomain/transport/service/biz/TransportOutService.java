package com.inossem.wms.bizdomain.transport.service.biz;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import com.inossem.wms.bizdomain.transport.service.component.TransportOutComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportOutService {

    @Autowired
    private TransportOutComponent transportOutComponent;

    /**
     * 页面初始化
     */
    @Entrance(call = {"transportOutComponent#init", "transportOutComponent#setExtendWf",
        "transportOutComponent#setExtendAttachment", "transportOutComponent#setExtendOperationLog",
        "transportOutComponent#setExtendRelation"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportOutComponent.init(ctx);

        // 开启审批
        transportOutComponent.setExtendWf(ctx);

        // 开启附件
        transportOutComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportOutComponent.setExtendOperationLog(ctx);

        // 开启单据流
        transportOutComponent.setExtendRelation(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transportOutComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportOutComponent.getStock(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transportOutComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportOutComponent.getPage(ctx);
    }

    /**
     * 列表 - 没有分页
     */
    @Entrance(call = {"transportOutComponent#getList"})
    public void getList(BizContext ctx) {

        // 列表 - 没有分页
        transportOutComponent.getList(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transportOutComponent#getInfo", "transportOutComponent#setExtendAttachment",
        "transportOutComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 详情，包含按钮组和扩展功能
        transportOutComponent.getInfo(ctx);

        // 开启附件
        transportOutComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportOutComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transportOutComponent#check", "transportOutComponent#setAssembleInputBatchId",
        "transportOutComponent#save", "transportOutComponent#saveBizReceiptAttachment",
        "transportOutComponent#saveBizReceiptOperationLog"})
    public void save(BizContext ctx) {

        // 校验数据
        transportOutComponent.check(ctx);

        // 【先过账模式】设置批次id-用于生成接收方批次
        transportOutComponent.setAssembleInputBatchId(ctx);

        // 保存
        transportOutComponent.save(ctx);

        // 保存附件
        transportOutComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportOutComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 提交
     */
    @Entrance(call = {"transportOutComponent#check", "transportOutComponent#setAssembleInputBatchId",
        "transportOutComponent#submit", "transportOutComponent#occupyStock",
        "transportOutComponent#saveBizReceiptAttachment", "transportOutComponent#saveReceiptTree",
        "transportOutComponent#saveBizReceiptOperationLog", "transportOutComponent#updateStatusSubmitted",
        "transportOutComponent#saveOutputBinByMoveType", "transportOutComponent#generateNoTaskInsDocToPost",
        "transportOutComponent#generateInsDocToPostByAssemble", "transportOutComponent#checkAndComputeForModifyStock",
        "transportOutComponent#updateStatusUnsync", "transportOutComponent#post", "transportOutComponent#modifyStock",
        "transportOutComponent#modifyLabel", "transportOutComponent#updateStatusPosted",
        "transportOutComponent#updateStatusCompleted", "transportOutComponent#generateOutputTaskReq"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 校验数据
        transportOutComponent.check(ctx);

        // 【先过账模式】设置批次id-用于生成接收方批次
        transportOutComponent.setAssembleInputBatchId(ctx);

        // 提交
        transportOutComponent.submit(ctx);

        // 保存附件
        transportOutComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        transportOutComponent.saveReceiptTree(ctx);

        // 保存操作日志
        transportOutComponent.saveBizReceiptOperationLog(ctx);

        // 【先过账模式】根据assemble生成ins凭证
        transportOutComponent.generateInsDocToPostByAssemble(ctx);

        // 过账前校验和数量计算
        transportOutComponent.checkAndComputeForModifyStock(ctx);

        // 状态变更-未同步
        transportOutComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transportOutComponent.post(ctx);

        // 修改库存
        transportOutComponent.modifyStock(ctx);

        // 修改标签
        transportOutComponent.modifyLabel(ctx);

        // 【先过账模式】状态变更-已记账
        transportOutComponent.updateStatusPosted(ctx);

        // 下架推送
        transportOutComponent.generateOutputTaskReq(ctx);
    }

    /**
     * 过账
     */
    @Entrance(call = {"transportOutComponent#saveOutputBinByMoveType", "transportOutComponent#generateInsDocToPost",
        "transportOutComponent#generateNoTaskInsDocToPost", "transportOutComponent#generateInsDocToPostByAssemble",
        "transportOutComponent#checkAndComputeForModifyStock", "transportOutComponent#updateStatusUnsync",
        "transportOutComponent#post", "transportOutComponent#modifyStock", "transportOutComponent#deleteOccupyStock",
        "transportOutComponent#updateStatusCompleted", "transportOutComponent#updateStatusPosted"})
    public void post(BizContext ctx) {

        // 【先过账模式】根据assemble生成ins凭证
        transportOutComponent.generateInsDocToPostByAssemble(ctx);

        // 过账前校验和数量计算
        transportOutComponent.checkAndComputeForModifyStock(ctx);

        // 状态变更-未同步
        transportOutComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transportOutComponent.post(ctx);

        // 修改库存
        transportOutComponent.modifyStock(ctx);

        // 状态变更-已完成
        transportOutComponent.updateStatusCompleted(ctx);

        // 【先过账模式】状态变更-已记账
        transportOutComponent.updateStatusPosted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transportOutComponent#checkTaskStatus", "transportOutComponent#delete",
        "transportOutComponent#deleteOccupyStock", "transportOutComponent#deleteBizReceiptAttachment",
        "transportOutComponent#cancelTaskRequest"})
    public void delete(BizContext ctx) {

        // 删除
        transportOutComponent.delete(ctx);

        // 逻辑删除附件
        transportOutComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 下架完成后推送MQ,接收到信息后处理
     */
    @Entrance(call = {"transportOutComponent#saveOutputBinByTask", "transportOutComponent#updateUnloadQty",
        "transportOutComponent#updateStatusTaskByUnloadQty", "transportOutComponent#checkCanPost", "this#post",
        "transportOutComponent#updateStatusCompleted"})
    @WmsMQListener(tags = TagConst.TASK_TRANSPORT_OUT_UNLOAD_CALLBACK)
    public void generatePost(BizContext ctx) {

        // 根据下架作业单生成bin表
        transportOutComponent.saveOutputBinByTask(ctx);

        // 修改item上的已下架数量
        transportOutComponent.updateUnloadQty(ctx);

        // 根据已下架数量判断修改单据状态-已下架作业
        transportOutComponent.updateStatusTaskByUnloadQty(ctx);

        // 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
        transportOutComponent.checkCanPost(ctx);

        // 状态变更-已完成
        transportOutComponent.updateStatusCompleted(ctx);
    }

    /**
     * 冲销
     */
    @Entrance(call = {"transportOutComponent#checkWriteOffData", "transportOutComponent#generateInsDocToPost",
        "transportOutComponent#generateNoTaskInsDocToPost",
        "transportOutComponent#generateInsDocToPostWriteOffByAssemble",
        "transportOutComponent#checkAndComputeForModifyStock", "transportOutComponent#writeOff",
        "transportOutComponent#modifyStock", "transportOutComponent#modifyLabel",
        "transportOutComponent#addWriteOffRequest", "transportOutComponent#updateStatusWriteOff"})
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 冲销时校验数据
        transportOutComponent.checkWriteOffData(ctx);

        // 【先过账模式】生成ins凭证-冲销
        transportOutComponent.generateInsDocToPostWriteOffByAssemble(ctx);

        // 过账前校验和数量计算
        transportOutComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账-冲销
        transportOutComponent.writeOff(ctx);

        // 修改库存
        transportOutComponent.modifyStock(ctx);

        // 修改标签
        transportOutComponent.modifyLabel(ctx);

        // 推送冲销修改请求
        transportOutComponent.addWriteOffRequest(ctx);

        // 行项目状态变更-已冲销
        transportOutComponent.updateStatusWriteOff(ctx);
    }

    /**
     * 调拨出库-前续单据【调拨申请】
     */
    @Entrance(call = {"transportOutComponent#getReferReceiptItemList"})
    public void getReferReceiptItemList(BizContext ctx) {

        // 调拨出库-前续单据【调拨申请】
        transportOutComponent.getReferReceiptItemList(ctx);
    }

    /**
     * 过门后推送MQ
     */
    @Entrance(call = {"transportOutComponent#updateFinishQty", "transportOutComponent#checkCanPost",
        "transportOutComponent#generatePalletSorting", "this#post", "transportOutComponent#updateStatusCompleted"})
    public void saveUnloadByPassDoorMq(BizContext ctx) {

        // 修改item上的发完成数量
        transportOutComponent.updateFinishQty(ctx);

        // 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
        transportOutComponent.checkCanPost(ctx);

        // 生成接收方码盘数据,关联表
        transportOutComponent.generatePalletSorting(ctx);

        // 状态变更-已完成
        transportOutComponent.updateStatusCompleted(ctx);
    }

}