package com.inossem.wms.bizdomain.logistics.service.biz;

import com.inossem.wms.common.enums.EnumReceiptStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.inossem.wms.bizdomain.logistics.service.component.LogisticsComponent;
import com.inossem.wms.bizdomain.logistics.service.component.LogisticsSAPComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 物流清关费用
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */

@Service
@Slf4j
public class LogisticsService {

    @Autowired
    private LogisticsComponent logisticsComponent;

    @Autowired
    private LogisticsSAPComponent logisticsSAPComponent;

    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @return 库存地点下拉框
     */
    public void getLocationList(BizContext ctx) {

        // 查询库存地点-根据选中的工厂id获取
        logisticsComponent.getLocationList(ctx);

    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_DELIVERY_NOTICE_APPLY)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        logisticsComponent.approvalCallback(wfReceiptCo);

    }

    /**
     * 物流清关费用-初始化
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"deliveryNoticeComponent#setInit", "deliveryNoticeComponent#setExtendWf",
        "deliveryNoticeComponent#setExtendAttachment", "deliveryNoticeComponent#setExtendOperationLog",
        "deliveryNoticeComponent#setExtendRelation"})
    public void init(BizContext ctx) {

        // 页面初始化:
        // 1、设置物流清关费用【单据类型、创建时间、创建人】
        // 2、设置按钮权限【提交、保存】
        logisticsComponent.setInit(ctx);

        // 开启附件
        logisticsComponent.setExtendAttachment(ctx);

        // 开启操作日志
        logisticsComponent.setExtendOperationLog(ctx);

        // 开启操单据流
        logisticsComponent.setExtendRelation(ctx);

    }

    /**
     * 物流清关费用-分页
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"deliveryNoticeComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 物流清关费用单-分页
        logisticsComponent.getPage(ctx);
    }

    /**
     * 物流清关费用-详情
     *
     * @param ctx 入参上下文 {"id":"物流清关费用主键"}
     */
    @Entrance(call = {"deliveryNoticeComponent#getInfo", "deliveryNoticeComponent#setExtendWf",
        "deliveryNoticeComponent#setExtendAttachment", "deliveryNoticeComponent#setExtendOperationLog",
        "deliveryNoticeComponent#setExtendRelation"})
    public void getInfo(BizContext ctx) {

        // 物流清关费用单-详情
        logisticsComponent.getInfo(ctx);

        // 开启附件
        logisticsComponent.setExtendAttachment(ctx);

        // 开启操作日志
        logisticsComponent.setExtendOperationLog(ctx);

        // 开启操单据流
        logisticsComponent.setExtendRelation(ctx);


    }


    /**
     * 打印数据详情
     * @param ctx
     */
    public void getPrintInfo(BizContext ctx) {
        logisticsComponent.arrangePrintInfo(ctx);
    }

    /**
     * 物流清关费用-保存
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"deliveryNoticeComponent#checkSaveData", "deliveryNoticeComponent#saveDeliveryNotice",
        "deliveryNoticeComponent#saveBizReceiptOperationLog", "deliveryNoticeComponent#saveBizReceiptAttachment",
        "deliveryNoticeComponent#saveReceiptTree"})
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存-校验物流清关费用入参
        logisticsComponent.checkSaveData(ctx);

        // 保存-物流清关费用单
        logisticsComponent.saveLogistics(ctx);

        // 保存操作日志
        logisticsComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        logisticsComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        logisticsComponent.saveReceiptTree(ctx);
    }

    /**
     * 物流清关费用-提交
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"deliveryNoticeComponent#checkSubmitData", "deliveryNoticeComponent#submitDeliveryNotice",
        "deliveryNoticeComponent#saveBizReceiptOperationLog", "deliveryNoticeComponent#saveBizReceiptAttachment",
        "deliveryNoticeComponent#saveReceiptTree"})
    public void submit(BizContext ctx) {

        logisticsSAPComponent.submitBeforePurchase(ctx);

        // 生成采购单
        logisticsComponent.createPurchaseSap(ctx);

        logisticsSAPComponent.submitAfterPurchase(ctx);

    
    }

    /**
     * 物流清关费用-删除
     *
     * @param ctx 入参上下文{"id":"主表id"}
     */
    @Entrance(
        call = {"deliveryNoticeComponent#checkDeleteDeliveryNotice", "deliveryNoticeComponent#deleteDeliveryNotice",
            "deliveryNoticeComponent#deleteReceiptTree", "deliveryNoticeComponent#deleteReceiptAttachment"})
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 删除前校验
        logisticsComponent.checkDeleteDeliveryNotice(ctx);

        // 删除物流清关费用单
        logisticsComponent.deleteDeliveryNotice(ctx);

        // 删除单据流
        logisticsComponent.deleteReceiptTree(ctx);

        // 删除单据附件
        logisticsComponent.deleteReceiptAttachment(ctx);
    }

    /**
     * 物流清关费用-前续单据
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"deliveryNoticeComponent#purchaseReceipt"})
    public void getReferReceiptItemList(BizContext ctx) {

        // 添加物料查询-基于采购订单
        logisticsComponent.purchaseReceipt(ctx);
    }

    /**
     * 物流清关费用-单据复制
     *
     * @param ctx 入参上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void copy(BizContext ctx) {

        // 单据复制
        logisticsComponent.copyReceipt(ctx);

        // 保存操作日志
        logisticsComponent.saveBizReceiptOperationLog(ctx);

        // 保存附件
        logisticsComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        logisticsComponent.saveReceiptTree(ctx);

    }

    public void importCase(BizContext ctx) {
        logisticsComponent.importCase(ctx);
    }

    /*    *//**
     * 物流清关费用-完成物流清关费用
     *
     * @param ctx 入参上下文
     *//*
    @Entrance(call = {"deliveryNoticeComponent#checkFinishDelivery", "deliveryNoticeComponent#finishDelivery"})
    public void finishDelivery(BizContext ctx) {

        // 完成物流清关费用前校验
        deliveryNoticeComponent.checkFinishDelivery(ctx);

        // 完成物流清关费用
        deliveryNoticeComponent.finishDelivery(ctx);
    }*/

    public void revokeLogistics(BizContext ctx){
        
        // 撤销采购单校验
        logisticsComponent.checkRevokePurchase(ctx);

        //删除采购订单
        logisticsComponent.revokePurchase(ctx);
        // 关闭
        // logisticsComponent.closeLogistics(ctx);

        // 清除采购信息
        logisticsComponent.clearPurchaseInfo(ctx);

        // 更新状态草稿
        logisticsComponent.updateStatus(ctx, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
    }

    public void closeLogistics(BizContext ctx){
        // 删除采购订单
        logisticsComponent.closePurchase(ctx);
        // 关闭
        // logisticsComponent.closeLogistics(ctx);


        // 关闭采购单后回写合同信息
        logisticsComponent.writeBackContract(ctx);

        // 更新状态已完成
        logisticsComponent.updateStatus(ctx, EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());
    }

    public void postPurchase(BizContext ctx){
        logisticsComponent.createPurchaseSap(ctx);
        logisticsSAPComponent.submitAfterPurchase(ctx);
    }
    
}
