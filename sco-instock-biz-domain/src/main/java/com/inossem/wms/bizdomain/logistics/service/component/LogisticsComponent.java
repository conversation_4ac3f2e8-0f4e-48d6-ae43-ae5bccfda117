package com.inossem.wms.bizdomain.logistics.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizdomain.contract.service.component.ContractComponent;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractItemDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.logistics.service.datawrap.BizReceiptLogisticsCaseRelDataWrap;
import com.inossem.wms.bizdomain.logistics.service.datawrap.BizReceiptLogisticsHeadDataWrap;
import com.inossem.wms.bizdomain.logistics.service.datawrap.BizReceiptLogisticsItemDataWrap;
import com.inossem.wms.bizdomain.logistics.service.datawrap.BizReceiptLogisticsTaxDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.bizdomain.suppliercase.service.component.SupplierCaseComponent;
import com.inossem.wms.bizdomain.suppliercase.service.datawrap.BizReceiptSupplierCaseHeadDataWrap;
import com.inossem.wms.bizdomain.suppliercase.service.datawrap.BizReceiptSupplierCaseItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.enums.contract.EnumContractTaxRate;
import com.inossem.wms.common.enums.sap.EnumPurchaseOrderType;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemQtyDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractItem;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsHeadDTO;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsItemDTO;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsTaxDTO;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsCaseRel;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsHead;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsItem;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsTax;
import com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsCaseRelImport;
import com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsDeletePO;
import com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsSearchPO;
import com.inossem.wms.common.model.bizdomain.logistics.vo.BizReceiptLogisticsListVo;
import com.inossem.wms.common.model.bizdomain.logistics.vo.BizReceiptLogisticsPreHeadVo;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.model.sap.purchase.*;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 物流清关费用组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */

@Service
@Slf4j
public class LogisticsComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private BizReceiptLogisticsHeadDataWrap bizReceiptLogisticsHeadDataWrap;

    @Autowired
    private BizReceiptLogisticsItemDataWrap bizReceiptLogisticsItemDataWrap;

    @Autowired
    protected PurchaseReceiptService purchaseReceiptService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected DicStockLocationDataWrap dicStockLocationDataWrap;

    @Autowired
    protected BizReceiptLogisticsCaseRelDataWrap bizReceiptLogisticsCaseRelDataWrap;


    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    protected ApprovalService approvalService;
    
    @Autowired 
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected ErpPurchaseReceiptHeadDataWrap erpPurchaseReceiptHeadDataWrap;

    @Autowired
    private UserService userService;

    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;

    @Autowired
    private BizReceiptContractItemDataWrap bizReceiptContractItemDataWrap;

    @Autowired
    private ContractComponent contractComponent;

    @Autowired
    private BizReceiptSupplierCaseHeadDataWrap supplierCaseHeadDataWrap;

    @Autowired
    private SupplierCaseComponent supplierCaseComponent;
    
    @Autowired
    private BizReceiptSupplierCaseItemDataWrap supplierCaseItemDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap deliveryNoticeHeadDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap deliveryNoticeItemDataWrap;

    @Autowired
    private BizReceiptRegisterItemDataWrap registerItemDataWrap;

    @Autowired
    private BizReceiptRegisterHeadDataWrap registerHeadDataWrap;

    @Autowired
    private BizReceiptInspectItemDataWrap inspectItemDataWrap;

    @Autowired
    private BizReceiptInspectHeadDataWrap inspectHeadDataWrap;

    @Autowired
    private BizReceiptLogisticsTaxDataWrap bizReceiptLogisticsTaxDataWrap;  

    @Autowired
    private BizReceiptInconformityHeadDataWrap inconformityHeadDataWrap;

    @Autowired
    private BizReceiptInputHeadDataWrap inputHeadDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap inputItemDataWrap;


    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @out ctx 出参 {@link MultiResultVO <> ("dicCarTypeDataWrap.list()":"库存地点下拉框")}
     */
    public void getLocationList(BizContext ctx) {
        // 从上下文获取工厂id
        Long ftyId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 查询库存地点下拉
        QueryWrapper<DicStockLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicStockLocation::getFtyId, ftyId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dicStockLocationDataWrap.list(queryWrapper)));
    }

    /**
     * 页面初始化: 1、设置物流清关费用【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"物流清关费用","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptLogisticsHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptLogisticsHeadDTO().setReceiptType(EnumReceiptType.LOGISTICS.getValue())
                .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                    .setCreateUserDeptName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptName()).setPurchaseType(Const.ONE),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true).setButtonDeliveryNoticeApprove(this.checkUserFactory(ctx.getCurrentUser())));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptLogisticsHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptLogisticsHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 开启操单据流
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启单据流")}
     */
    public void setExtendRelation(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptLogisticsHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 物流清关费用单-分页
     *
     * @in ctx 入参 {@link BizReceiptLogisticsSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO <BizReceiptDeliveryNoticeHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptLogisticsSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptLogisticsSearchPO> wrapper = this.setQueryWrapper(po,user);
        // 分页处理
        IPage<BizReceiptLogisticsListVo> page = po.getPageObj(BizReceiptLogisticsListVo.class);
        // 物流清关费用单-分页
        bizReceiptLogisticsHeadDataWrap.getDeliveryNoticePageVo(page, wrapper);
        // 转dto
        List<BizReceiptLogisticsHeadDTO> dtoList =
            UtilCollection.toList(page.getRecords(), BizReceiptLogisticsHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        // 设置物流清关费用分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 物流清关费用单-详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"物流清关费用单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取物流清关费用单
        BizReceiptLogisticsHead deliveryNoticeHead = bizReceiptLogisticsHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptLogisticsHeadDTO deliveryNoticeHeadDTO =
            UtilBean.newInstance(deliveryNoticeHead, BizReceiptLogisticsHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(deliveryNoticeHeadDTO);
        // 物流清关费用填充固定资产物料描述属性


        //查询税费
        BizReceiptLogisticsTax tax = bizReceiptLogisticsTaxDataWrap.getOne(new QueryWrapper<BizReceiptLogisticsTax>().eq("head_id", headId));
        if (UtilObject.isNotNull(tax)) { 
            BizReceiptLogisticsTaxDTO taxDTO = UtilBean.newInstance(tax, BizReceiptLogisticsTaxDTO.class);
            deliveryNoticeHeadDTO.setTaxDTO(taxDTO);
        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(deliveryNoticeHeadDTO, ctx.getCurrentUser());

        // 设置物流清关费用单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(deliveryNoticeHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 封装打印数据
     * @param ctx
     */
    public void arrangePrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取物流清关费用单
        BizReceiptLogisticsHead deliveryNoticeHead = bizReceiptLogisticsHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptLogisticsHeadDTO deliveryNoticeHeadDTO =
                UtilBean.newInstance(deliveryNoticeHead, BizReceiptLogisticsHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(deliveryNoticeHeadDTO);

        //查询税费
        BizReceiptLogisticsTax tax = bizReceiptLogisticsTaxDataWrap.getOne(new QueryWrapper<BizReceiptLogisticsTax>().eq("head_id", headId));
        if (UtilObject.isNotNull(tax)) { 
            BizReceiptLogisticsTaxDTO taxDTO = UtilBean.newInstance(tax, BizReceiptLogisticsTaxDTO.class);
            deliveryNoticeHeadDTO.setTaxDTO(taxDTO);
        }

        // 属性填充
        deliveryNoticeHeadDTO.setPurchaseUserCode(deliveryNoticeHeadDTO.getItemList().get(0).getPurchaseUserCode())
                .setPurchaseUserName(deliveryNoticeHeadDTO.getItemList().get(0).getPurchaseUserName())
                .setContractCode(deliveryNoticeHeadDTO.getItemList().get(0).getContractCode())
                .setContractName(deliveryNoticeHeadDTO.getItemList().get(0).getContractName())
                .setSupplierCode(deliveryNoticeHeadDTO.getItemList().get(0).getSupplierCode())
                .setSupplierName(deliveryNoticeHeadDTO.getItemList().get(0).getSupplierName())
                .setPurchaseManagerCode(deliveryNoticeHeadDTO.getCreateUserCode())
                .setPurchaseManagerName(deliveryNoticeHeadDTO.getCreateUserName())
                .setApplyUserCode(deliveryNoticeHeadDTO.getItemList().get(0).getApplyUserCode())
                .setApplyUserName(deliveryNoticeHeadDTO.getItemList().get(0).getApplyUserName());
        // 封装打印数据
        List<BizReceiptLogisticsItemDTO> itemList = deliveryNoticeHeadDTO.getItemList();
        /* ================ 箱件清单 ================*/
        Map<String, List<BizReceiptLogisticsItemDTO>> groupCaseCode = itemList.stream().collect(Collectors.groupingBy(BizReceiptLogisticsItemDTO::getCaseCode));
        List<BizReceiptLogisticsItemDTO> caseList = new ArrayList<>();
        //7.18 需求变更，箱与单据绑定关系变更，为维护之前数据显示修改
        AtomicInteger rid = new AtomicInteger(1);
        deliveryNoticeHeadDTO.getCaseNowList().forEach(obj->{
            switch (obj.getPackageType()){
                case "1":
                    obj.setPackageType("纸箱");
                    obj.setPackageTypeStr("纸箱");
                    break;
                case "2":
                    obj.setPackageType("木箱");
                    obj.setPackageTypeStr("木箱");
                    break;
                case "3":
                    obj.setPackageType("裸装");
                    obj.setPackageTypeStr("裸装");
                    break;
                case "4":
                    obj.setPackageType("罐车");
                    obj.setPackageTypeStr("罐车");
                    break;
                case "5":
                    obj.setPackageType("钢瓶");
                    obj.setPackageTypeStr("钢瓶");
                    break;
                case "6":
                    obj.setPackageType("托盘");
                    obj.setPackageTypeStr("托盘");
                    break;
                case "7":
                    obj.setPackageType("袋装");
                    obj.setPackageTypeStr("袋装");
                    break;
                case "8":
                    obj.setPackageType("其他");
                    obj.setPackageTypeStr("其他");
                default:
                    obj.setPackageTypeStr(obj.getPackageType());
            }
            obj.setRid(rid.getAndIncrement()+"");
        });
        deliveryNoticeHeadDTO.setCaseList(caseList);
        /* ================ 箱件清单 ================*/
        deliveryNoticeHeadDTO.setReferReceiptCode(deliveryNoticeHeadDTO.getItemList().get(0).getReferReceiptCode());
        deliveryNoticeHeadDTO.setIsTranSpecialStr(deliveryNoticeHeadDTO.getIsTranSpecial()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsSafeStr(deliveryNoticeHeadDTO.getIsSafe()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsRadioactivityStr(deliveryNoticeHeadDTO.getIsRadioactivity()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsDangerStr(deliveryNoticeHeadDTO.getIsDanger()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsSpecialRequStr(deliveryNoticeHeadDTO.getIsSpecialRequ()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsReportStr(deliveryNoticeHeadDTO.getIsReport()==1?"是":"否");
        // 物流清关费用车型
        switch (deliveryNoticeHeadDTO.getDeliveryCarType()){
            case "1":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("板车小于6米");
                break;
            case "2":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("板车大于6米小于13米");
                break;
            case "3":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("板车大于13米");
                break;
            case "4":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("厢式货车大于6米");
                break;
            case "5":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("厢式货车小于6米");
                break;
            default:
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("");

        }

        // 分物资返运填充固定资产物料描述属性
        itemList.stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(item -> {
            ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(new QueryWrapper<ErpPurchaseReceiptItem>().eq(null != item.getReferReceiptItemId(), "id", item.getReferReceiptItemId()));
            if (erpPurchaseReceiptItem != null && erpPurchaseReceiptItem.getSubjectType().equals("1")){
                item.setMatName(erpPurchaseReceiptItem.getMatNameBack());
            }
        });
        
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(deliveryNoticeHeadDTO, ctx.getCurrentUser());
        // 设置物流清关费用单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(deliveryNoticeHeadDTO, new ExtendVO(), buttonVO));
    }


    /**
     * 保存-校验物流清关费用入参
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            log.warn("提交的出库单没有包含抬头信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            log.warn("提交的验收单没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 设置行项目号
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptLogisticsItemDTO itemDto : po.getItemList()) {
            itemDto.setRid(String.format("%05d",rid.getAndIncrement()*10));
        }
    }

    /**
     * 保存-物流清关费用单
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     * @out ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     */
    public void saveLogistics(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String deliveryNoticeCode = po.getReceiptCode();
        if(UtilNumber.isEmpty(po.getId())){
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.LOGISTICS.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新物流清关费用单
            bizReceiptLogisticsHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteDeliveryNoticeItem(po);
            // 修改前删除箱关联
            this.deleteDeliveryNoticeCase(po);
            // 修改前删除税费
            this.deleteDeliveryNoticeTax(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            deliveryNoticeCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_LOGISTICS.getValue());
            po.setReceiptCode(deliveryNoticeCode);
            bizReceiptLogisticsHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存物流清关费用单head成功!单号{},主键{},操作人{}", deliveryNoticeCode, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptLogisticsItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(String.format("%05d",rid.getAndIncrement()*10));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }


        // 设置均摊税费
        this.setShareTax(po);

        bizReceiptLogisticsItemDataWrap.saveBatchDto(po.getItemList());
        for (BizReceiptLogisticsCaseRelDTO bizReceiptDeliveryNoticeCaseRelDTO : po.getCaseNowList()) {
            bizReceiptDeliveryNoticeCaseRelDTO.setId(UtilSequence.nextId());
            bizReceiptDeliveryNoticeCaseRelDTO.setHeadId(po.getId());
            bizReceiptDeliveryNoticeCaseRelDTO.setCreateTime(new Date());
            bizReceiptDeliveryNoticeCaseRelDTO.setModifyTime(new Date());
            bizReceiptDeliveryNoticeCaseRelDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptDeliveryNoticeCaseRelDTO.setModifyUserId(ctx.getCurrentUser().getId());
        }
        bizReceiptLogisticsCaseRelDataWrap.saveBatchDto(po.getCaseNowList());

        // 保存税费 
        if(po.getTaxDTO() != null){
            po.getTaxDTO().setHeadId(po.getId());
            bizReceiptLogisticsTaxDataWrap.saveDto(po.getTaxDTO());
        }


        log.debug("批量保存物流清关费用单item成功,code{},headId{},操作人{}", deliveryNoticeCode, po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, deliveryNoticeCode);
    }



    /*
     * 设置分摊税额 
     * 
     * 关税=总关税*费用占比%*分类价值百分比%
     * 附加关税=总附加关税*费用占比%*分类价值百分比%
     * 销售税=总销售税*费用占比%*分类价值百分比%
     * 附加销售税=总附加销售税*费用占比%*分类价值百分比%
     * 调节税=总调节税*费用占比%*分类价值百分比%
     * 所得税=总所得税*费用占比%*分类价值百分比%
     * Totall GST = Totall GST General Sales Tax *费用占比%*分类价值百分比%
     * 联邦消费税 =总联邦消费税 *费用占比%*分类价值百分比%
     * 逾期罚款=总逾期罚款*货物价值百分比%
     * 发票缺失=总发票缺失*货物价值百分比%
     * GD提交费=总GD提交费*货物价值百分比%
     * 基建税 =总基建税 *货物价值百分比%
     * 印花税  =总印花税  *货物价值百分比%
     * 检验检测费  =总检验检测费  *货物价值百分比%
     * 出口报关服务费  =总出口报关服务费  *货物价值百分比%
     * 海/空运费  =总海/空运费  *货物价值百分比%
     * 进口清关服务费=总进口清关服务费  *货物价值百分比%
     * 内陆运输费=总内陆运输费  *货物价值百分比%
     * 进口拖车押车费=总进口拖车押车费  *货物价值百分比%
     * 
     * 
     */
    private void setShareTax(BizReceiptLogisticsHeadDTO po){

        if (UtilCollection.isEmpty(po.getItemList()) || 
            UtilObject.isNull(po.getTaxDTO())) {
            return;
        }
        for (BizReceiptLogisticsItemDTO item : po.getItemList()) {  
            this.calculateAndSetShareValues(item, po.getTaxDTO());
        }
       
        // 各项税费差值处理，第一行补充或扣减差值，第一行超过总值或小于0，则第二行扣减差值，第二行超过总值或小于0，则第三行扣减差值，以此类推
        this.handleTaxDiff(po);

    }   

    private void calculateAndSetShareValues(BizReceiptLogisticsItemDTO item, BizReceiptLogisticsTaxDTO taxDTO) {
//        item.setShareTax(calculatecCategoryShareValue(taxDTO.getTotalTax(), item));
//        item.setShareAdditionalTax(calculatecCategoryShareValue(taxDTO.getTotalAdditionalTax(), item));
//        item.setShareSalesTax(calculatecCategoryShareValue(taxDTO.getTotalSalesTax(), item));
//        item.setShareAdditionalSalesTax(calculatecCategoryShareValue(taxDTO.getTotalAdditionalSalesTax(), item));
//        item.setShareAdjustmentTax(calculatecCategoryShareValue(taxDTO.getTotalAdjustmentTax(), item));
//        item.setShareIncomeTax(calculatecCategoryShareValue(taxDTO.getTotalIncomeTax(), item));
//        item.setShareGst(calculatecCategoryShareValue(taxDTO.getTotalGst(), item));
//        item.setShareFederalConsumptionTax(calculatecCategoryShareValue(taxDTO.getTotalFederalConsumptionTax(), item));



        item.setShareTax(calculateCargoShareValue(taxDTO.getTotalTax(), item));
        item.setShareAdditionalTax(calculateCargoShareValue(taxDTO.getTotalAdditionalTax(), item));
        // item.setShareSalesTax(calculateCargoShareValue(taxDTO.getTotalSalesTax(), item));
        // item.setShareAdditionalSalesTax(calculateCargoShareValue(taxDTO.getTotalAdditionalSalesTax(), item));
        item.setShareAdjustmentTax(calculateCargoShareValue(taxDTO.getTotalAdjustmentTax(), item));
        // item.setShareIncomeTax(calculateCargoShareValue(taxDTO.getTotalIncomeTax(), item));
        // item.setShareGst(calculateCargoShareValue(taxDTO.getTotalGst(), item));
        item.setShareFederalConsumptionTax(calculateCargoShareValue(taxDTO.getTotalFederalConsumptionTax(), item));


        item.setShareLatePenalty(calculateCargoShareValue(taxDTO.getTotalLatePenalty(), item));
        item.setShareInvoiceMissing(calculateCargoShareValue(taxDTO.getTotalInvoiceMissing(), item));
        item.setShareGdSubmissionFee(calculateCargoShareValue(taxDTO.getTotalGdSubmissionFee(), item));
        item.setShareInfrastructureTax(calculateCargoShareValue(taxDTO.getTotalInfrastructureTax(), item));
        item.setShareStampDuty(calculateCargoShareValue(taxDTO.getTotalStampDuty(), item));
        item.setShareInspectionFee(calculateCargoShareValue(taxDTO.getTotalInspectionFee(), item));
        item.setShareExportCustomsServiceFee(calculatecCategoryShareValue(taxDTO.getTotalExportCustomsServiceFee(), item));
        item.setShareOceanFreight(calculateCargoShareValue(taxDTO.getTotalOceanFreight(), item));
        item.setShareImportCustomsServiceFee(calculateCargoShareValue(taxDTO.getTotalImportCustomsServiceFee(), item));
        item.setShareInlandTransportFee(calculateCargoShareValue(taxDTO.getTotalInlandTransportFee(), item));
        item.setShareImportTruckFee(calculateCargoShareValue(taxDTO.getTotalImportTruckFee(), item));
    }
    
    /**
     * 计算货物价值百分比分摊值
     */
    private BigDecimal calculateCargoShareValue(BigDecimal totalValue, BizReceiptLogisticsItemDTO item) {
        if(UtilNumber.isEmpty(totalValue)){
            return BigDecimal.ZERO;
        }
        BigDecimal calculatedValue = totalValue
            .multiply(item.getCargoValueRatio())
            .divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP)
            .setScale(2, BigDecimal.ROUND_HALF_UP);
    
        // 如果计算结果小于0.01，则设置为0.01
        if (calculatedValue.compareTo(new BigDecimal("0.01")) < 0) {
            calculatedValue = new BigDecimal("0.01");
        }
    
        return calculatedValue;
    }

    /**
     * 计算分类价值百分比分摊值
     */
    private BigDecimal calculatecCategoryShareValue(BigDecimal totalValue, BizReceiptLogisticsItemDTO item) {
        if(UtilNumber.isEmpty(totalValue)){
            return BigDecimal.ZERO;
        }
        BigDecimal calculatedValue = totalValue
            .multiply(item.getFeeRatio())
            .multiply(item.getCategoryValueRatio())
            .divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP)
            .setScale(2, BigDecimal.ROUND_HALF_UP);
        
        // 如果计算结果小于0.01，则设置为0.01
        if (calculatedValue.compareTo(new BigDecimal("0.01")) < 0) {
            calculatedValue = new BigDecimal("0.01");
        }
    
        return calculatedValue;
    }



    /**
     * 处理费用差异
     * @param headDTO 物流清关费用单头DTO
     */
    private void handleTaxDiff(BizReceiptLogisticsHeadDTO headDTO) {
        if (UtilCollection.isEmpty(headDTO.getItemList()) || 
            UtilObject.isNull(headDTO.getTaxDTO())) {
            return;
        }

        BizReceiptLogisticsTaxDTO taxDTO = headDTO.getTaxDTO();

        // 计算各项费用总和
        BigDecimal totalShareTax = BigDecimal.ZERO;
        BigDecimal totalShareAdditionalTax = BigDecimal.ZERO;
        BigDecimal totalShareSalesTax = BigDecimal.ZERO;
        BigDecimal totalShareAdditionalSalesTax = BigDecimal.ZERO;
        BigDecimal totalShareAdjustmentTax = BigDecimal.ZERO;
        BigDecimal totalShareIncomeTax = BigDecimal.ZERO;
        BigDecimal totalShareGst = BigDecimal.ZERO;
        BigDecimal totalShareFederalConsumptionTax = BigDecimal.ZERO;
        BigDecimal totalShareLatePenalty = BigDecimal.ZERO;
        BigDecimal totalShareInvoiceMissing = BigDecimal.ZERO;
        BigDecimal totalShareGdSubmissionFee = BigDecimal.ZERO;
        BigDecimal totalShareInfrastructureTax = BigDecimal.ZERO;
        BigDecimal totalShareStampDuty = BigDecimal.ZERO;
        BigDecimal totalShareInspectionFee = BigDecimal.ZERO;
        BigDecimal totalShareExportCustomsServiceFee = BigDecimal.ZERO;
        BigDecimal totalShareOceanFreight = BigDecimal.ZERO;
        BigDecimal totalShareImportCustomsServiceFee = BigDecimal.ZERO;
        BigDecimal totalShareInlandTransportFee = BigDecimal.ZERO;
        BigDecimal totalShareImportTruckFee = BigDecimal.ZERO;

        // 计算费用总和
        for (BizReceiptLogisticsItemDTO item : headDTO.getItemList()) {
            totalShareTax = totalShareTax.add(UtilObject.isNull(item.getShareTax()) ? BigDecimal.ZERO : item.getShareTax());
            totalShareAdditionalTax = totalShareAdditionalTax.add(UtilObject.isNull(item.getShareAdditionalTax()) ? BigDecimal.ZERO : item.getShareAdditionalTax());
            totalShareSalesTax = totalShareSalesTax.add(UtilObject.isNull(item.getShareSalesTax()) ? BigDecimal.ZERO : item.getShareSalesTax());
            totalShareAdditionalSalesTax = totalShareAdditionalSalesTax.add(UtilObject.isNull(item.getShareAdditionalSalesTax()) ? BigDecimal.ZERO : item.getShareAdditionalSalesTax());
            totalShareAdjustmentTax = totalShareAdjustmentTax.add(UtilObject.isNull(item.getShareAdjustmentTax()) ? BigDecimal.ZERO : item.getShareAdjustmentTax());
            totalShareIncomeTax = totalShareIncomeTax.add(UtilObject.isNull(item.getShareIncomeTax()) ? BigDecimal.ZERO : item.getShareIncomeTax());
            totalShareGst = totalShareGst.add(UtilObject.isNull(item.getShareGst()) ? BigDecimal.ZERO : item.getShareGst());
            totalShareFederalConsumptionTax = totalShareFederalConsumptionTax.add(UtilObject.isNull(item.getShareFederalConsumptionTax()) ? BigDecimal.ZERO : item.getShareFederalConsumptionTax());
            totalShareLatePenalty = totalShareLatePenalty.add(UtilObject.isNull(item.getShareLatePenalty()) ? BigDecimal.ZERO : item.getShareLatePenalty());
            totalShareInvoiceMissing = totalShareInvoiceMissing.add(UtilObject.isNull(item.getShareInvoiceMissing()) ? BigDecimal.ZERO : item.getShareInvoiceMissing());
            totalShareGdSubmissionFee = totalShareGdSubmissionFee.add(UtilObject.isNull(item.getShareGdSubmissionFee()) ? BigDecimal.ZERO : item.getShareGdSubmissionFee());
            totalShareInfrastructureTax = totalShareInfrastructureTax.add(UtilObject.isNull(item.getShareInfrastructureTax()) ? BigDecimal.ZERO : item.getShareInfrastructureTax());
            totalShareStampDuty = totalShareStampDuty.add(UtilObject.isNull(item.getShareStampDuty()) ? BigDecimal.ZERO : item.getShareStampDuty());
            totalShareInspectionFee = totalShareInspectionFee.add(UtilObject.isNull(item.getShareInspectionFee()) ? BigDecimal.ZERO : item.getShareInspectionFee());
            totalShareExportCustomsServiceFee = totalShareExportCustomsServiceFee.add(UtilObject.isNull(item.getShareExportCustomsServiceFee()) ? BigDecimal.ZERO : item.getShareExportCustomsServiceFee());
            totalShareOceanFreight = totalShareOceanFreight.add(UtilObject.isNull(item.getShareOceanFreight()) ? BigDecimal.ZERO : item.getShareOceanFreight());
            totalShareImportCustomsServiceFee = totalShareImportCustomsServiceFee.add(UtilObject.isNull(item.getShareImportCustomsServiceFee()) ? BigDecimal.ZERO : item.getShareImportCustomsServiceFee());
            totalShareInlandTransportFee = totalShareInlandTransportFee.add(UtilObject.isNull(item.getShareInlandTransportFee()) ? BigDecimal.ZERO : item.getShareInlandTransportFee());
            totalShareImportTruckFee = totalShareImportTruckFee.add(UtilObject.isNull(item.getShareImportTruckFee()) ? BigDecimal.ZERO : item.getShareImportTruckFee());
        }

        // 处理差异
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalTax(), totalShareTax, BizReceiptLogisticsItemDTO::getShareTax, BizReceiptLogisticsItemDTO::setShareTax);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalAdditionalTax(), totalShareAdditionalTax, BizReceiptLogisticsItemDTO::getShareAdditionalTax, BizReceiptLogisticsItemDTO::setShareAdditionalTax);
        //adjustShareValue(headDTO.getItemList(), taxDTO.getTotalSalesTax(), totalShareSalesTax, BizReceiptLogisticsItemDTO::getShareSalesTax, BizReceiptLogisticsItemDTO::setShareSalesTax);
        //adjustShareValue(headDTO.getItemList(), taxDTO.getTotalAdditionalSalesTax(), totalShareAdditionalSalesTax, BizReceiptLogisticsItemDTO::getShareAdditionalSalesTax, BizReceiptLogisticsItemDTO::setShareAdditionalSalesTax);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalAdjustmentTax(), totalShareAdjustmentTax, BizReceiptLogisticsItemDTO::getShareAdjustmentTax, BizReceiptLogisticsItemDTO::setShareAdjustmentTax);
        //adjustShareValue(headDTO.getItemList(), taxDTO.getTotalIncomeTax(), totalShareIncomeTax, BizReceiptLogisticsItemDTO::getShareIncomeTax, BizReceiptLogisticsItemDTO::setShareIncomeTax);
        //adjustShareValue(headDTO.getItemList(), taxDTO.getTotalGst(), totalShareGst, BizReceiptLogisticsItemDTO::getShareGst, BizReceiptLogisticsItemDTO::setShareGst);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalFederalConsumptionTax(), totalShareFederalConsumptionTax, BizReceiptLogisticsItemDTO::getShareFederalConsumptionTax, BizReceiptLogisticsItemDTO::setShareFederalConsumptionTax);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalLatePenalty(), totalShareLatePenalty, BizReceiptLogisticsItemDTO::getShareLatePenalty, BizReceiptLogisticsItemDTO::setShareLatePenalty);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalInvoiceMissing(), totalShareInvoiceMissing, BizReceiptLogisticsItemDTO::getShareInvoiceMissing, BizReceiptLogisticsItemDTO::setShareInvoiceMissing);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalGdSubmissionFee(), totalShareGdSubmissionFee, BizReceiptLogisticsItemDTO::getShareGdSubmissionFee, BizReceiptLogisticsItemDTO::setShareGdSubmissionFee);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalInfrastructureTax(), totalShareInfrastructureTax, BizReceiptLogisticsItemDTO::getShareInfrastructureTax, BizReceiptLogisticsItemDTO::setShareInfrastructureTax);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalStampDuty(), totalShareStampDuty, BizReceiptLogisticsItemDTO::getShareStampDuty, BizReceiptLogisticsItemDTO::setShareStampDuty);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalInspectionFee(), totalShareInspectionFee, BizReceiptLogisticsItemDTO::getShareInspectionFee, BizReceiptLogisticsItemDTO::setShareInspectionFee);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalExportCustomsServiceFee(), totalShareExportCustomsServiceFee, BizReceiptLogisticsItemDTO::getShareExportCustomsServiceFee, BizReceiptLogisticsItemDTO::setShareExportCustomsServiceFee);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalOceanFreight(), totalShareOceanFreight, BizReceiptLogisticsItemDTO::getShareOceanFreight, BizReceiptLogisticsItemDTO::setShareOceanFreight);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalImportCustomsServiceFee(), totalShareImportCustomsServiceFee, BizReceiptLogisticsItemDTO::getShareImportCustomsServiceFee, BizReceiptLogisticsItemDTO::setShareImportCustomsServiceFee);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalInlandTransportFee(), totalShareInlandTransportFee, BizReceiptLogisticsItemDTO::getShareInlandTransportFee, BizReceiptLogisticsItemDTO::setShareInlandTransportFee);
        adjustShareValue(headDTO.getItemList(), taxDTO.getTotalImportTruckFee(), totalShareImportTruckFee, BizReceiptLogisticsItemDTO::getShareImportTruckFee, BizReceiptLogisticsItemDTO::setShareImportTruckFee);
    }

    /**
     * 调整费用差异
     * @param itemList 行项目列表
     * @param totalValue 总费用
     * @param totalShare 总分摊费用
     * @param getter 获取器
     * @param setter 设置器
     */
    private void adjustShareValue(List<BizReceiptLogisticsItemDTO> itemList, BigDecimal totalValue, BigDecimal totalShare,
                                Function<BizReceiptLogisticsItemDTO, BigDecimal> getter,
                                BiConsumer<BizReceiptLogisticsItemDTO, BigDecimal> setter) {
        if (UtilNumber.isNull(totalValue)) {
            return;
        }
        BigDecimal diff = totalValue.subtract(totalShare);
        if (diff.compareTo(BigDecimal.ZERO) > 0) {
            // 找到最小的费用项进行增补
            BizReceiptLogisticsItemDTO minItem = itemList.stream()
                .min(Comparator.comparing(getter))
                .orElse(null);
            if (minItem != null) {
                BigDecimal newValue = getter.apply(minItem).add(diff);
                setter.accept(minItem, newValue);
            }
        } else if (diff.compareTo(BigDecimal.ZERO) < 0) {
            // 找到最大的费用项进行增补
            BizReceiptLogisticsItemDTO maxItem = itemList.stream()
                .max(Comparator.comparing(getter))
                .orElse(null);
            if (maxItem != null) {
                BigDecimal newValue = getter.apply(maxItem).add(diff);
                setter.accept(maxItem, newValue);
            }
        }
    }

    private void deleteDeliveryNoticeCase(BizReceiptLogisticsHeadDTO po) {
        QueryWrapper<BizReceiptLogisticsCaseRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptLogisticsCaseRel::getHeadId,po.getId());
        bizReceiptLogisticsCaseRelDataWrap.remove(queryWrapper);
    }

    private void deleteDeliveryNoticeTax(BizReceiptLogisticsHeadDTO po) {
        QueryWrapper<BizReceiptLogisticsTax> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptLogisticsTax::getHeadId,po.getId());
        bizReceiptLogisticsTaxDataWrap.remove(queryWrapper);
    }


    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "要保存操作日志的物流清关费用单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的物流清关费用单
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "要保存附件的物流清关费用单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存物流清关费用单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            EnumReceiptType.DELIVERY_NOTICE.getValue(), user.getId());
        log.debug("保存物流清关费用单附件成功!");
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "要保存单据流的物流清关费用单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptLogisticsItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation();
            dto.setReceiptType(headDTO.getReceiptType());
            dto.setReceiptHeadId(item.getHeadId());
            dto.setReceiptItemId(item.getId());
            dto.setPreReceiptType(item.getPreReceiptType());
            dto.setPreReceiptHeadId(item.getPreReceiptHeadId());
            dto.setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        receiptRelationService.multiSaveReceiptTree(dtoList);
    }

    /**
     * 提交-校验物流清关费用入参
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验入参 ******** */
        this.checkSaveData(ctx);
        /* ******** 校验计划到货日期不能为空 ******** */
//        if (UtilObject.isNull(headDTO.getPlanArrivalDate())) {
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_PLANNED_ARRIVAL_DATE_IS_EMPTY);
//        }
        /* ******** 校验数量 ******** */
        this.checkItemQty(headDTO);
        
    }

    

    /**
     * 提交物流清关费用
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "提交物流清关费用"}
     * @out ctx 出参 {@link BizReceiptLogisticsHeadDTO : "提交的物流清关费用")}
     */
    public void submitDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 设置提交操作信息
        po.setSubmitUserId(ctx.getCurrentUserId());
        po.setSubmitTime(new Date());

        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存物流清关费用
        this.saveLogistics(ctx);
        // 更新物流清关费用head、item状态 - 已完成
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
    }

    /**
     * 删除前校验
     *
     * @in ctx 入参 {@link BizReceiptLogisticsDeletePO : "物流清关费用单删除入参"}
     * @out ctx 出参 {@link BizReceiptLogisticsDeletePO : "如果是全部删除，设置行项目id集合"}
     */
    public void checkDeleteDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取物流清关费用信息
        BizReceiptLogisticsHead deliveryNoticeHead = bizReceiptLogisticsHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptLogisticsHeadDTO deliveryNoticeHeadDTO =
            UtilBean.newInstance(deliveryNoticeHead, BizReceiptLogisticsHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(deliveryNoticeHeadDTO);
        /* ******** 校验物流清关费用单head ******** */
        if (UtilObject.isNotNull(deliveryNoticeHeadDTO)) {
            if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue()
                .equals(deliveryNoticeHeadDTO.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(deliveryNoticeHeadDTO.getItemList().stream().map(BizReceiptLogisticsItemDTO::getId)
                .collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 删除物流清关费用单
     *
     * @in ctx 入参 {@link BizReceiptLogisticsDeletePO : "物流清关费用单删除入参"}
     */
    public void deleteDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除物流清关费用 ******** */
        if (po.isDeleteAll()) {
            // 删除物流清关费用head
            bizReceiptLogisticsHeadDataWrap.removeById(po.getHeadId());
            // 删除物流清关费用item
            UpdateWrapper<BizReceiptLogisticsItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptLogisticsItem::getHeadId, po.getHeadId());
            bizReceiptLogisticsItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.DELIVERY_NOTICE.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除物流清关费用item
            bizReceiptLogisticsItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 删除单据流
     *
     * @in ctx 入参 {@link BizReceiptLogisticsDeletePO : "物流清关费用删除入参"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据流
            receiptRelationService.deleteReceiptTree(EnumReceiptType.DELIVERY_NOTICE.getValue(), po.getHeadId());
        }
    }

    /**
     * 删除单据附件
     *
     * @in ctx 入参 {@link BizReceiptLogisticsDeletePO : "物流清关费用删除入参"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据附件
            receiptAttachmentService.deleteBizReceiptAttachment(po.getHeadId(),
                EnumReceiptType.DELIVERY_NOTICE.getValue());
        }
    }

    /**
     * 添加物料查询-基于采购订单
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO< BizReceiptLogisticsPreHeadVo > :"物流清关费用单前续单据结果集"}
     */
    public void purchaseReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (EnumReceiptType.PURCHASE_RECEIPT.getValue().equals(po.getPreReceiptType())) {
            // 装载返回对象
            MultiResultVO<BizReceiptLogisticsPreHeadVo> returnVo = new MultiResultVO<>();
            // 调用SAP查询采购订单
            List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList = purchaseReceiptService
                .getErpPurchaseReceiptItemList(po.setIsReturnFlag(EnumRealYn.FALSE.getIntValue()), user);
            // 上下文返回参数
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, this.purchaseDataFormat(returnVo, purchaseReceiptItemVoList));
        }
    }

/*    *//**
     * 完成物流清关费用前校验
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     *//*
    public void checkFinishDelivery(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        *//* ******** 校验入参 ******** *//*
        this.checkSaveData(ctx);
        if (!EnumReceiptStatus.RECEIPT_STATUS_IN_DELIVERY.getValue().equals(po.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_OPERATION);
        }
    }*/

/*    *//**
     * 完成物流清关费用
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     *//*
    @Transactional(rollbackFor = Exception.class)
    public void finishDelivery(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新物流清关费用单head、item状态-已完成
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, po.getReceiptCode());
    }*/

    /**
     * 采购订单查询参转换
     *
     * @param returnVo 要设置的返回数据
     * @param purchaseReceiptItemVoList 采购订单信息
     * @return 物料信息
     */
    private MultiResultVO<BizReceiptLogisticsPreHeadVo> purchaseDataFormat(
        MultiResultVO<BizReceiptLogisticsPreHeadVo> returnVo,
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList) {
        if (UtilCollection.isNotEmpty(purchaseReceiptItemVoList)) {
            // 装载返回数据
            List<BizReceiptLogisticsPreHeadVo> headInfoList = new ArrayList<>();
            // 根据采购订单号分组
            LinkedHashMap<String, List<ErpPurchaseReceiptItemDTO>> purchaseMap =
                purchaseReceiptItemVoList.stream().collect(Collectors
                    .groupingBy(ErpPurchaseReceiptItemDTO::getReceiptCode, LinkedHashMap::new, Collectors.toList()));
            Set<String> keys = purchaseMap.keySet();
            for (String key : keys) {
                // 装载返回数据head
                BizReceiptLogisticsPreHeadVo headInfo = new BizReceiptLogisticsPreHeadVo();
                // 装载返回数据item
                List<BizReceiptLogisticsItemDTO> itemInfoList = new ArrayList<>();
                List<ErpPurchaseReceiptItemDTO> purchaseItemList = purchaseMap.get(key);
                for (int i = 0; i < purchaseItemList.size(); i++) {
                    ErpPurchaseReceiptItemDTO purchaseDTO = purchaseItemList.get(i);
                    BizReceiptLogisticsItemDTO itemInfo = new BizReceiptLogisticsItemDTO();
                    /* ******** 设置head列字段 ******** */
                    if (i == 0) {
                        headInfo = UtilBean.newInstance(purchaseDTO, headInfo.getClass());
                        headInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                        headInfo.setPurchaseUserCode(purchaseDTO.getPurchaseUserCode());
                        headInfo.setPurchaseUserName(purchaseDTO.getPurchaseUserName());
                        headInfo.setContractCode(purchaseDTO.getContractCode());
                        headInfo.setContractName(purchaseDTO.getContractName());
                        headInfo.setSupplierCode(purchaseDTO.getSupplierCode());
                        headInfo.setSupplierName(purchaseDTO.getSupplierName());
                    }
                    /* ******** 设置item列字段 ******** */
                    itemInfo = UtilBean.newInstance(purchaseDTO, itemInfo.getClass());
                    itemInfo.setId(null);
                    itemInfo.setHeadId(null);
                    itemInfo.setRid(Const.STRING_EMPTY);
                    itemInfo.setReceiptCode(Const.STRING_EMPTY);
                    itemInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                    itemInfo.setReferReceiptRid(purchaseDTO.getRid());
                    itemInfo.setPreReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setPreReceiptItemId(purchaseDTO.getId());
                    itemInfo.setPreReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setPreReceiptQty(purchaseDTO.getReceiptQty());
                    itemInfo.setReferReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setReferReceiptItemId(purchaseDTO.getId());
                    itemInfo.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                    // 获取缓存中仓库信息
                    itemInfo.setWhId(UtilObject.isNotNull(dictionaryService.getLocationCacheById(purchaseDTO.getLocationId()))
                            ? dictionaryService.getLocationCacheById(purchaseDTO.getLocationId()).getWhId() : 0L);
                    itemInfoList.add(itemInfo);
                }
                headInfo.setChildren(itemInfoList);
                headInfoList.add(headInfo);
            }
            returnVo.setResultList(headInfoList);
        }
        return returnVo;
    }

    /**
     * 设置已物流清关费用数量
     *
     * @param referReceiptItemId 参考单据行项目id
     * @return 已物流清关费用数量
     */
    private BigDecimal setSendQty(Long referReceiptItemId) {
        if (UtilNumber.isEmpty(referReceiptItemId)) {
            return BigDecimal.ZERO;
        }
        QueryWrapper<BizReceiptLogisticsItem> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptLogisticsItem::getReferReceiptItemId, referReceiptItemId)
            .eq(BizReceiptLogisticsItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 根据参考单据获取物流清关费用单
        List<BizReceiptLogisticsItem> itemList = bizReceiptLogisticsItemDataWrap.list(wrapper);
        // 已物流清关费用数量
        BigDecimal sendQty = BigDecimal.ZERO;
        for (BizReceiptLogisticsItem item : itemList) {
            sendQty = sendQty.add(item.getQty());
        }
        return sendQty;
    }

    /**
     * 按钮组
     *
     * @param headDTO 物流清关费用单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptLogisticsHeadDTO headDTO, CurrentUser user) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 - [30562]【物流清关】打开已完成的物流清关单，请添加：撤销按钮。
            return buttonVO.setButtonClose(true).setButtonRevoke(true);
            // return buttonVO.setButtonCopyReceipt(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }else if(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)){
            // 未同步 -【采购单提交】
            return buttonVO.setButtonPost(true);
        }
        return buttonVO;
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return QueryWrapper<BizReceiptDeliveryNoticeHead>
     */
    private WmsQueryWrapper<BizReceiptLogisticsSearchPO> setQueryWrapper(BizReceiptLogisticsSearchPO po, CurrentUser user) {
        if (null == po) {
            po = new BizReceiptLogisticsSearchPO();
        }
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        String deliveryNoticeDesc = po.getDeliveryNoticeDescribe();
        // 查询条件设置
        WmsQueryWrapper<BizReceiptLogisticsSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .eq(true, BizReceiptLogisticsSearchPO::getIsDelete,
                        BizReceiptLogisticsHead.class, 0)
                .likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptLogisticsSearchPO::getReceiptCode,
                        BizReceiptLogisticsHead.class, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), BizReceiptLogisticsSearchPO::getReceiptType,
                        BizReceiptLogisticsHead.class, po.getReceiptType())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptLogisticsSearchPO::getReceiptStatus,
                        BizReceiptLogisticsHead.class, po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptLogisticsSearchPO::getLocationId,
                        BizReceiptLogisticsItem.class,locationIdList)
                .like(UtilString.isNotNullOrEmpty(po.getPurchaseReceiptCode()), BizReceiptLogisticsSearchPO::getPurchaseCode,
                        BizReceiptLogisticsHead.class, po.getPurchaseReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getBatchCode()), BizReceiptLogisticsSearchPO::getBatchCode,
                        BizReceiptLogisticsHead.class, po.getBatchCode())
                .like(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptLogisticsSearchPO::getMatCode,
                        DicMaterial.class, po.getMatCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptLogisticsSearchPO::getUserName,
                        SysUser.class, po.getCreateUserName())
                .between((UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime())), BizReceiptLogisticsSearchPO::getCreateTime,
                        BizReceiptLogisticsHead.class, po.getStartTime(), po.getEndTime())
                .like(UtilString.isNotNullOrEmpty(deliveryNoticeDesc), BizReceiptLogisticsSearchPO::getDeliveryNoticeDescribe,
                        BizReceiptLogisticsHead.class, deliveryNoticeDesc)
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptLogisticsSearchPO::getContractCode,
                        BizReceiptContractHead.class, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getPurchaserName()), BizReceiptLogisticsSearchPO::getCreateUserName,
                        BizReceiptContractHead.class, po.getPurchaserName())
                .eq(UtilNumber.isNotEmpty(po.getSendType()), BizReceiptLogisticsSearchPO::getSendType,
                        BizReceiptLogisticsHead.class, po.getSendType())
        ;
        return wrapper;
    }

    /**
     * 删除物流清关费用单行项目
     *
     * @param headDTO 物流清关费用
     */
    private void deleteDeliveryNoticeItem(BizReceiptLogisticsHeadDTO headDTO) {
        UpdateWrapper<BizReceiptLogisticsItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptLogisticsItem::getHeadId,
            headDTO.getId());
        bizReceiptLogisticsItemDataWrap.physicalDelete(wrapper);
    }

    /**
     * 校验行项目提交数量
     *
     * @param headDTO 物流清关费用单
     */
    private void checkItemQty(BizReceiptLogisticsHeadDTO headDTO) {
        // 装载qty为零的行项目
        List<String> errorQtyZeroList = new ArrayList<>();
        // 装载qty已超过物流清关费用数量的行项目
        List<String> errorQtyExceedList = new ArrayList<>();

        // 刷新合同可物流清关费用数量
        if (headDTO.getItemList().get(0).getPreReceiptType().equals(EnumReceiptType.CONTRACT_RECEIPT.getValue())) {

            List<Long> idList = headDTO.getItemList().stream().map(e -> e.getPreReceiptItemId()).collect(Collectors.toList());

            List<BizReceiptContractItem> contractItemList = bizReceiptContractItemDataWrap.listByIds(idList);

            Map<Long, BizReceiptContractItem> contractItemMap = contractItemList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (k1, k2) -> k2));
            headDTO.getItemList().forEach(itemDTO -> {
                BizReceiptContractItem contractItem = contractItemMap.get(itemDTO.getPreReceiptItemId());

                itemDTO.setCanDeliveryQty(contractItem.getQty().subtract(contractItem.getSendQty()));
            });

        }

        headDTO.getItemList().forEach(itemDTO -> {
            if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                errorQtyZeroList.add(itemDTO.getRid());
                return;
            }
            if (itemDTO.getQty().compareTo(itemDTO.getCanDeliveryQty()) > 0) {
                errorQtyExceedList.add(itemDTO.getRid());
            }
        });
        if (UtilCollection.isNotEmpty(errorQtyZeroList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_OPERATION_QTY_ZERO, errorQtyZeroList.toString());
        }
        if (UtilCollection.isNotEmpty(errorQtyExceedList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEED_DELIVERY_QTY, errorQtyExceedList.toString());
        }
    }

    /**
     * 准备更新物流清关费用状态
     *
     * @param headDTO 物流清关费用head
     * @param itemDTOList 物流清关费用item
     */
    public void updateStatus(BizReceiptLogisticsHeadDTO headDTO, List<BizReceiptLogisticsItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新物流清关费用head状态
     *
     * @param headDto 物流清关费用head
     */
    private void updateHead(BizReceiptLogisticsHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptLogisticsHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新物流清关费用item状态
     *
     * @param itemDtoList 物流清关费用item
     */
    private void updateItem(List<BizReceiptLogisticsItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptLogisticsItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }


    /**
     * 物流清关费用-单据复制
     *
     * @in ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     * @out ctx 入参 {@link BizReceiptLogisticsHeadDTO : "物流清关费用单"}
     */
    public void copyReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 设置抬头
        headDTO.setId(null).setCreateTime(null);
        // 设置行项目
        BizReceiptPreSearchPO po = new BizReceiptPreSearchPO();
        po.setPurchaseReceiptCode(headDTO.getItemList().get(0).getReferReceiptCode()).setIsReturnFlag(EnumRealYn.FALSE.getIntValue());
        // 调用SAP查询采购订单
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList = purchaseReceiptService.getErpPurchaseReceiptItemList(po, user);
        headDTO.getItemList().forEach(p -> {
            purchaseReceiptItemVoList.forEach(q -> {
                if(p.getReferReceiptItemId().equals(q.getId())) {
                    // 更新可物流清关费用数量
                    p.setCanDeliveryQty(q.getCanDeliveryQty());
                }
            });
        });
        // 保存单据
        this.saveLogistics(ctx);
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 若用户有J046工厂权限时，则触发审批
        if (this.checkUserFactory(ctx.getCurrentUser())) {
            this.approveCheck(ctx);
            // 发起流程审批
            Long receiptId = headDTO.getId();
            String receiptCode = headDTO.getReceiptCode();
            Integer receiptType = headDTO.getReceiptType();
            Map<String, Object> variables = new HashMap<>();
            Long ftyId = headDTO.getItemList().get(0).getFtyId();
            variables.put("ftyId", ftyId);
            // 物项合同科审核人员存入流程变量，作为一级审核节点审批人
            if (Objects.isNull(headDTO.getAssignUserId())) {
                throw new WmsException("物项合同科审核人员缺失");
            }
            List<String> userCode = new ArrayList<>();
            userCode.add(dictionaryService.getSysUserCacheById(headDTO.getAssignUserId()).getUserCode());
            variables.put("userCode", userCode);
            workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getRemark());
            workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        } else {
            // 更新物流清关费用head、item状态 - 已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
    }

    /**
     * 审批人校验
     *
     * @param ctx
     */
    private void approveCheck(BizContext ctx) {
        // 仓储承包商固定推送至两位用户“24001844-刘贵文”和“24001288-梁亮”(20240923变更需求)
        List<String> approveUserCode = Arrays.asList("24001844", "24001288");
        List<SysUser> sysUsersByUserCodeList = userService.getSysUsersByUserCodeList(approveUserCode);
        if (UtilCollection.isEmpty(sysUsersByUserCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptLogisticsHead head = bizReceiptLogisticsHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptLogisticsHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptLogisticsHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            // 更新状态已完成(插入数据库)
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            // 生成采购订单
            this.genPurchaseReceipt(ctx);
        } else {
            // 更新状态已驳回
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 生成采购订单(插入数据库)
     * @param ctx
     */
    public void genPurchaseReceipt(BizContext ctx){
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        

        ErpPurchaseReceiptHead purchaseHead = new ErpPurchaseReceiptHead();
        purchaseHead.setReceiptCode(headDTO.getPurchaseCode());
        purchaseHead.setCreateTime(new Date());
        purchaseHead.setIsReturnFlag(0);
        purchaseHead.setErpCreateTime(new Date());
        purchaseHead.setErpCreateUserName(headDTO.getModifyUserName());
        purchaseHead.setPaymentMethod(headDTO.getPaymentMethod());
        purchaseHead.setCurrency(headDTO.getCurrency());
        purchaseHead.setContractCode(headDTO.getContractCode());
        purchaseHead.setContractName(headDTO.getContractName());
        purchaseHead.setSupplierCode(headDTO.getSupplierCode());
        purchaseHead.setSupplierName(headDTO.getSupplierName());
        erpPurchaseReceiptHeadDataWrap.saveDto(purchaseHead);


        List<ErpPurchaseReceiptItem> purchaseItemList = new ArrayList<>();

        for(BizReceiptLogisticsItemDTO itemDTO:headDTO.getItemList()){
            ErpPurchaseReceiptItem purchaseItem = UtilBean.newInstance(itemDTO, ErpPurchaseReceiptItem.class);
            purchaseItem.setId(null);
            purchaseItem.setHeadId(purchaseHead.getId());
            purchaseItem.setRid(itemDTO.getRid());
            purchaseItem.setTaxCode(itemDTO.getContractTaxCode());
            purchaseItem.setPrice(itemDTO.getTaxPrice());
            purchaseItem.setReceiptQty(itemDTO.getQty());

            purchaseItemList.add(purchaseItem);
        }

        erpPurchaseReceiptItemDataWrap.saveBatch(purchaseItemList); 

    }



    public void createPurchase(BizContext ctx){
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        String purchaseCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_PURCHASE_CODE.getValue());

        headDTO.setPurchaseCode(purchaseCode);

        UpdateWrapper<BizReceiptLogisticsHead> headQueryWrapper = new UpdateWrapper<>();

        headQueryWrapper.lambda().set(BizReceiptLogisticsHead::getPurchaseCode,purchaseCode).eq(BizReceiptLogisticsHead::getId,headDTO.getId());

        bizReceiptLogisticsHeadDataWrap.update(headQueryWrapper);

    }

    public void autoApproval(BizContext ctx){
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
        wfReceiptCo.setReceiptHeadId(headDTO.getId());
        wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
        wfReceiptCo.setInitiator(ctx.getCurrentUser());
        this.approvalCallback(wfReceiptCo);
    }


    

    /**
     * 校验用户的工厂权限
     *
     * @param currentUser currentUser
     * @return
     */
    public Boolean checkUserFactory(CurrentUser currentUser) {
        // 用户只有J358工厂权限
        if (currentUser.getFactoryList().stream().allMatch(factory -> "J358".equals(factory.getFtyCode()))) {
            return false;
        }
        // 用户有J046工厂权限
        if (currentUser.getFactoryList().stream().anyMatch(factory -> "J046".equals(factory.getFtyCode()))) {
            return true;
        }
        return false;
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptLogisticsHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    public void importCase(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);

        try {
            //获取EXCEL数据
            List<BizReceiptLogisticsCaseRelImport> list = (List<BizReceiptLogisticsCaseRelImport>) UtilExcel.readExcelData(file.getInputStream(), BizReceiptLogisticsCaseRelImport.class);
            if (UtilCollection.isEmpty(list)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
            }
            List<String> packTypeList = list.stream().map(obj -> obj.getPackageType() == null ? "" : obj.getPackageType().trim()).distinct().collect(Collectors.toList());
            for (String s : packTypeList) {
                if (!s.equals("纸箱")&&!s.equals("裸装")&&!s.equals("木箱")&&!s.equals("罐车")&&!s.equals("钢瓶")&&!s.equals("托盘")&&!s.equals("袋装")&&!s.equals("其他")){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_UNSUP_PACK,s);
                }
            }
            List<BizReceiptLogisticsCaseRelDTO> bizReceiptDeliveryNoticeCaseRelDTOS = UtilCollection.toList(list, BizReceiptLogisticsCaseRelDTO.class);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,bizReceiptDeliveryNoticeCaseRelDTOS);
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }



    


    

    /**
     * 调用sap生成采购单
     */
    public void genPurchase(BizContext ctx){
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // TODO 暂用假接口
        String purchaseCode = bizCommonService.getNextSequence(EnumSequenceCode.SEQUENCE_PURCHASE_CODE.getValue());

        headDTO.setPurchaseCode(purchaseCode);
        AtomicInteger rid = new AtomicInteger(0);
        if(UtilCollection.isNotEmpty(headDTO.getItemList()) ){
            for(BizReceiptLogisticsItemDTO itemDTO:headDTO.getItemList()){
                itemDTO.setPurchaseCode(purchaseCode);
                // 生成自动流水
                itemDTO.setPurchaseRid(itemDTO.getRid());
            }
        }

        // 更新物流清关费用主表
        bizReceiptLogisticsHeadDataWrap.updateDtoById(headDTO);

        // 更新物流清关费用行项目
        bizReceiptLogisticsItemDataWrap.updateBatchDtoById(headDTO.getItemList());
    }

    /**
     * 将采购订单信息更新到送货通知与到货登记中
     */
    public void writeBackPurchase(BizContext ctx) {
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO == null || UtilCollection.isEmpty(headDTO.getItemList())) {
            return;
        }

        // 获取所有物流清关费用行项目的前序单据ID
        List<Long> preReceiptItemIds = headDTO.getItemList().stream()
            .map(BizReceiptLogisticsItemDTO::getPreReceiptItemId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (UtilCollection.isEmpty(preReceiptItemIds)) {
            return;
        }

        // 查询相关的送货通知行项目
        List<BizReceiptDeliveryNoticeItem> deliveryItems = deliveryNoticeItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptDeliveryNoticeItem>()
                .in(BizReceiptDeliveryNoticeItem::getId, preReceiptItemIds));

        if (UtilCollection.isEmpty(deliveryItems)) {
            return;
        }

        // 构建送货通知ID与物流清关费用行项目的映射
        Map<Long, BizReceiptLogisticsItemDTO> logisticsItemMap = headDTO.getItemList().stream()
            .collect(Collectors.toMap(BizReceiptLogisticsItemDTO::getPreReceiptItemId,
                e->e, (v1, v2) -> v1));

        // 准备送货通知批量更新数据
        List<BizReceiptDeliveryNoticeItemDTO> deliveryUpdateList = deliveryItems.stream()
            .map(deliveryItem -> {
                BizReceiptLogisticsItemDTO logisticsItem = logisticsItemMap.get(deliveryItem.getId());
                if (logisticsItem != null) {
                    deliveryItem.setPurchaseCode(logisticsItem.getPurchaseCode());
                    deliveryItem.setPurchaseRid(logisticsItem.getPurchaseRid());
                    return BizReceiptDeliveryNoticeItemDTO.builder()
                        .id(deliveryItem.getId())
                        .purchaseCode(logisticsItem.getPurchaseCode())
                        .purchaseRid(logisticsItem.getPurchaseRid())
                        .build();
                }
                return null;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // 批量更新送货通知
        if (UtilCollection.isNotEmpty(deliveryUpdateList)) {
            deliveryNoticeItemDataWrap.updateBatchDtoById(deliveryUpdateList);
        }

        // 更新送货通知抬头
        deliveryNoticeHeadDataWrap.update(new UpdateWrapper<BizReceiptDeliveryNoticeHead>()
            .lambda()
            .set(BizReceiptDeliveryNoticeHead::getPurchaseCode, headDTO.getPurchaseCode())
            .in(BizReceiptDeliveryNoticeHead::getId, deliveryItems.stream()
                .map(BizReceiptDeliveryNoticeItem::getHeadId)
                .distinct()
                .collect(Collectors.toList()))  );
        

        // 获取所有送货通知行项目ID
        List<Long> deliveryItemIds = deliveryItems.stream()
            .map(BizReceiptDeliveryNoticeItem::getId)
            .collect(Collectors.toList());

        // 查询相关的到货登记行项目
        List<BizReceiptRegisterItem> registerItems = registerItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptRegisterItem>()
                .in(BizReceiptRegisterItem::getPreReceiptItemId, deliveryItemIds));

        if (UtilCollection.isEmpty(registerItems)) {
            return;
        }

        // 构建送货通知ID与采购单信息的映射
        Map<Long, BizReceiptDeliveryNoticeItem> deliveryItemMap = deliveryItems.stream()
            .collect(Collectors.toMap(BizReceiptDeliveryNoticeItem::getId,
                e->e, (v1, v2) -> v1));

        // 准备到货登记批量更新数据
        List<BizReceiptRegisterItemDTO> registerUpdateList = registerItems.stream()
            .map(registerItem -> {
                BizReceiptDeliveryNoticeItem deliveryItem = deliveryItemMap.get(registerItem.getPreReceiptItemId());
                if (deliveryItem != null) {
                    registerItem.setPurchaseCode(deliveryItem.getPurchaseCode());
                    registerItem.setPurchaseRid(deliveryItem.getPurchaseRid());
                    BizReceiptRegisterItemDTO dto = new BizReceiptRegisterItemDTO();
                    dto.setId(registerItem.getId());
                    dto.setPurchaseCode(deliveryItem.getPurchaseCode());
                    dto.setPurchaseRid(deliveryItem.getPurchaseRid());
                    return dto;
                }
                return null;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // 批量更新到货登记
        if (UtilCollection.isNotEmpty(registerUpdateList)) {
            registerItemDataWrap.updateBatchDtoById(registerUpdateList);
        }

        // 更新到货登记抬头
        registerHeadDataWrap.update(new UpdateWrapper<BizReceiptRegisterHead>()
            .lambda()
            .set(BizReceiptRegisterHead::getPurchaseCode, headDTO.getPurchaseCode())
            .in(BizReceiptRegisterHead::getId, registerItems.stream()
                .map(BizReceiptRegisterItem::getHeadId)
                .distinct()
                .collect(Collectors.toList())));    

        // 采购信息更新到分配质检单中，根据到货登记明细的id查询质检分配单，再回写采购订单信息

        // 根据到货登记明细的id查询质检分配单，再回写采购订单信息

        List<Long> registerItemIds = registerItems.stream()
            .map(BizReceiptRegisterItem::getId)
            .collect(Collectors.toList());  

        List<BizReceiptInspectItem> inspectItems = inspectItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptInspectItem>()
                .in(BizReceiptInspectItem::getPreReceiptItemId, registerItemIds));

        if (UtilCollection.isEmpty(inspectItems)) {
            return;
        }

        // 构建质检分配单ID与到货登记行项目的映射
        Map<Long, BizReceiptRegisterItem> registerItemMap = registerItems.stream()
            .collect(Collectors.toMap(BizReceiptRegisterItem::getId,
                e->e, (v1, v2) -> v1));

        // 准备质检分配单批量更新数据
        List<BizReceiptInspectItemDTO> inspectUpdateList = inspectItems.stream()
            .map(inspectItem -> {
                BizReceiptRegisterItem registerItem = registerItemMap.get(inspectItem.getPreReceiptItemId());
                if (registerItem != null) {
                    BizReceiptInspectItemDTO dto = new BizReceiptInspectItemDTO();
                    dto.setId(inspectItem.getId());
                    dto.setPurchaseCode(registerItem.getPurchaseCode());
                    dto.setPurchaseRid(registerItem.getPurchaseRid());
                    return dto;
                }
                return null;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // 批量更新质检分配单
        if (UtilCollection.isNotEmpty(inspectUpdateList)) {
            inspectItemDataWrap.updateBatchDtoById(inspectUpdateList);
        }

        // 更新质检分配单抬头
        inspectHeadDataWrap.update(new UpdateWrapper<BizReceiptInspectHead>()
            .lambda()
            .set(BizReceiptInspectHead::getPurchaseCode, headDTO.getPurchaseCode())
            .in(BizReceiptInspectHead::getId, inspectItems.stream()
                .map(BizReceiptInspectItem::getHeadId)
                .distinct()
                .collect(Collectors.toList())));    


    }



    /**
     * 关闭物流清关费用
     * @param headId 物流清关费用ID
     */
    @Transactional
    public void closeLogistics(BizContext ctx) {
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(headDTO == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        Long headId = headDTO.getId();  

        // 1. 查询物流清关费用
        BizReceiptLogisticsHead logisticsHead = bizReceiptLogisticsHeadDataWrap.getById(headId);
        if (logisticsHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_ERROR_LOGISTICS_NOT_EXISTS);
        }

        // 2. 查询物流清关费用行项目
        List<BizReceiptLogisticsItem> logisticsItems = bizReceiptLogisticsItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptLogisticsItem>()
                .eq(BizReceiptLogisticsItem::getHeadId, headId));

        if (UtilCollection.isEmpty(logisticsItems)) {
            throw new WmsException(EnumReturnMsg.RETURN_ERROR_LOGISTICS_NO_ITEMS);
        }

        // 3. 获取送货通知ID列表
        List<Long> deliveryItemIds = logisticsItems.stream()
            .map(BizReceiptLogisticsItem::getPreReceiptItemId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (UtilCollection.isNotEmpty(deliveryItemIds)) {
            // 4. 查询送货通知行项目
            List<BizReceiptDeliveryNoticeItem> deliveryItems = deliveryNoticeItemDataWrap.listByIds(deliveryItemIds);
            
            if (UtilCollection.isNotEmpty(deliveryItems)) {
                // 5. 查询到货登记行项目
                List<BizReceiptRegisterItem> registerItems = registerItemDataWrap.list(
                    new LambdaQueryWrapper<BizReceiptRegisterItem>()
                        .eq(BizReceiptRegisterItem::getIsDelete, 0)
                        .in(BizReceiptRegisterItem::getPreReceiptItemId, 
                            deliveryItems.stream()
                                .map(BizReceiptDeliveryNoticeItem::getId)
                                .collect(Collectors.toList())));

                if (UtilCollection.isNotEmpty(registerItems)) {
                    

                    

                    // 跟新到货登记采购信息为空
                    registerItemDataWrap.update(
                        new UpdateWrapper<BizReceiptRegisterItem>()
                            .lambda()
                            .set(BizReceiptRegisterItem::getPurchaseCode, Const.STRING_EMPTY)
                            .set(BizReceiptRegisterItem::getPurchaseRid, Const.STRING_EMPTY)
                            .in(BizReceiptRegisterItem::getHeadId, registerItems.stream()
                                .map(BizReceiptRegisterItem::getHeadId)
                                .collect(Collectors.toList())));   


//                    // 查询质检分配单
//                    List<BizReceiptInspectItem> inspectItems = inspectItemDataWrap.list(
//                        new LambdaQueryWrapper<BizReceiptInspectItem>()
//                            .in(BizReceiptInspectItem::getPreReceiptItemId, registerItems.stream()
//                                .map(BizReceiptRegisterItem::getId)
//                                .collect(Collectors.toList())));
//
//                    if(UtilCollection.isEmpty(inspectItems)){
//                        return;
//                    }

//                    // 查询质检分配抬头
//                    List<BizReceiptInspectHead> inspectHeads = inspectHeadDataWrap.listByIds(inspectItems.stream()
//                        .map(BizReceiptInspectItem::getHeadId)
//                        .distinct()
//                        .collect(Collectors.toList()));

//                    if(UtilCollection.isEmpty(inspectHeads)){
//                        return;
//                    }
//
//                    // 校验质检分配单状态
//                    for (BizReceiptInspectHead inspectHead : inspectHeads) {
//                        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(inspectHead.getReceiptStatus())) {
//                            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_ASSIGN_HAS_INSPECT_HEAD,
//                                String.format("质检分配单[%s]", inspectHead.getReceiptCode()));
//                        }
//                    }
//
//                    // 9. 更新质检分配单采购信息为空
//                    inspectItemDataWrap.update(
//                        new UpdateWrapper<BizReceiptInspectItem>()
//                            .lambda()
//                            .set(BizReceiptInspectItem::getPurchaseCode, Const.STRING_EMPTY)
//                            .set(BizReceiptInspectItem::getPurchaseRid, Const.STRING_EMPTY)
//                            .in(BizReceiptInspectItem::getHeadId, inspectHeads.stream().map(o->o.getId()).collect(Collectors.toList())));
                }
            }
        }
    }

    /**
     * 清除采购信息
     */
    public void clearPurchaseInfo(BizContext ctx){

        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(headDTO == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 清空物流清关费用采购信息
        bizReceiptLogisticsHeadDataWrap.update(
            new UpdateWrapper<BizReceiptLogisticsHead>()
                .lambda()
                .set(BizReceiptLogisticsHead::getPurchaseCode, Const.STRING_EMPTY)
                .eq(BizReceiptLogisticsHead::getId, headDTO.getId()));

        // 清空物流清关费用行项目采购信息
        bizReceiptLogisticsItemDataWrap.update(
            new UpdateWrapper<BizReceiptLogisticsItem>()
                .lambda()
                .set(BizReceiptLogisticsItem::getPurchaseCode, Const.STRING_EMPTY)
                .set(BizReceiptLogisticsItem::getPurchaseRid, Const.STRING_EMPTY)
                .eq(BizReceiptLogisticsItem::getHeadId, headDTO.getId()));


        


        // 3. 获取送货通知ID列表
        List<Long> deliveryItemIds = headDTO.getItemList().stream()
            .map(BizReceiptLogisticsItemDTO::getPreReceiptItemId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        List<Long> deliveryHeadIds = headDTO.getItemList().stream()
            .map(BizReceiptLogisticsItemDTO::getPreReceiptHeadId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (UtilCollection.isNotEmpty(deliveryItemIds)) {

            // 更新送货通知采购信息为空 
            deliveryNoticeItemDataWrap.update(
                new UpdateWrapper<BizReceiptDeliveryNoticeItem>()
                    .lambda()
                    .set(BizReceiptDeliveryNoticeItem::getPurchaseCode, Const.STRING_EMPTY)
                    .set(BizReceiptDeliveryNoticeItem::getPurchaseRid, Const.STRING_EMPTY)
                    .in(BizReceiptDeliveryNoticeItem::getId, deliveryItemIds)); 
            // 更新送货通知抬头采购信息为空 
            deliveryNoticeHeadDataWrap.update(
                new UpdateWrapper<BizReceiptDeliveryNoticeHead>()
                    .lambda()
                    .set(BizReceiptDeliveryNoticeHead::getPurchaseCode, Const.STRING_EMPTY)
                    .in(BizReceiptDeliveryNoticeHead::getId, deliveryHeadIds));    


            // 4. 查询送货通知行项目
            List<BizReceiptDeliveryNoticeItem> deliveryItems = deliveryNoticeItemDataWrap.listByIds(deliveryItemIds);
            
            if (UtilCollection.isNotEmpty(deliveryItems)) {
                // 5. 查询到货登记行项目
                List<BizReceiptRegisterItem> registerItems = registerItemDataWrap.list(
                    new LambdaQueryWrapper<BizReceiptRegisterItem>()
                        .eq(BizReceiptRegisterItem::getIsDelete, 0)
                        .in(BizReceiptRegisterItem::getPreReceiptItemId, 
                            deliveryItems.stream()
                                .map(BizReceiptDeliveryNoticeItem::getId)
                                .collect(Collectors.toList())));

                if (UtilCollection.isNotEmpty(registerItems)) {
                    

                    

                    // 跟新到货登记采购信息为空
                    registerItemDataWrap.update(
                        new UpdateWrapper<BizReceiptRegisterItem>()
                            .lambda()
                            .set(BizReceiptRegisterItem::getPurchaseCode, Const.STRING_EMPTY)
                            .set(BizReceiptRegisterItem::getPurchaseRid, Const.STRING_EMPTY)
                            .in(BizReceiptRegisterItem::getHeadId, registerItems.stream()
                                .map(BizReceiptRegisterItem::getHeadId)
                                .collect(Collectors.toList())));   

                    registerHeadDataWrap.update(
                        new UpdateWrapper<BizReceiptRegisterHead>()
                            .lambda()
                            .set(BizReceiptRegisterHead::getPurchaseCode, Const.STRING_EMPTY)
                            .in(BizReceiptRegisterHead::getId, registerItems.stream()
                                .map(BizReceiptRegisterItem::getHeadId)
                                .distinct()
                                .collect(Collectors.toList())));    


                   // 查询质检分配单
                   List<BizReceiptInspectItem> inspectItems = inspectItemDataWrap.list(
                       new LambdaQueryWrapper<BizReceiptInspectItem>()
                           .in(BizReceiptInspectItem::getPreReceiptItemId, registerItems.stream()
                               .map(BizReceiptRegisterItem::getId)
                               .collect(Collectors.toList())));

                   if(UtilCollection.isEmpty(inspectItems)){
                       return;
                   }

                   // 查询质检分配抬头
                   List<BizReceiptInspectHead> inspectHeads = inspectHeadDataWrap.listByIds(inspectItems.stream()
                       .map(BizReceiptInspectItem::getHeadId)
                       .distinct()
                       .collect(Collectors.toList()));

                   // 9. 更新质检分配单采购信息为空
                   inspectItemDataWrap.update(
                       new UpdateWrapper<BizReceiptInspectItem>()
                           .lambda()
                           .set(BizReceiptInspectItem::getPurchaseCode, Const.STRING_EMPTY)
                           .set(BizReceiptInspectItem::getPurchaseRid, Const.STRING_EMPTY)
                           .in(BizReceiptInspectItem::getHeadId, inspectHeads.stream().map(o->o.getId()).collect(Collectors.toList())));
                   inspectHeadDataWrap.update(
                       new UpdateWrapper<BizReceiptInspectHead>()
                           .lambda()
                           .set(BizReceiptInspectHead::getPurchaseCode, Const.STRING_EMPTY)
                           .in(BizReceiptInspectHead::getId, inspectHeads.stream().map(o->o.getId()).collect(Collectors.toList())));
                }
            }
        }
    }

    public void updateStatus(BizContext ctx, Integer receiptStatus){
        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(headDTO == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        Long headId = headDTO.getId();
        // 更新物流清关费用状态
        bizReceiptLogisticsHeadDataWrap.update(
                new UpdateWrapper<BizReceiptLogisticsHead>()
                        .lambda()
                        .set(BizReceiptLogisticsHead::getReceiptStatus, receiptStatus)
                        .eq(BizReceiptLogisticsHead::getId, headId));

        bizReceiptLogisticsItemDataWrap.update(
                new UpdateWrapper<BizReceiptLogisticsItem>()
                        .lambda()
                        .set(BizReceiptLogisticsItem::getItemStatus, receiptStatus)
                        .eq(BizReceiptLogisticsItem::getHeadId, headId));
    }


    @Autowired
    private HXSapIntegerfaceService sapIntegerfaceService;

    /**
     * 调用sap生产采购订单
     * @param ctx
     */
    public void createPurchaseSap(BizContext ctx){

        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        HXPurchaseOrderHeader header = new HXPurchaseOrderHeader();


        // 更新状态未同步   
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());


        // 判断 第一个行项目判断，若第一个行项目物料组是Y501资产，则BSART传ZY02，非Y501资产，在BSART传ZY01；
        if(headDTO.getItemList().get(0).getMatGroupCode().equals("Y501")){
            header.setPurchaseOrderType(EnumPurchaseOrderType.ASSET_PO.getCode());
        }else{
            header.setPurchaseOrderType(EnumPurchaseOrderType.STANDARD_PO.getCode());
        }
        
        header.setReferenceReceiptCode(headDTO.getReceiptCode());
        header.setSupplierCode(headDTO.getSupplierCode());
        // header.setPaymentTerms(EnumContractPaymentMethod.getByValue(headDTO.getPaymentMethod()).getDesc());
        header.setCurrency(EnumContractCurrency.getByValue(headDTO.getCurrency()).getDesc());  

        List<HXPurchaseOrderItem> items =new ArrayList<>();


        for(BizReceiptLogisticsItemDTO itemDTO:headDTO.getItemList()){
            HXPurchaseOrderItem item = new HXPurchaseOrderItem();
            item.setItemCode(itemDTO.getRid());
            item.setMatCode(itemDTO.getMatCode());
            if(header.getPurchaseOrderType().equals(EnumPurchaseOrderType.ASSET_PO.getCode())){
                // 资产类采购订单品名使用品名
                item.setMatName(itemDTO.getProductName());  
                item.setAccountCategory("A");
            }else{
                // 生产类采购订单品名使用物料描述
                item.setMatName(itemDTO.getMatName());
                item.setAccountCategory(Const.STRING_EMPTY);
            }
            // 关税
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalTaxCurrency())){
                item.setTariff(UtilBigDecimal.getString(itemDTO.getShareTax()));
                item.setTariffCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalTaxCurrency()).getDesc());
                item.setSupplierCode1(headDTO.getTaxDTO().getTotalTaxSupplierCode());
            }
            // 附加关税
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalAdditionalTaxCurrency())){
                item.setAdditionalTariff(UtilBigDecimal.getString(itemDTO.getShareAdditionalTax()));
                item.setAdditionalTariffCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalAdditionalTaxCurrency()).getDesc());
                item.setSupplierCode2(headDTO.getTaxDTO().getTotalAdditionalTaxSupplierCode());
            }
            // 调节税
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalAdjustmentTaxCurrency())){
                item.setAdjustmentTax(UtilBigDecimal.getString(itemDTO.getShareAdjustmentTax()));
                item.setAdjustmentTaxCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalAdjustmentTaxCurrency()).getDesc());
                item.setSupplierCode3(headDTO.getTaxDTO().getTotalAdjustmentTaxSupplierCode());
            }
            // 联邦消费税
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalFederalConsumptionTaxCurrency())){
                item.setFederalConsumptionTax(UtilBigDecimal.getString(itemDTO.getShareFederalConsumptionTax()));
                item.setFederalConsumptionTaxCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalFederalConsumptionTaxCurrency()).getDesc());
                item.setSupplierCode4(headDTO.getTaxDTO().getTotalFederalConsumptionTaxSupplierCode());
            }   

            // 逾期罚款
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalLatePenaltyCurrency())){
                item.setOverduePenalty(UtilBigDecimal.getString(itemDTO.getShareLatePenalty()));
                item.setOverduePenaltyCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalLatePenaltyCurrency()).getDesc());
                item.setSupplierCode5(headDTO.getTaxDTO().getTotalLatePenaltySupplierCode());
            }

            // 发票缺失
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalInvoiceMissingCurrency())){
                item.setInvoiceMissing(UtilBigDecimal.getString(itemDTO.getShareInvoiceMissing()));
                item.setInvoiceMissingCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalInvoiceMissingCurrency()).getDesc());
                item.setSupplierCode6(headDTO.getTaxDTO().getTotalInvoiceMissingSupplierCode());
            }   

            // GD提交费
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalGdSubmissionFeeCurrency())){
                item.setGdSubmissionFee(UtilBigDecimal.getString(itemDTO.getShareGdSubmissionFee()));
                item.setGdSubmissionFeeCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalGdSubmissionFeeCurrency()).getDesc());
                item.setSupplierCode7(headDTO.getTaxDTO().getTotalGdSubmissionFeeSupplierCode());
            }   
            // 印花税
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalStampDutyCurrency())){
                item.setStampDuty(UtilBigDecimal.getString(itemDTO.getShareStampDuty()));
                item.setStampDutyCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalStampDutyCurrency()).getDesc());
                item.setSupplierCode8(headDTO.getTaxDTO().getTotalStampDutySupplierCode());
            }   
            
            // 基建税
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalInfrastructureTaxCurrency())){
                item.setConstructionTax(UtilBigDecimal.getString(itemDTO.getShareInfrastructureTax()));
                item.setConstructionTaxCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalInfrastructureTaxCurrency()).getDesc());
                item.setSupplierCode9(headDTO.getTaxDTO().getTotalInfrastructureTaxSupplierCode());
            }   
            // 检验检测费
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalInspectionFeeCurrency())){
                item.setInspectionFee(UtilBigDecimal.getString(itemDTO.getShareInspectionFee()));
                item.setInspectionFeeCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalInspectionFeeCurrency()).getDesc());
                item.setSupplierCode10(headDTO.getTaxDTO().getTotalInspectionFeeSupplierCode());
            }      

            // 海/空运费 
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalOceanFreightCurrency())){
                item.setSeaAirFreight(UtilBigDecimal.getString(itemDTO.getShareOceanFreight()));
                item.setSeaAirFreightCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalOceanFreightCurrency()).getDesc());
                item.setSupplierCode11(headDTO.getTaxDTO().getTotalOceanFreightSupplierCode());
            }   
            // 内陆运输费
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalInlandTransportFeeCurrency())){
                item.setInlandTransportFee(UtilBigDecimal.getString(itemDTO.getShareInlandTransportFee()));
                item.setInlandTransportFeeCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalInlandTransportFeeCurrency()).getDesc());
                item.setSupplierCode12(headDTO.getTaxDTO().getTotalInlandTransportFeeSupplierCode());
            }
            
            // 出口报关服务费
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalExportCustomsServiceFeeCurrency())){
                item.setExportCustomsServiceFee(UtilBigDecimal.getString(itemDTO.getShareExportCustomsServiceFee()));
                item.setExportCustomsServiceFeeCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalExportCustomsServiceFeeCurrency()).getDesc());
                item.setSupplierCode13(headDTO.getTaxDTO().getTotalExportCustomsServiceFeeSupplierCode());
            }   

            //进口清关服务费
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalImportCustomsServiceFeeCurrency())){
                item.setImportCustomsServiceFee(UtilBigDecimal.getString(itemDTO.getShareImportCustomsServiceFee()));
                item.setImportCustomsServiceFeeCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalImportCustomsServiceFeeCurrency()).getDesc());
                item.setSupplierCode14(headDTO.getTaxDTO().getTotalImportCustomsServiceFeeSupplierCode());
            }   
            
            // 进口拖车押车费
            if(UtilNumber.isNotEmpty(headDTO.getTaxDTO().getTotalImportTruckFeeCurrency())){   
                item.setImportTruckRentalFee(UtilBigDecimal.getString(itemDTO.getShareImportTruckFee()));
                item.setImportTruckRentalFeeCurrency(EnumContractCurrency.getByValue(headDTO.getTaxDTO().getTotalImportTruckFeeCurrency()).getDesc());
                item.setSupplierCode15(headDTO.getTaxDTO().getTotalImportTruckFeeSupplierCode());
            }

            item.setMaterialGroup(itemDTO.getMatGroupCode());
            item.setQty(UtilBigDecimal.getString(itemDTO.getQty()));
            item.setUnitCode(itemDTO.getUnitCode());
            EnumContractTaxRate taxCode = EnumContractTaxRate.getByKey(itemDTO.getContractTaxCode());
            if(taxCode != null){
                item.setTaxCode(taxCode.getCode());
            }
            item.setNetPrice(UtilBigDecimal.getString(itemDTO.getPoNoTaxPrice()));
            item.setDeliveryDate(UtilDate.convertDateToDateStr(headDTO.getCanDeliveryDate()));
            if(header.getPurchaseOrderType().equals(EnumPurchaseOrderType.ASSET_PO.getCode())){
                item.setAssetCardNumber(itemDTO.getAssetCardNo());
            }
            item.setFactory(itemDTO.getFtyCode());
            items.add(item);
        }   

        header.setItems(items);

        HXPurchaseOrderReturn erpReturnObj = sapIntegerfaceService.createPurchaseOrder(header);

        if(erpReturnObj.getSuccess().equals(Const.ERP_RETURN_TYPE_S)){

            headDTO.setPurchaseCode(erpReturnObj.getPurchaseOrderCode());

            UpdateWrapper<BizReceiptLogisticsHead> headQueryWrapper = new UpdateWrapper<>();
    
            headQueryWrapper.lambda().set(BizReceiptLogisticsHead::getPurchaseCode,headDTO.getPurchaseCode()).eq(BizReceiptLogisticsHead::getId,headDTO.getId());
    
            bizReceiptLogisticsHeadDataWrap.update(headQueryWrapper);
    
        
            if(UtilCollection.isNotEmpty(headDTO.getItemList()) ){
                for(BizReceiptLogisticsItemDTO itemDTO:headDTO.getItemList()){
                    itemDTO.setPurchaseCode(headDTO.getPurchaseCode());
                    // 生成自动流水
                    itemDTO.setPurchaseRid(itemDTO.getRid());
                }
            }
            // 更新送货通知单行项目
            bizReceiptLogisticsItemDataWrap.updateBatchDtoById(headDTO.getItemList());  


        }else{

            throw new WmsException(EnumReturnMsg.RETURN_CODE_CREATE_PURCHASE_ORDER_FAILED,erpReturnObj.getReturnMessage());
        }

    }


    /**
     * 关闭采购单   
     * 校验后续单据是否有未完成状态  到货登记、质检分配单、质检验收、差异通知、差异处置、入库  （根据采购订单号查询后续单据）
     * @param ctx   
     */
    public void closePurchase(BizContext ctx){

        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(headDTO == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        String purchaseCode = headDTO.getPurchaseCode();
        
        this.checkPurchaseCanClosed(purchaseCode);  

        // 调用sap关闭采购订单
        this.closePurchaseSap(headDTO);    

    }   


    /**
     * 关闭采购单   
     * 校验后续单据是否有未完成状态  到货登记、质检分配单、质检验收、差异通知、差异处置、入库  （根据采购订单号查询后续单据）
     * @param ctx   
     */
    public void revokePurchase(BizContext ctx){

        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(headDTO == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }     
        // 调用sap关闭采购订单
        this.closePurchaseSap(headDTO);    

    } 

    /**
     * 撤销采购单校验
     */
    public void checkRevokePurchase(BizContext ctx){
        // 关联的质检分配单必须为空或者为草稿状态，否则不可撤销

        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 根据送货通知行项目id 查询 质检分配单
        List<Long> deliveryItemIds = headDTO.getItemList().stream().map(BizReceiptLogisticsItemDTO::getPreReceiptItemId).collect(Collectors.toList());

        List<BizReceiptInspectItem> inspectItems = inspectItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptInspectItem>()
                .in(BizReceiptInspectItem::getDeliveryItemId, deliveryItemIds));
         
        
    

        if(UtilCollection.isNotEmpty(inspectItems)){

            List<BizReceiptInspectHead> inspectHeads = inspectHeadDataWrap.listByIds(inspectItems.stream().map(BizReceiptInspectItem::getHeadId).collect(Collectors.toList()));
            inspectHeads = inspectHeads.stream().filter(head -> head.getReceiptType().equals(EnumReceiptType.DISTRIBUTE_INSPECTION_PURCHASE.getValue())).collect(Collectors.toList());
            if(UtilCollection.isNotEmpty(inspectHeads)){
                // 如果为已完成状态，则不可撤销
                List<String> inspectCodes = inspectHeads.stream().filter(head -> head.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())).map(BizReceiptInspectHead::getReceiptCode).collect(Collectors.toList());
                if(UtilCollection.isNotEmpty(inspectCodes)){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_ORDER_HAS_FINISHED_INSPECT_ASSIGN ,String.join(",", inspectCodes));
                }
            }   
        }
    }

    /**
     * 调用sap关闭采购订单
     */ 
    public void closePurchaseSap(BizReceiptLogisticsHeadDTO headDTO){

        // 根据采购订单号 查询入库单行项目（排除 草稿状态）
        List<BizReceiptInputItem> inputItems = inputItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptInputItem>()
                .eq(BizReceiptInputItem::getPurchaseCode, headDTO.getPurchaseCode())
                .notIn(BizReceiptInputItem::getItemStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue(),EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue())));

        Map<String,String> inputItemMap = inputItems.stream().collect(Collectors.toMap(e->e.getPurchaseCode()+"_"+e.getPurchaseRid(), e->e.getPurchaseCode()+"_"+e.getPurchaseRid(),(k1,k2)->k1));


        HXPurchaseOrderDeleteHeader header = new HXPurchaseOrderDeleteHeader();
        header.setPurchaseOrderCode(headDTO.getPurchaseCode());
        List<HXPurchaseOrderDeleteItem> items = new ArrayList<>();
        for(BizReceiptLogisticsItemDTO itemDTO:headDTO.getItemList()){
            HXPurchaseOrderDeleteItem item = new HXPurchaseOrderDeleteItem();
            item.setItemCode(itemDTO.getRid());

            String key = inputItemMap.get(itemDTO.getPurchaseCode()+"_"+itemDTO.getPurchaseRid());
            if(UtilString.isNotNullOrEmpty(key)){
                // 已入库
                item.setDeleteFlag("");
                item.setReceiptCompletedFlag("X");
            }else{
                // 未入库
                item.setDeleteFlag("L");
                item.setReceiptCompletedFlag("");
                item.setFtyCode(dictionaryService.getFtyCacheById(itemDTO.getFtyId()).getFtyCode());
            }
            items.add(item);
        }   

        header.setItems(items);

        HXPurchaseOrderReturn erpReturnObj = sapIntegerfaceService.deletePurchaseOrder(header);


        if(erpReturnObj.getSuccess().equals(Const.ERP_RETURN_TYPE_S)){
            // 更新采购单状态为已关闭
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_CLOSE_PURCHASE_ORDER_FAILED,erpReturnObj.getReturnMessage());
        }
    }


    /**
     * 检查采购单是否可以关闭   
     */
    public void checkPurchaseCanClosed(String purchaseCode){
//        // 查询到货登记单
//        List<BizReceiptRegisterHead> registerHeads = registerHeadDataWrap.list(
//            new LambdaQueryWrapper<BizReceiptRegisterHead>()
//                .eq(BizReceiptRegisterHead::getPurchaseCode, purchaseCode)
//                .in(BizReceiptRegisterHead::getReceiptStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())));
//
//        if(UtilCollection.isNotEmpty(registerHeads)){
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_REGISTER ,registerHeads.stream().map(BizReceiptRegisterHead::getReceiptCode).collect(Collectors.joining(",")));
//        }
        // 查询质检分配单 /   质检验收
        List<BizReceiptInspectHead> inspectHeads = inspectHeadDataWrap.list(
            new LambdaQueryWrapper<BizReceiptInspectHead>()
                .eq(BizReceiptInspectHead::getPurchaseCode, purchaseCode)
                .in(BizReceiptInspectHead::getReceiptStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())));        


        if(UtilCollection.isNotEmpty(inspectHeads)){
            // 质检分配单
            List<String> inspectCodes = inspectHeads.stream().filter(head -> head.getReceiptType().equals(EnumReceiptType.DISTRIBUTE_INSPECTION_PURCHASE.getValue())).map(BizReceiptInspectHead::getReceiptCode).collect(Collectors.toList());
            // 质检验收
            List<String> inspectionCodes = inspectHeads.stream().filter(head -> head.getReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue())).map(BizReceiptInspectHead::getReceiptCode).collect(Collectors.toList());

            if(UtilCollection.isNotEmpty(inspectCodes)){    
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_INSPECT_ASSIGN ,String.join(",", inspectCodes));
            }
            if(UtilCollection.isNotEmpty(inspectionCodes)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_INSPECT_SIGN ,String.join(",", inspectionCodes));
            }
        } 
        //  查询差异通知 差异处置
        List<BizReceiptInconformityHead> differenceHeads = inconformityHeadDataWrap.list(
            new LambdaQueryWrapper<BizReceiptInconformityHead>()
                .eq(BizReceiptInconformityHead::getPurchaseCode, purchaseCode)
                .in(BizReceiptInconformityHead::getReceiptStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())));        

        if(UtilCollection.isNotEmpty(differenceHeads)){
            // 差异通知 
            List<String> differenceCodes = differenceHeads.stream().filter(head -> head.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NOTICE.getValue())).map(BizReceiptInconformityHead::getReceiptCode).collect(Collectors.toList());
            // 差异处置
            List<String> disposalCodes = differenceHeads.stream().filter(head -> head.getReceiptType().equals(EnumReceiptType.INCONFORMITY_NAINTAIN.getValue())).map(BizReceiptInconformityHead::getReceiptCode).collect(Collectors.toList());

            if(UtilCollection.isNotEmpty(differenceCodes)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_DIFFERENCE_NOTICE ,String.join(",", differenceCodes));
            }
            if(UtilCollection.isNotEmpty(disposalCodes)){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_DIFFERENCE_DISPOSAL ,String.join(",", disposalCodes));
            }
        }   

        // 查询入库单
        List<BizReceiptInputHead> inputHeads = inputHeadDataWrap.list(
            new LambdaQueryWrapper<BizReceiptInputHead>()
                .eq(BizReceiptInputHead::getPurchaseCode, purchaseCode)
                .in(BizReceiptInputHead::getReceiptStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue(),EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue())));        

        if(UtilCollection.isNotEmpty(inputHeads)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_INPUT ,String.join(",", inputHeads.stream().map(BizReceiptInputHead::getReceiptCode).collect(Collectors.toList())));
        }   
    }


    /**
     *  关闭采购单后回写合同信息
     */
    public void writeBackContract(BizContext ctx){

        BizReceiptLogisticsHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if(headDTO == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        Map<Long, BizReceiptContractItemQtyDTO> contractItemMap = new HashMap<>();



        // 获取已入库数量 查询送货通知单
        List<BizReceiptDeliveryNoticeItem> deliveryNoticeItems = deliveryNoticeItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptDeliveryNoticeItem>()
                .in(BizReceiptDeliveryNoticeItem::getId, headDTO.getItemList().stream().map(BizReceiptLogisticsItemDTO::getPreReceiptItemId).collect(Collectors.toList())));  

        Map<Long, BizReceiptDeliveryNoticeItem> deliveryNoticeItemMap = deliveryNoticeItems.stream().collect(Collectors.toMap(BizReceiptDeliveryNoticeItem::getId, o->o));



        // 设置物流清关费用已入库数量
        headDTO.getItemList().forEach(itemDTO -> {
            BizReceiptDeliveryNoticeItem deliveryNoticeItem = deliveryNoticeItemMap.get(itemDTO.getPreReceiptItemId());
            if(deliveryNoticeItem != null){
                itemDTO.setInputQty(deliveryNoticeItem.getInputQty());
            }
        });


        if(UtilCollection.isNotEmpty(headDTO.getItemList())){
            for(BizReceiptLogisticsItemDTO itemDTO:headDTO.getItemList()){
                Long key = itemDTO.getReferReceiptItemId();
                if(contractItemMap.containsKey(key)){
                    BizReceiptContractItemQtyDTO contractItem = contractItemMap.get(key);
                    contractItem.setSendQty(contractItem.getSendQty().add(itemDTO.getQty().subtract(itemDTO.getInputQty())));
                }else {
                    BizReceiptContractItemQtyDTO contractItem = new BizReceiptContractItemQtyDTO();
                    contractItem.setId(itemDTO.getReferReceiptItemId());
                    contractItem.setSendQty(itemDTO.getQty().subtract(itemDTO.getInputQty()));
                    contractItemMap.put(key,contractItem);
                }
            }
        }

       

        
        // 驳回时回写扣减 数量取相反数
        contractItemMap.values().forEach(item -> item.setSendQty(item.getSendQty().negate()));    
        
        contractItemMap.values().forEach(item -> {
            if(headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())){
                item.setFrameworkContract(true);
            }
        });  
        if(UtilCollection.isNotEmpty(contractItemMap.values())){
            BizContext newCtx = new BizContext();  
            newCtx.setContextData(Const.BIZ_CONTEXT_KEY_LIST,new ArrayList<>(contractItemMap.values()));
            newCtx.setCurrentUser(ctx.getCurrentUser());
            contractComponent.batchUpdateItemQty(newCtx);
        }

    }
}
