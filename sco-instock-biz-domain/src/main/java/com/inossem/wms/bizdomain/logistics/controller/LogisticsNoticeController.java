package com.inossem.wms.bizdomain.logistics.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.inossem.wms.bizdomain.logistics.service.biz.LogisticsService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsHeadDTO;
import com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsDeletePO;
import com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsSearchPO;
import com.inossem.wms.common.model.bizdomain.logistics.vo.BizReceiptLogisticsPreHeadVo;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;

import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 物流清关费用
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */

@RestController
@Api(tags = "物流清关费用管理")
public class LogisticsNoticeController {

    @Autowired
    private LogisticsService logisticsService;

    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @return 库存地点
     */
    @ApiOperation(value = "查询库存地点-根据选中的工厂id获取", tags = {"物流清关费用管理-物流清关费用"})
    @GetMapping(path = "/logistics/notices/location-down/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicStockLocation>> getLocationList(@PathVariable("id") Long id, BizContext ctx) {
        logisticsService.getLocationList(ctx);
        MultiResultVO<DicStockLocation> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 物流清关费用-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "物流清关费用-初始化", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptLogisticsHeadDTO>> init(BizContext ctx) {
        logisticsService.init(ctx);
        BizResultVO<BizReceiptLogisticsHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 物流清关费用-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 物流清关费用单分页
     */
    @ApiOperation(value = "物流清关费用-分页", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptLogisticsHeadDTO>> getPage(@RequestBody BizReceiptLogisticsSearchPO po, BizContext ctx) {
        logisticsService.getPage(ctx);
        PageObjectVO<BizReceiptLogisticsHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 物流清关费用-详情
     *
     * @param id 物流清关费用主键
     * @param ctx 入参上下文 {"id":"物流清关费用主键"}
     */
    @ApiOperation(value = "物流清关费用-详情", tags = {"物流清关费用管理-物流清关费用"})
    @GetMapping(value = "/logistics/notices/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptLogisticsHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        logisticsService.getInfo(ctx);
        BizResultVO<BizReceiptLogisticsHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 物流清关费用-打印详情
     *
     * @param id 物流清关费用主键
     * @param ctx 入参上下文 {"id":"物流清关费用主键"}
     */
    @ApiOperation(value = "物流清关费用-打印详情", tags = {"物流清关费用管理-物流清关费用"})
    @GetMapping(value = "/logistics/notices/print/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptLogisticsHeadDTO>> getPrintInfo(@PathVariable("id") Long id, BizContext ctx) {
        logisticsService.getPrintInfo(ctx);
        BizResultVO<BizReceiptLogisticsHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 物流清关费用-保存
     *
     * @param po 保存物流清关费用单表单
     * @param ctx 入参上下文 {"po":"保存物流清关费用单表单"}
     * @return 物流清关费用单号
     */
    @ApiOperation(value = "物流清关费用-保存", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptLogisticsHeadDTO po, BizContext ctx) {
        logisticsService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_SAVE_SUCCESS, code);
    }

    /**
     * 物流清关费用-提交
     *
     * @param po 提交物流清关费用单表单
     * @param ctx 入参上下文 {"po":"提交物流清关费用单表单"}
     * @return 物流清关费用单号
     */
    @ApiOperation(value = "物流清关费用-提交", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptLogisticsHeadDTO po, BizContext ctx) {
        logisticsService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_SUBMIT_SUCCESS, code);
    }

    /**
     * 物流清关费用-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文 {"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "-删除", tags = {"物流清关费用管理-物流清关费用"})
    @DeleteMapping("/logistics/notices/ids")
    public BaseResult<Long> delete(@RequestBody BizReceiptLogisticsDeletePO po, BizContext ctx) {
        logisticsService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_DELETE_SUCCESS, po.getReceiptCode());
    }

    /**
     * 物流清关费用-前续单据
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件"}
     * @return 物料信息
     */
    @ApiOperation(value = "物流清关费用-前续单据【240:采购订单】", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/mat-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptLogisticsPreHeadVo>> getReferReceiptItemList(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        logisticsService.getReferReceiptItemList(ctx);
        MultiResultVO<BizReceiptLogisticsPreHeadVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 物流清关费用-单据复制
     *
     * @param po 保存物流清关费用单表单
     * @param ctx 入参上下文 {"po":"保存物流清关费用单表单"}
     * @return 物流清关费用单号
     */
    @ApiOperation(value = "物流清关费用-单据复制", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/copy", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> copy(@RequestBody BizReceiptLogisticsHeadDTO po, BizContext ctx) {
        logisticsService.copy(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_SAVE_SUCCESS, code);
    }

/*    *//**
     * 物流清关费用-完成物流清关费用
     *
     * @param po 物流清关费用单
     * @param ctx 入参上下文 {"po":"物流清关费用单"}
     * @return 物流清关费用单号
     *//*
    @ApiOperation(value = "物流清关费用-完成物流清关费用", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/finish_delivery", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> finishDelivery(@RequestBody BizReceiptDeliveryNoticeHeadDTO po, BizContext ctx) {
        deliveryNoticeService.finishDelivery(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_DELIVERY_FINISH_SUCCESS, code);
    }*/

    @ApiOperation(value = "导入", tags = {"导入"})
    @PostMapping(value = "/logistics/notices/import")
    public BaseResult<MultiResultVO<BizReceiptLogisticsCaseRelDTO>> importMatStock(@RequestPart("file") MultipartFile file, BizContext ctx) {
        logisticsService.importCase(ctx);
        List<BizReceiptLogisticsCaseRelDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    @ApiOperation(value = "撤销物流清关", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/revoke")
    public BaseResult revokeLogistics(@RequestBody BizReceiptLogisticsHeadDTO po, BizContext ctx) {
        logisticsService.revokeLogistics(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "关闭物流清关费用单据", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/close")
    public BaseResult closeLogistics(@RequestBody BizReceiptLogisticsHeadDTO po, BizContext ctx) {
        logisticsService.closeLogistics(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "采购单提交", tags = {"物流清关费用管理-物流清关费用"})
    @PostMapping(value = "/logistics/notices/post")
    public BaseResult postPurchase(@RequestBody BizReceiptLogisticsHeadDTO po, BizContext ctx) {
        logisticsService.postPurchase(ctx);
        return BaseResult.success();
    }   
}
