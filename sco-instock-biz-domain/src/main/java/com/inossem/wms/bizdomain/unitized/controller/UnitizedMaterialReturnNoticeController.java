package com.inossem.wms.bizdomain.unitized.controller;

import java.util.List;
import java.util.Map;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedMaterialReturnNoticeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnHeadDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnItemDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnWaybillDTO;
import com.inossem.wms.common.model.masterdata.mat.info.po.BizMaterialReturnSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.po.BizMaterialReturnWayBillSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.BizMaterialReturnHeadVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *     物资返运通知 Controller
 * </p>
 */
@RestController
@Api(tags = "物资返运通知管理")
public class UnitizedMaterialReturnNoticeController {

    @Autowired
    private UnitizedMaterialReturnNoticeService materialReturnService;

    /**
     * 物资返运通知-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "物资返运通知-初始化", tags = {"物资返运通知管理"})
    @PostMapping(value = "/unitized/material-return-notice/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        materialReturnService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 获取物资返运通知列表
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * @return 单位关系集合列表
     */
    @ApiOperation(value = "获取物资返运通知列表", tags = {"物资返运通知管理"})
    @PostMapping(path = "/unitized/material-return-notice/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizMaterialReturnHeadVO>> getPage(@RequestBody BizMaterialReturnSearchPO po, BizContext ctx) {
        materialReturnService.getPage(ctx);
        PageObjectVO<BizMaterialReturnHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 获取物资返-详情
     * @param id 物料Id
     * @return 物料详情
     */
    @ApiOperation(value = "物资返运通知详情", tags = {"物资返运通知管理"})
    @GetMapping(path = "/unitized/material-return-notice/details/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizMaterialReturnHeadDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        materialReturnService.get(ctx);
        BizResultVO<BizMaterialReturnHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 物资返运通知查询运单信息
     * @param id 物料Id
     * @return 物料详情
     */
    @ApiOperation(value = "物资返运通知查询运单信息", tags = {"物资返运通知管理"})
    @PostMapping(path = "/unitized/material-return-notice/search/waybillList", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BizMaterialReturnItemDTO>> waybillList(@RequestBody BizMaterialReturnWayBillSearchPO po, BizContext ctx) {
        return BaseResult.success(materialReturnService.waybillList(ctx));
    }


    /**
     * 新增物资返运通知
     * @param po
     * @return 处理结果
     */
    @ApiOperation(value = "新增物资返运通知", tags = {"物资返运通知管理"})
    @PostMapping(path = "/unitized/material-return-notice/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizMaterialReturnHeadDTO po, BizContext ctx) {
        // 新增物资返运通知
        materialReturnService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.MATERIAL_RETURN_CODE_SAVE_SUCCESS, code);
    }


    /**
     * 物资返运通知-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "物资返运通知-删除", tags = {"入库管理-物资返运通知管理"})
    @DeleteMapping("/unitized/material-return-notice/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        materialReturnService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.MATERIAL_RETURN_CODE_DELETE_SUCCESS);
    }


    /**
     * 提交物资返运通知
     * @param po
     * @return 处理结果
     */
    @ApiOperation(value = "提交物资返运通知", tags = {"物资返运通知管理"})
    @PostMapping(path = "/unitized/material-return-notice/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizMaterialReturnHeadDTO po, BizContext ctx) {
        // 新增物资返运通知
        materialReturnService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.MATERIAL_RETURN_CODE_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "物资返运通知打印", tags = {"物资返运通知管理"})
    @GetMapping(path = "/unitized/material-return-notice/print/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizMaterialReturnHeadDTO>> getInfoPrint(@PathVariable("id") Long id, BizContext ctx) {
        materialReturnService.getInfoPrint(ctx);
        BizResultVO<BizMaterialReturnHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}
