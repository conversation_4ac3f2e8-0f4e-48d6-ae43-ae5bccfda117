package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedStocktakingPlanReportService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingReportHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingReportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportHeadPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 成套设备 计划盘点报告 前端控制器
 * </p>
 */
@RestController
@Api(tags = "成套设备-计划盘点报告")
public class UnitizedStocktakingPlanReportController {

    @Autowired
    protected UnitizedStocktakingPlanReportService stocktakingReportService;

    /**
     * 计划盘点报告-初始化
     *
     * @return 计划盘点报告列表
     */
    @ApiOperation(value = "计划盘点报告-初始化", tags = {"成套设备-盘点管理"})
    @GetMapping(value = "/unitized/stocktaking-plan-report/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptStocktakingReportHeadDTO>> init(BizContext ctx) {
        stocktakingReportService.init(ctx);
        BizResultVO<BizReceiptStocktakingReportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询计划盘点报告单列表-分页
     *
     * @param po 查询条件对象
     * @return 计划盘点报告单列表
     */
    @ApiOperation(value = "查询计划盘点报告列表-分页", tags = {"成套设备-盘点管理"})
    @PostMapping(value = "/unitized/stocktaking-plan-report/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptStocktakingReportHeadPageVO>> getPage(@RequestBody BizReceiptStocktakingReportHeadSearchPO po, BizContext ctx) {
        stocktakingReportService.getPage(ctx);
        PageObjectVO<BizReceiptStocktakingReportHeadPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询计划盘点报告单详情
     *
     * @param headId 库存计划盘点报告头表主键
     * @return 计划盘点报告单详情
     */
    @ApiOperation(value = "查询盘点单详情", tags = {"成套设备-盘点管理"})
    @GetMapping(value = "/unitized/stocktaking-plan-report/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptStocktakingReportHeadDTO>> getInfo(@PathVariable("id") Long headId, BizContext ctx) {
        stocktakingReportService.getInfo(ctx);
        BizResultVO<BizReceiptStocktakingReportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存计划盘点报告单
     *
     * @param po 计划盘点报告单传输对象
     * @return 国际化提示
     */
    @ApiOperation(value = "保存盘点单", tags = {"成套设备-盘点管理"})
    @PostMapping(value = "/unitized/stocktaking-plan-report/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptStocktakingReportHeadDTO po, BizContext ctx) {
        stocktakingReportService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_REPORT_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 提交计划盘点报告单
     *
     * @param po 计划盘点报告单传输对象
     * @return 国际化提示
     */
    @ApiOperation(value = "提交盘点单", tags = {"成套设备-盘点管理"})
    @PostMapping(value = "/unitized/stocktaking-plan-report/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptStocktakingReportHeadDTO po, BizContext ctx) {
        stocktakingReportService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCKTAKING_REPORT_SUBMIT_SUCCESS, receiptCode);
    }

}
