package com.inossem.wms.bizdomain.transport.service.biz;

import com.inossem.wms.bizdomain.transport.service.component.TransportComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportService {

    @Autowired
    private TransportComponent transportComponent;


    /**
     * 移动类型列表
     */
    @Entrance(call = {"transportComponent#getMoveTypeList"})
    public void getMoveTypeList(BizContext ctx) {

        // 移动类型列表
        transportComponent.getMoveTypeList(ctx);
    }

    /**
     * 页面初始化
     */
    @Entrance(call = {"transportComponent#init", "transportComponent#setExtendWf",
        "transportComponent#setExtendAttachment", "transportComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportComponent.init(ctx);

        // 开启审批
        transportComponent.setExtendWf(ctx);

        // 开启附件
        transportComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportComponent.setExtendOperationLog(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transportComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportComponent.getStock(ctx);
    }

    /**
     * 转储物料导入
     */
    @Transactional(rollbackFor = Exception.class)
    public void importMaterial(BizContext ctx) {

        // 查询库存
        transportComponent.importMaterial(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transportComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportComponent.getPage(ctx);
    }

    /**
     * 获取盘点人列表
     *
     * @return 用户列表
     */
    @Entrance(call = {"stocktakingComponent#setUserList"})
    public void getUserList(BizContext ctx) {

        // 查询盘点人列表
        transportComponent.setUserList(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transportComponent#getInfo", "transportComponent#setExtendWf",
        "transportComponent#setExtendAttachment", "transportComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 查询单据详情,包含按钮组和扩展功能
        transportComponent.getInfo(ctx);

        // 开启审批
        transportComponent.setExtendWf(ctx);

        // 开启附件
        transportComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportComponent.setExtendOperationLog(ctx);

        // 开启单据流
        transportComponent.setExtendRelation(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transportComponent#checkSaveData", "transportComponent#setAssembleInputBatchId",
        "transportComponent#save", "transportComponent#saveBizReceiptAttachment",
        "transportComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存时校验数据
        transportComponent.checkSaveData(ctx);

        // 【先过账模式】设置批次id-用于生成接收方批次
        // transportComponent.setAssembleInputBatchId(ctx);

        // 保存
        transportComponent.save(ctx);

        // 保存附件
        transportComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 提交时逻辑处理
     */
    @Entrance(call = {"transportComponent#checkSubmitData", "transportComponent#setAssembleInputBatchId",
        "transportComponent#submit", "transportComponent#occupyStock", "transportComponent#saveBizReceiptAttachment",
        "transportComponent#saveBizReceiptOperationLog", "transportComponent#updateStatusSubmitted",
        "transportComponent#generateInsDocToPost", "transportComponent#generateInsDocToPostByAssemble",
        "transportComponent#checkAndComputeForModifyStock", "transportComponent#post", "transportComponent#modifyStock",
        "transportComponent#modifyLabel", "transportComponent#updateStatusPosted",
        "transportComponent#updateStatusCompleted", "transportComponent#generateOutputTaskReq",
        "transportComponent#saveOutputBinByMoveType", "transportComponent#setMoveTypeListCache"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        BizReceiptTransportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (headDTO!=null && Const.MOVE_TYPE_301.equals(headDTO.getMoveTypeCode()) && EnumRealYn.TRUE.getIntValue().equals(headDTO.getQuickModel())) {
            submitQuickModel(ctx);
            return;
        }
        // 2024-04-23  改为先作业模式，不在转储提交时配货
        // 提交时校验数据
        transportComponent.checkSubmitData(ctx);

        // 【先过账模式】设置批次id-用于生成接收方批次
        // transportComponent.setAssembleInputBatchId(ctx);

        // 提交
        transportComponent.submit(ctx);

        // 保存附件
        transportComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportComponent.saveBizReceiptOperationLog(ctx);

        // 【先过账模式】根据assemble生成ins凭证
        // transportComponent.generateInsDocToPostByAssemble(ctx);

        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
        // transportComponent.checkAndComputeForModifyStock(ctx);

        // 【同时模式-提交】【先过账模式】调用sap接口过账
        // transportComponent.post(ctx);

        // 【同时模式-提交】【先过账模式】修改库存
        // transportComponent.modifyStock(ctx);

        // 修改标签
        // transportComponent.modifyLabel(ctx);

        // 【先过账模式】状态变更-已过帐
        // transportComponent.updateStatusPosted(ctx);

        // 状态变更-已提交
        transportComponent.updateStatusSubmitted(ctx);

        // 【先作业模式】【先过账模式】下架推送
        transportComponent.generateOutputTaskReq(ctx);
    }

    @Transactional(rollbackFor = Exception.class)
    public void submitQuickModel(BizContext ctx) {
        // 提交时校验数据
        transportComponent.checkSubmitData(ctx);

        // 【先过账模式】设置批次id-用于生成接收方批次
        transportComponent.setAssembleInputBatchId(ctx);

        // 提交
        transportComponent.submit(ctx);

        // 保存附件
        transportComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportComponent.saveBizReceiptOperationLog(ctx);

        //快速模式 生成bin表
        transportComponent.saveOutputBinQuickModel(ctx);

        //  快速模式 生成ins凭证
        transportComponent.generateInsDocToPostQuickModel(ctx);

        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
        transportComponent.checkAndComputeForModifyStock(ctx);

        // 【同时模式-提交】【先过账模式】调用sap接口过账
        transportComponent.post(ctx);

        // 【同时模式-提交】【先过账模式】修改库存
        transportComponent.modifyStock(ctx);

        // 修改标签
        transportComponent.modifyLabel(ctx);

        // 【先过账模式】状态变更-已完成
        transportComponent.updateStatusCompleted(ctx);

    }

    /**
     * 过账
     */
    @Entrance(call = {"transportComponent#generateInsDocToPost", "transportComponent#generateInsDocToPostByAssemble",
        "transportComponent#checkAndComputeForModifyStock", "transportComponent#updateStatusUnsync",
        "transportComponent#post", "transportComponent#modifyStock", "transportComponent#deleteOccupyStock",
        "transportComponent#updateStatusCompleted", "transportComponent#updateStatusPosted",
        "transportComponent#setMoveTypeListCache"})
    public void post(BizContext ctx) {
        // 生成ins凭证
        transportComponent.generateInsDocToPost(ctx);
        // 【先过账模式】根据assemble生成ins凭证
        // transportComponent.generateInsDocToPostByAssemble(ctx);

        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
        transportComponent.checkAndComputeForModifyStock(ctx);

        // 状态变更-未同步
        transportComponent.updateStatusUnsync(ctx);

        // 【同时模式-提交】【先过账模式】调用sap接口过账
        transportComponent.post(ctx);

        // 【同时模式-提交】【先过账模式】修改库存
        transportComponent.modifyStock(ctx);

        // 【先过账模式】状态变更-已过帐
        // transportComponent.updateStatusPosted(ctx);

        // 状态变更-已完成
        transportComponent.updateStatusCompleted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transportComponent#checkTaskStatus", "transportComponent#delete",
        "transportComponent#deleteOccupyStock", "transportComponent#deleteBizReceiptAttachment",
        "transportComponent#cancelTaskRequest"})
    public void delete(BizContext ctx) {

        // 删除
        transportComponent.delete(ctx);

        // 逻辑删除附件
        transportComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 冲销
     */
    @Entrance(call = {"transportComponent#checkWriteOffData", "transportComponent#generateInsDocToPostWriteOff",
        "transportComponent#generateInsDocToPostWriteOffByAssemble", "transportComponent#checkAndComputeForModifyStock",
        "transportComponent#writeOff", "transportComponent#modifyStock", "transportComponent#modifyLabel",
        "transportComponent#updateStatusWriteOff", "transportComponent#saveBizReceiptOperationLog",
        "transportComponent#addWriteOffRequest", "transportComponent#setMoveTypeListCache"})
    public void writeOff(BizContext ctx) {

        // 冲销时校验数据
        transportComponent.checkWriteOffData(ctx);

        // 【先过账模式】生成ins凭证-冲销
        // transportComponent.generateInsDocToPostWriteOffByAssemble(ctx);

        // 【先作业模式】【同时模式】-生成ins凭证-冲销
        transportComponent.generateInsDocToPostWriteOff(ctx);

        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
        transportComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账-冲销
        transportComponent.writeOff(ctx);

        // 【同时模式-提交】【先过账模式】修改库存
        transportComponent.modifyStock(ctx);

        // 修改标签
        transportComponent.modifyLabel(ctx);

        // 行项目状态变更-已冲销
        transportComponent.updateStatusWriteOff(ctx);

        // 保存操作日志
        //transportComponent.saveBizReceiptOperationLog(ctx);

        // 【先过帐模式】推送冲销修改请求
        // transportComponent.addWriteOffRequest(ctx);
    }

    /**
     * 下架完成后推送MQ,接收到信息后处理
     */
    @Entrance(call = {"transportComponent#saveOutputBinByTask", "transportComponent#updateUnloadQty",
        "transportComponent#updateStatusTaskByUnloadQty", "transportComponent#checkCanPost",
        "transportComponent#generateInputTaskReq"})
    @WmsMQListener(tags = TagConst.TASK_TRANSPORT_UNLOAD_CALLBACK)
    public void saveByUnloadMq(BizContext ctx) {
        log.info("<============================= 转储下架回调 start=================================>");

        // 根据下架作业单生成bin表
        transportComponent.saveOutputBinByTask(ctx);

        // 修改item上的已下架数量
        transportComponent.updateUnloadQty(ctx);

        // 根据已下架数量判断修改单据状态-已下架作业
        transportComponent.updateStatusTaskByUnloadQty(ctx);

        // 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
        transportComponent.checkCanPost(ctx);

        // 上架推送
        transportComponent.generateInputTaskReq(ctx);

        log.info("<============================= 转储下架回调 end=================================>");
    }

    /**
     * 上架完成后推送MQ,接收到信息后处理
     */
    @Entrance(call = {"transportComponent#updateLoadQty", "transportComponent#updateStatusTaskByLoadQty",
        "transportComponent#updateStatusCompletedByLoadQty", "this#post"})
    @WmsMQListener(tags = TagConst.TASK_TRANSPORT_LOAD_CALLBACK)
    public void saveByLoadMq(BizContext ctx) {

        log.info("<============================= 转储上架回调 start=================================>");

        // 修改item上的已上架数量
        transportComponent.updateLoadQty(ctx);

        // 【先过账模式】根据已下架数量判断修改单据状态已完成
        transportComponent.updateStatusCompletedByLoadQty(ctx);

        // 【先作业后自动过账模式】
        this.post(ctx);

        // 缺件转储时生成对应验收款付款预算
        transportComponent.genPaymentPlan(ctx);

        log.info("<============================= 转储上架回调 end=================================>");
    }

    /**
     * 过门后推送MQ
     */
    @Entrance(call = {"transportComponent#updateFinishQty", "transportComponent#checkCanPost",
        "transportComponent#generatePalletSorting"})
    public void saveUnloadByPassDoorMq(BizContext ctx) {

        // 修改item上的发完成数量
        transportComponent.updateFinishQty(ctx);

        // 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
        transportComponent.checkCanPost(ctx);

        // 生成接收方码盘数据,关联表
        transportComponent.generatePalletSorting(ctx);
    }

    /**
     * 获取WBS集合
     */
    @Entrance(call = {"transportComponent#getWbsList"})
    public void getWbsList(BizContext ctx) {

        // 获取WBS集合
        transportComponent.getWbsList(ctx);
    }

    public void export(BizContext ctx) {
        transportComponent.export(ctx);
    }

    public void importData(BizContext ctx, MultipartFile file, Long outputFtyId, Long outputLocationId, Long inputFtyId,
                           Long inputLocationId) {
        transportComponent.importTransportItem(ctx, file, outputFtyId, outputLocationId, inputFtyId, inputLocationId);
    }
}
