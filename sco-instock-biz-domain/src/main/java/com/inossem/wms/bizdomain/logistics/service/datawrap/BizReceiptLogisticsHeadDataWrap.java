package com.inossem.wms.bizdomain.logistics.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.logistics.dao.BizReceiptLogisticsHeadMapper;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsHead;
import com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsSearchPO;
import com.inossem.wms.common.model.bizdomain.logistics.vo.BizReceiptLogisticsListVo;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 物流清关费用抬头表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Service
public class BizReceiptLogisticsHeadDataWrap extends BaseDataWrap<BizReceiptLogisticsHeadMapper, BizReceiptLogisticsHead> {

    /**
     * 物流清关费用单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 物流清关费用单
     */
    public IPage<BizReceiptLogisticsListVo> getDeliveryNoticePageVo(IPage<BizReceiptLogisticsListVo> pageData,
                                                                    WmsQueryWrapper<BizReceiptLogisticsSearchPO> pageWrapper) {
        return pageData.setRecords(this.baseMapper.selectLogisticsPageVo(pageData, pageWrapper));
    }
    /**
     * 物流清关费用单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 物流清关费用单
     */
    public IPage<BizReceiptLogisticsListVo> getDeliveryNoticePageVoUnitized(IPage<BizReceiptLogisticsListVo> pageData,
                                                                            WmsQueryWrapper<BizReceiptLogisticsSearchPO> pageWrapper) {
        return pageData.setRecords(this.baseMapper.selectLogisticsPageVoUnitized(pageData, pageWrapper));
    }
}
