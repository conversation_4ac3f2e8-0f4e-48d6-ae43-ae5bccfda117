package com.inossem.wms.bizdomain.transport.service.biz;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import com.inossem.wms.common.annotation.Entrance;
import org.springframework.beans.factory.annotation.Autowired;
import com.inossem.wms.bizdomain.transport.service.component.TransportApplyComponent;
import com.inossem.wms.common.model.common.base.BizContext;

/**
 * 调拨申请
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportApplyService {

    @Autowired
    private TransportApplyComponent transportApplyComponent;

    /**
     * 页面初始化
     */
    @Entrance(call = {"transportApplyComponent#init", "transportApplyComponent#setExtendWf",
        "transportApplyComponent#setExtendAttachment", "transportApplyComponent#setExtendOperationLog",
        "transportApplyComponent#setExtendRelation"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportApplyComponent.init(ctx);

        // 开启审批
        transportApplyComponent.setExtendWf(ctx);

        // 开启附件
        transportApplyComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportApplyComponent.setExtendOperationLog(ctx);

        // 开启单据流
        transportApplyComponent.setExtendRelation(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transportApplyComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportApplyComponent.getStock(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transportApplyComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportApplyComponent.getPage(ctx);
    }

    /**
     * 列表 - 没有分页
     */
    @Entrance(call = {"transportApplyComponent#getList"})
    public void getList(BizContext ctx) {

        // 列表 - 没有分页
        transportApplyComponent.getList(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transportApplyComponent#getInfo", "transportApplyComponent#setExtendAttachment",
        "transportApplyComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 详情
        transportApplyComponent.getInfo(ctx);

        // 开启附件
        transportApplyComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportApplyComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transportApplyComponent#check", "transportApplyComponent#save",
        "transportApplyComponent#saveBizReceiptAttachment"})
    public void save(BizContext ctx) {

        // 校验数据
        transportApplyComponent.check(ctx);

        // 保存
        transportApplyComponent.save(ctx);

        // 保存附件
        transportApplyComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交
     */
    @Entrance(call = {"transportApplyComponent#check", "transportApplyComponent#submit",
        "transportApplyComponent#saveBizReceiptAttachment", "transportApplyComponent#updateStatusSubmitted"})
    public void submit(BizContext ctx) {

        // 校验数据
        transportApplyComponent.check(ctx);

        // 提交
        transportApplyComponent.submit(ctx);

        // 保存附件
        transportApplyComponent.saveBizReceiptAttachment(ctx);

        // 状态变更已提交
        transportApplyComponent.updateStatusSubmitted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transportApplyComponent#delete", "transportApplyComponent#deleteBizReceiptAttachment"})
    public void delete(BizContext ctx) {

        // 删除
        transportApplyComponent.delete(ctx);

        // 逻辑删除附件
        transportApplyComponent.deleteBizReceiptAttachment(ctx);
    }

}