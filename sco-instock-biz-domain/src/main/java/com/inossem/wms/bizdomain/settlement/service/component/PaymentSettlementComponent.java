package com.inossem.wms.bizdomain.settlement.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.user.dao.SysUserDeptOfficeRelMapper;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractItemDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractSubItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.invoice.service.datawrap.DicInvoiceDataWrap;
import com.inossem.wms.bizdomain.invoice.service.datawrap.DicInvoiceOccupyDataWrap;
import com.inossem.wms.bizdomain.report.dao.BizReportMapper;
import com.inossem.wms.bizdomain.room.service.component.BizRoomReceiptSettlementComponent;
import com.inossem.wms.bizdomain.settlement.service.datawrap.*;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.auth.EnumUserJob;
import com.inossem.wms.common.enums.contract.EnumContractPaymentNode;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.purchase.EnumPurchaseType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractSubItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractItem;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractSubItem;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.report.po.StockBinSearchPO;
import com.inossem.wms.common.model.bizdomain.report.vo.StockBinVO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptSettlementItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentInvoiceDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentProgressDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementInputItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.*;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.DeliveryVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.LackMatVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.invoice.dto.DicInvoiceDTO;
import com.inossem.wms.common.model.masterdata.invoice.entity.DicInvoice;
import com.inossem.wms.common.model.masterdata.invoice.entity.DicInvoiceOccupy;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import com.inossem.wms.system.workflow.service.business.datawrap.BizApprovalReceiptInstanceRelDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Component
@Slf4j
public class PaymentSettlementComponent {

    @Autowired
    private BizReceiptPaymentSettlementHeadDataWrap paymentSettlementHeadDataWrap;
    @Autowired
    private BizReceiptPaymentSettlementItemDataWrap paymentSettlementItemDataWrap;
    @Autowired
    private BizReceiptPaymentSettlementInputItemDataWrap paymentSettlementInputItemDataWrap;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private BizReceiptContractSubItemDataWrap bizReceiptContractSubItemDataWrap;
    @Autowired
    private BizReportMapper bizReportMapper;
    @Autowired
    private BizReceiptPaymentInvoiceDataWrap paymentInvoiceDataWrap;
    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;
    @Autowired
    private BizReceiptPaymentProgressDataWrap paymentProgressDataWrap;
    @Autowired
    private BizReceiptPaymentLackMatDataWrap bizReceiptPaymentLackMatDataWrap;
    @Autowired
    private I18nTextCommonService i18nTextCommonService;
    @Autowired
    private DicSupplierDataWrap dicSupplierDataWrap;
    @Autowired
    private DicInvoiceDataWrap invoiceDataWrap;
    @Autowired
    private DicInvoiceOccupyDataWrap invoiceOccupyDataWrap;
    @Autowired
    private BizRoomReceiptSettlementComponent bizRoomReceiptSettlementComponent;
    @Autowired
    private SysUserDeptOfficeRelMapper sysUserDeptOfficeRelMapper;
    @Autowired
    private BizApprovalReceiptInstanceRelDataWrap bizApprovalReceiptInstanceRelDataWrap;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private BizReceiptContractItemDataWrap bizReceiptContractItemDataWrap;
    @Autowired
    private BizReceiptPaymentPlanHeadDataWrap bizReceiptPaymentPlanHeadDataWrap;
    @Autowired
    private BizReceiptPaymentPlanItemDataWrap bizReceiptPaymentPlanItemDataWrap;
    @Autowired
    private BizReceiptPaymentRegisterItemDataWrap bizReceiptPaymentRegisterItemDataWrap;
    @Autowired
    private BizReceiptPaymentRegisterHeadDataWrap bizReceiptPaymentRegisterHeadDataWrap;
    @Autowired
    private BizReceiptInvoicePrecastHeadDataWrap bizReceiptInvoicePrecastHeadDataWrap;
    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private DicInvoiceDataWrap dicInvoiceDataWrap;


    /**
     * 页面初始化:
     */
    public void setInit(BizContext ctx) {
        // 获取审批主管
        List<SysUserDTO> list = getSysUserDTOS();


        // 页面初始化设置
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptPaymentSettlementHeadDTO().setReceiptType(EnumReceiptType.PAYMENT_SETTLEMENT.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                        .setApproveUserList(list),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    private List<SysUserDTO> getSysUserDTOS() {
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setJobLevel(EnumApprovalLevel.LEVEL_4.getValue());
        List<SysUserDTO> list = sysUserDeptOfficeRelMapper.getApproveUserList(po);
        return list;
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    public void setInfoExtendRelation(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }


    /**
     * 分页查询付款结算
     */
    public void getPageVo(BizContext ctx) {

        // 上下文入参
        BizReceiptPaymentSettlementSearchPO po = ctx.getPoContextData();

        // 分页处理
        IPage<BizReceiptPaymentSettlementPageVO> page = po.isPaging() ? po.getPageObj(BizReceiptPaymentSettlementPageVO.class) : null;

        // 分页列表查询
        List<BizReceiptPaymentSettlementPageVO> resultList = paymentSettlementHeadDataWrap.getPageVo(page, new WmsLambdaQueryWrapper<BizReceiptPaymentSettlementSearchPO>()
                .eq(true, BizReceiptPaymentSettlementSearchPO::getIsDelete, BizReceiptPaymentSettlementHead.class, EnumRealYn.FALSE.getIntValue())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptPaymentSettlementSearchPO::getReceiptCode, BizReceiptPaymentSettlementHead.class, po.getReceiptCode())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptPaymentSettlementSearchPO::getReceiptStatus, BizReceiptPaymentSettlementHead.class, po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptPaymentSettlementSearchPO::getReceiptCode, BizReceiptContractHead.class, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptPaymentSettlementSearchPO::getContractName, BizReceiptContractHead.class, po.getContractName())
                .eq(UtilNumber.isNotEmpty(po.getPurchaseType()), BizReceiptPaymentSettlementSearchPO::getPurchaseType, BizReceiptContractHead.class, po.getPurchaseType())
                .eq(UtilString.isNotNullOrEmpty(po.getPaymentMonth()), BizReceiptPaymentSettlementSearchPO::getPaymentMonth, BizReceiptPaymentSettlementHead.class, po.getPaymentMonth())
        );

        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(resultList, po.isPaging() ? Objects.requireNonNull(page).getTotal() : resultList.size()));
    }

    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptPaymentSettlementHead head = paymentSettlementHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptPaymentSettlementHeadDTO headDTO =
                UtilBean.newInstance(head, BizReceiptPaymentSettlementHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        ctx.setPoContextData(headDTO);
        this.fillData(ctx);
        // 设置审批主管
        List<SysUserDTO> list = getSysUserDTOS();
        headDTO.setApproveUserList(list);
        SysUser user = dictionaryService.getSysUserCacheByuserCode(headDTO.getApproveUserCode());
        if (Objects.nonNull(user)) {
            headDTO.setApproveUserName(user.getUserName());
        }

        // 获取审批记录
        List<BizApproveRecordDTO> approveList = bizApprovalReceiptInstanceRelDataWrap
                .getApproveRecord(headDTO.getReceiptCode());
        headDTO.setApproveList(approveList);

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentSettlementHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.CAPITAL_PLAN.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public void fillData(BizContext ctx) {
        BizReceiptPaymentSettlementHeadDTO headDTO = ctx.getPoContextData();
        List<BizReceiptPaymentSettlementHead> list = paymentSettlementHeadDataWrap.list(new QueryWrapper<BizReceiptPaymentSettlementHead>().lambda()
                .eq(BizReceiptPaymentSettlementHead::getContractId, headDTO.getContractId())
                .eq(BizReceiptPaymentSettlementHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        // 历次提报金额
        headDTO.setEveryPaidAmount(list.stream().map(BizReceiptPaymentSettlementHead::getPaymentAmount).collect(Collectors.toList()));
        
        // 资金类的，生产物资类的，查询实时的缺件信息
        // if (headDTO.getSettlementType().equals(1) && headDTO.getPurchaseType().equals(EnumPurchaseType.PRODUCTION_MATERIAL.getCode())) {
        //     ctx.setPoContextData(headDTO.getItemList());
        //     this.selectLackMat(ctx);
        //     List<LackMatVO> lackMatVOList = ctx.getVoContextData();
        //     headDTO.setLackMatVOList(lackMatVOList);
        // }
        // 非生产物资类、服务类、施工类,查询分项信息
        List<Integer> purchaseTypeList = Arrays.asList(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode(), EnumPurchaseType.SERVICE.getCode(), EnumPurchaseType.CONSTRUCTION.getCode());
        if (headDTO.getSettlementType().equals(1) && purchaseTypeList.contains(headDTO.getPurchaseType())) {
            ctx.setPoContextData(headDTO.getItemList());
            this.selectSubItem(ctx);
            List<BizReceiptContractSubItemDTO> subItemDTOList = ctx.getVoContextData();
            headDTO.setSubItemList(subItemDTOList);
        }

    }

    /**
     * 按钮组
     *
     * @param headDTO 付款结算单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptPaymentSettlementHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 审批中、已完成 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }
        return buttonVO;
    }

    /**
     * 查询付款计划
     *
     * @param ctx
     */
    public void selectPaymentPlan(BizContext ctx) {

        // 上下文入参
        BizReceiptPaymentPlanSearchPO po = ctx.getPoContextData();
        if (Objects.isNull(po)) {
            po = new BizReceiptPaymentPlanSearchPO();
        }
        if (po.getIsBeyondPlan() == null) {
            po.setIsBeyondPlan(0);
        }
        List<BizReceiptPaymentPlanVO> resultList = paymentSettlementHeadDataWrap.selectPaymentPlan(po);
        dataFillService.fillAttr(resultList);
        List<BizReceiptPaymentPlanHeadDTO> list = UtilCollection.toList(resultList, BizReceiptPaymentPlanHeadDTO.class);
        List<BizReceiptPaymentSettlementItemDTO> itemList = new ArrayList<>();
        BizReceiptPaymentPlanSearchPO finalPo = po;
        list.forEach(item -> {
            BizReceiptPaymentSettlementItemDTO bizReceiptPaymentSettlementItemDTO = new BizReceiptPaymentSettlementItemDTO();
            bizReceiptPaymentSettlementItemDTO.setPaymentPlanId(item.getId());
            bizReceiptPaymentSettlementItemDTO.setPaymentPlanHeadDTO(item);
            bizReceiptPaymentSettlementItemDTO.setIsBeyondPlan(finalPo.getIsBeyondPlan());
            itemList.add(bizReceiptPaymentSettlementItemDTO);

        });

        // 设置返回信息到上下文
        ctx.setVoContextData(itemList);
    }

    public void genPaymentSettlement(BizContext ctx) {

        // 上下文入参
        List<BizReceiptPaymentSettlementItemDTO> po = ctx.getPoContextData();

        CurrentUser currentUser = ctx.getCurrentUser();
        BizReceiptPaymentSettlementHeadDTO headDTO = new BizReceiptPaymentSettlementHeadDTO();
        // 已经保存过
        if (po.stream().anyMatch(c -> UtilNumber.isNotEmpty(c.getHeadId()))) {
            Long headId = po.stream().filter(c -> UtilNumber.isNotEmpty(c.getHeadId())).findFirst().get().getHeadId();
            BizReceiptPaymentSettlementHead paymentSettlementHead = paymentSettlementHeadDataWrap.getById(headId);
            headDTO.setReceiptCode(paymentSettlementHead.getReceiptCode());
            headDTO.setId(paymentSettlementHead.getId());
        } else {
            headDTO.setId(null);
        }

        headDTO.setReceiptType(EnumReceiptType.PAYMENT_SETTLEMENT.getValue());
        headDTO.setSettlementType(1);
        if (Objects.nonNull(po.get(0).getPaymentPlanHeadDTO())) {
            BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = po.get(0).getPaymentPlanHeadDTO();
            headDTO.setContractId(paymentPlanHeadDTO.getContractId());
            headDTO.setContractCode(paymentPlanHeadDTO.getContractCode());
            headDTO.setFirstParty(paymentPlanHeadDTO.getFirstParty());
            headDTO.setContractCurrency(paymentPlanHeadDTO.getCurrency());
            headDTO.setContractCurrencyI18n(paymentPlanHeadDTO.getCurrencyI18n());
            headDTO.setSupplierName(paymentPlanHeadDTO.getSupplierName());
            headDTO.setPurchaseType(paymentPlanHeadDTO.getPurchaseType());
            headDTO.setPaymentMonth(paymentPlanHeadDTO.getPaymentMonth());
            // 计划外的付款结算单“合同基础信息”计划付款月份默认是当前月份
            if (po.get(0).getIsBeyondPlan() == 1) {
                headDTO.setPaymentMonth(YearMonth.from(LocalDate.now()).format(DateTimeFormatter.ofPattern("yyyy-MM")));
            }
            headDTO.setTaxCodeRate(paymentPlanHeadDTO.getTaxCodeRate());
        }
        // else {
        //     headDTO.setContractId(po.get(0).getContractId());
        //     headDTO.setContractCode(po.get(0).getContractCode());
        //     headDTO.setFirstParty(po.get(0).getFirstParty());
        //     headDTO.setSupplierName(po.get(0).getSupplierName());
        //     headDTO.setPaymentMonth(po.get(0).getPaymentMonth());
        //     headDTO.setPurchaseType(po.get(0).getPurchaseType());
        // }
        BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(headDTO.getContractId());
        headDTO.setCreateUserName(currentUser.getUserName());
        headDTO.setCreateTime(new Date());
        List<BizReceiptPaymentSettlementHead> list = paymentSettlementHeadDataWrap.list(new QueryWrapper<BizReceiptPaymentSettlementHead>().lambda()
                .eq(BizReceiptPaymentSettlementHead::getContractId, headDTO.getContractId())
                .eq(BizReceiptPaymentSettlementHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        // 历次提报金额
        headDTO.setEveryPaidAmount(list.stream().map(BizReceiptPaymentSettlementHead::getPaymentAmount).collect(Collectors.toList()));
        List<BizReceiptPaymentSettlementItemDTO> itemList = new ArrayList<>();
        List<BizReceiptPaymentLackMat> paymentLackMats = new ArrayList<>();
        BigDecimal sum = BigDecimal.ZERO;
        for (BizReceiptPaymentSettlementItemDTO itemDTO : po) {
            BizReceiptPaymentSettlementItemDTO bizReceiptPaymentSettlementItemDTO = new BizReceiptPaymentSettlementItemDTO();
            bizReceiptPaymentSettlementItemDTO.setPaymentPlanId(itemDTO.getPaymentPlanHeadDTO().getId());
            // BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = UtilBean.newInstance(bizReceiptPaymentPlanVO, BizReceiptPaymentPlanHeadDTO.class);
            // dataFillService.fillAttr(paymentPlanHeadDTO);
            bizReceiptPaymentSettlementItemDTO.setPaymentPlanHeadDTO(itemDTO.getPaymentPlanHeadDTO());
            sum = sum.add(itemDTO.getPaymentPlanHeadDTO().getQty());
            itemList.add(bizReceiptPaymentSettlementItemDTO);
        }
        headDTO.setItemList(itemList);
        // 生产物资类查询缺件信息
        this.selectLackMat(po, itemList, headDTO, paymentLackMats);

        headDTO.setLackMatVOList(paymentLackMats);

        if (!headDTO.getPurchaseType().equals(EnumPurchaseType.PRODUCTION_MATERIAL.getCode()) && !headDTO.getPurchaseType().equals(EnumPurchaseType.ASSET.getCode())) {
            // 查询分项信息
            List<BizReceiptContractSubItemDTO> subItemDTOList = this.getSubItemDTOList(po);
            headDTO.setSubItemList(subItemDTOList);
            // 查询房费信息,作为扣款信息
            Collection<BizRoomReceiptSettlementItemDTO> roomFeesByContractId = bizRoomReceiptSettlementComponent.getRoomFeesByContractId(headDTO.getContractId());
            BigDecimal sumFee = roomFeesByContractId.stream().map(BizRoomReceiptSettlementItemDTO::getSettlementTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            headDTO.setDeduct14(sumFee.negate());
            // 存一份住房结算单抬头id
            headDTO.setRoomSettlementIds(roomFeesByContractId.stream().map(c -> c.getHeadId().toString()).collect(Collectors.joining(Const.COMMA)));

            List<BizReceiptPaymentProgressDTO> progressList = new ArrayList<>();
            // 每个付款预算单的“计划付款金额/（1+税率）”之和
            BigDecimal bigDecimal = sum.divide(headDTO.getTaxCodeRate().add(new BigDecimal(1)), 2, RoundingMode.HALF_UP);
            BigDecimal bigDecimal1 = BigDecimal.ZERO;
            BigDecimal bigDecimal2 = BigDecimal.ZERO;
            BigDecimal bigDecimal3 = BigDecimal.ZERO;
            BigDecimal bigDecimal4 = BigDecimal.ZERO;
            BigDecimal bigDecimal5 = BigDecimal.ZERO;
            // 查询已完成的进度信息
            List<BizReceiptPaymentProgress> paymentProgresses = paymentProgressDataWrap.list(new QueryWrapper<BizReceiptPaymentProgress>().lambda().
                    eq(BizReceiptPaymentProgress::getContractId, headDTO.getContractId()).
                    eq(BizReceiptPaymentProgress::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
            if (UtilCollection.isNotEmpty(paymentProgresses)) {
                bigDecimal1 = paymentProgresses.stream().filter(c -> "不含税产值".equals(c.getProgressDesc())).map(BizReceiptPaymentProgress::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                bigDecimal2 = paymentProgresses.stream().filter(c -> "信德省销售税".equals(c.getProgressDesc())).map(BizReceiptPaymentProgress::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                bigDecimal3 = paymentProgresses.stream().filter(c -> "含税产值".equals(c.getProgressDesc())).map(BizReceiptPaymentProgress::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                bigDecimal4 = paymentProgresses.stream().filter(c -> "应扣减的金额".equals(c.getProgressDesc())).map(BizReceiptPaymentProgress::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                bigDecimal5 = paymentProgresses.stream().filter(c -> "应支付的合同价款".equals(c.getProgressDesc())).map(BizReceiptPaymentProgress::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            }
            progressList.add(new BizReceiptPaymentProgressDTO().
                    setProgressDesc("不含税产值").
                    setCurrency(contractHead.getCurrency()).
                    setAmount(bigDecimal).
                    setContractId(headDTO.getContractId()).
                    setCumulativeAmount1(bigDecimal1).
                    setCumulativeAmount2(bigDecimal.add(bigDecimal1))
            );
            progressList.add(new BizReceiptPaymentProgressDTO().
                    setProgressDesc("信德省销售税").
                    setCurrency(contractHead.getCurrency()).
                    setAmount(sum.subtract(bigDecimal)).
                    setContractId(headDTO.getContractId()).
                    setCumulativeAmount1(bigDecimal2).
                    setCumulativeAmount2(sum.subtract(bigDecimal).add(bigDecimal2))
            );
            progressList.add(new BizReceiptPaymentProgressDTO().
                    setProgressDesc("含税产值").
                    setCurrency(contractHead.getCurrency()).
                    setAmount(sum).
                    setContractId(headDTO.getContractId()).
                    setCumulativeAmount1(bigDecimal3).
                    setCumulativeAmount2(sum.add(bigDecimal2))
            );
            progressList.add(new BizReceiptPaymentProgressDTO().
                    setProgressDesc("应扣减的金额").
                    setCurrency(contractHead.getCurrency()).
                    setContractId(headDTO.getContractId()).
                    setCumulativeAmount1(bigDecimal4)
            );
            progressList.add(new BizReceiptPaymentProgressDTO().
                    setProgressDesc("应支付的合同价款").
                    setCurrency(contractHead.getCurrency()).
                    setContractId(headDTO.getContractId()).
                    setCumulativeAmount1(bigDecimal5)
            );
            headDTO.setProgressList(progressList);
        } else {
            List<BizReceiptPaymentProgressDTO> progressList = new ArrayList<>();
            // 每个付款预算单的“计划付款金额/（1+税率）”之和
            BigDecimal bigDecimal = sum.divide(headDTO.getTaxCodeRate().add(new BigDecimal(1)), 2, RoundingMode.HALF_UP);
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Net Value不含税价格").setCurrency(contractHead.getCurrency()).setAmount(bigDecimal));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("GST 税码").setCurrency(contractHead.getCurrency()).setAmount(sum.subtract(bigDecimal)));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Total with GST & Freight (PKR)含税总计").setCurrency(contractHead.getCurrency()).setAmount(sum));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Invoice Value 本次发票金额").setCurrency(contractHead.getCurrency()));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Applied Payment申请支付金额").setCurrency(contractHead.getCurrency()));


            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("New Reporting 本次提报金额").setCurrency(contractHead.getCurrency()));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Accumulated Reporting累计提报金额").setCurrency(contractHead.getCurrency()).setAmount(headDTO.getEveryPaidAmount().stream().reduce(BigDecimal.ZERO, BigDecimal::add)));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Contract Value 合同金额").setCurrency(contractHead.getCurrency()).setAmount(contractHead.getContractAmountExcludeTax().add(contractHead.getTaxAmount())));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("The Cumulative Reporting Ratio 截止本次累计提报比例").setCurrency(contractHead.getCurrency()));
            headDTO.setProgressList(progressList);
        }


        // 设置审批主管

        headDTO.setApproveUserList(getSysUserDTOS());
        // 设置返回信息到上下文
        ctx.setVoContextData(headDTO);
    }

    public String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    private String getMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取下个月
        YearMonth nextMonth = YearMonth.from(currentDate).plusMonths(1);
        // 格式化为 "yyyy-MM" 的字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return nextMonth.format(formatter);

    }

    public void genPaymentSettlementByDelivery(BizContext ctx) {

        // 上下文入参
        List<DeliveryVO> po = ctx.getPoContextData();
        CurrentUser currentUser = ctx.getCurrentUser();
        BizReceiptPaymentSettlementHeadDTO headDTO = new BizReceiptPaymentSettlementHeadDTO();

        headDTO.setId(null);
        headDTO.setReceiptType(EnumReceiptType.PAYMENT_SETTLEMENT.getValue());
        headDTO.setSettlementType(2);
        headDTO.setContractId(po.get(0).getContractId());
        BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(po.get(0).getContractId());
        headDTO.setContractCode(contractHead.getReceiptCode());
        headDTO.setDeliveryId(po.get(0).getId());
        headDTO.setDeliveryCode(po.get(0).getReceiptCode());
        headDTO.setFirstParty(contractHead.getFirstParty());
        headDTO.setSupplierName(po.get(0).getSupplierName());
        headDTO.setTaxCode(po.get(0).getTaxCode());
        headDTO.setContractCurrency(contractHead.getCurrency());
        headDTO.setContractCurrencyI18n(i18nTextCommonService.getNameMessage(getLangCodeFromRequest(), "currency", contractHead.getCurrency().toString()));
        headDTO.setTaxAmount(po.get(0).getTaxAmount());
        headDTO.setPaymentMonth(this.getMonth());
        headDTO.setCreateUserName(currentUser.getUserName());
        headDTO.setCreateTime(new Date());
        List<BizReceiptPaymentSettlementHead> list = paymentSettlementHeadDataWrap.list(new QueryWrapper<BizReceiptPaymentSettlementHead>().lambda()
                .eq(BizReceiptPaymentSettlementHead::getContractId, headDTO.getContractId())
                .eq(BizReceiptPaymentSettlementHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        // 历次提报金额
        headDTO.setEveryPaidAmount(list.stream().map(BizReceiptPaymentSettlementHead::getPaymentAmount).collect(Collectors.toList()));
        List<BizReceiptPaymentSettlementItemDTO> itemList = new ArrayList<>();
        for (DeliveryVO deliveryVO : po) {
            dataFillService.fillAttr(deliveryVO);
            BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO = UtilBean.newInstance(deliveryVO, BizReceiptDeliveryNoticeHeadDTO.class);
            BizReceiptPaymentSettlementItemDTO bizReceiptPaymentSettlementItemDTO = new BizReceiptPaymentSettlementItemDTO();
            bizReceiptPaymentSettlementItemDTO.setDeliveryHeadId(deliveryVO.getId());
            List<BizReceiptContractItem> receiptContractItems = bizReceiptContractItemDataWrap.list(new QueryWrapper<BizReceiptContractItem>().lambda().eq(BizReceiptContractItem::getHeadId,
                    headDTO.getContractId()));
            BigDecimal taxCodeRate = receiptContractItems.get(0).getTaxCodeRate().add(new BigDecimal(1));
            deliveryNoticeHeadDTO.getItemList().forEach(c -> c.setPoTaxPrice(c.getPoNoTaxPrice().multiply(taxCodeRate))
                    .setPoTaxAmount(c.getPoNoTaxAmount().multiply(taxCodeRate)));
            bizReceiptPaymentSettlementItemDTO.setDeliveryNoticeHeadDTO(deliveryNoticeHeadDTO);
            itemList.add(bizReceiptPaymentSettlementItemDTO);
        }
        headDTO.setItemList(itemList);

        List<BizReceiptPaymentProgressDTO> progressList = new ArrayList<>();
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Net Value不含税价格").setCurrency(contractHead.getCurrency()).setAmount(contractHead.getContractAmountExcludeTax()));
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("GST 税码").setCurrency(contractHead.getCurrency()).setAmount(contractHead.getTaxAmount()));
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Total with GST & Freight (PKR)含税总计").setCurrency(contractHead.getCurrency()).setAmount(headDTO.getTaxAmount()));
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Invoice Value 本次发票金额").setCurrency(contractHead.getCurrency()));
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Applied Payment申请支付金额").setCurrency(contractHead.getCurrency()));


        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("New Reporting 本次提报金额").setCurrency(contractHead.getCurrency()));
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Accumulated Reporting累计提报金额").setCurrency(contractHead.getCurrency()).setAmount(headDTO.getEveryPaidAmount().stream().reduce(BigDecimal.ZERO, BigDecimal::add)));
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Contract Value 合同金额").setCurrency(contractHead.getCurrency()).setAmount(contractHead.getContractAmountExcludeTax().add(contractHead.getTaxAmount())));
        progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("The Cumulative Reporting Ratio 截止本次累计提报比例").setCurrency(contractHead.getCurrency()));
        headDTO.setProgressList(progressList);
        headDTO.setApproveUserList(getSysUserDTOS());
        // 设置返回信息到上下文
        ctx.setVoContextData(headDTO);
    }

    private List<BizReceiptContractSubItemDTO> getSubItemDTOList(List<BizReceiptPaymentSettlementItemDTO> po) {
        List<BizReceiptContractSubItemDTO> subItemDTOList = new ArrayList<>();
        for (BizReceiptPaymentSettlementItemDTO itemDTO : po) {
            List<BizReceiptContractSubItem> subItems = bizReceiptContractSubItemDataWrap.list(new QueryWrapper<BizReceiptContractSubItem>().lambda().eq
                    (BizReceiptContractSubItem::getReceiveId, itemDTO.getPaymentPlanHeadDTO().getReceiveId()));
            Map<String, List<BizReceiptContractSubItem>> map = subItems.stream().collect(Collectors.groupingBy(BizReceiptContractSubItem::getSubItemName));

            map.forEach((k, v) -> {
                BizReceiptContractSubItemDTO subItemDTO = new BizReceiptContractSubItemDTO();
                UtilBean.copy(v.get(0), subItemDTO);
                // 数量取合并后的
                subItemDTO.setPaymentPlanQty(v.stream().map(BizReceiptContractSubItem::getReceiveQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTO.setPaymentPlanValue(v.stream().map(BizReceiptContractSubItem::getReceiveValue).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTOList.add(subItemDTO);
            });

        }
        return subItemDTOList;
    }

    private void selectLackMat(List<BizReceiptPaymentSettlementItemDTO> po, List<BizReceiptPaymentSettlementItemDTO> itemList, BizReceiptPaymentSettlementHeadDTO headDTO, List<BizReceiptPaymentLackMat> paymentLackMats) {
        // 缺件信息隶属于验收款，缺件价值按验收款支付比例计算
        Optional<BizReceiptPaymentSettlementItemDTO> optionalItem = po.stream()
                .filter(c -> EnumContractPaymentNode.NODE_5.getCode().equals(c.getPaymentPlanHeadDTO().getPaymentNode()))
                .findFirst();
        // 来自转储的，不用再查缺件
        Optional<BizReceiptPaymentSettlementItemDTO> fromTransport = po.stream()
                .filter(c -> c.getPaymentPlanHeadDTO().getFromTransport() == 1)
                .findFirst();
        if (fromTransport.isPresent()) {
            return;
        }
        BigDecimal rate;
        if (optionalItem.isPresent()) {
            BizReceiptPaymentSettlementItemDTO itemDTO = optionalItem.get();
            rate = itemDTO.getPaymentPlanHeadDTO().getRate();
        } else {
            return;
        }

        if (headDTO.getPurchaseType().equals(EnumPurchaseType.PRODUCTION_MATERIAL.getCode())) {
            // 限制YQ01 缺件库
            List<Long> locationIdList = new ArrayList<>();
            Long locationId = dictionaryService.getLocationIdCacheByCode("1104", "YQ01");
            locationIdList.add(locationId);
            // 安装采购订单号和rid分组
            Map<String, Map<String, List<BizReceiptPaymentPlanItemDTO>>> groupedMap = itemList.stream().flatMap(item -> item.getPaymentPlanHeadDTO().getItemList().stream()).collect(Collectors.groupingBy(BizReceiptPaymentPlanItemDTO::getPurchaseReceiptCode,
                    Collectors.groupingBy(BizReceiptPaymentPlanItemDTO::getPurchaseReceiptRid)));

            for (Map.Entry<String, Map<String, List<BizReceiptPaymentPlanItemDTO>>> codeEntry : groupedMap.entrySet()) {
                String purchaseReceiptCode = codeEntry.getKey();
                if (UtilString.isNullOrEmpty(purchaseReceiptCode)) {
                    continue;
                }
                Map<String, List<BizReceiptPaymentPlanItemDTO>> ridMap = codeEntry.getValue();

                for (Map.Entry<String, List<BizReceiptPaymentPlanItemDTO>> ridEntry : ridMap.entrySet()) {
                    String purchaseReceiptRid = ridEntry.getKey();
                    List<BizReceiptPaymentPlanItemDTO> items = ridEntry.getValue();

                    StockBinSearchPO stockBinSearchPO = new StockBinSearchPO();
                    stockBinSearchPO.setLocationIdList(locationIdList);
                    stockBinSearchPO.setContractCode(headDTO.getContractCode());
                    stockBinSearchPO.setIsUnitized(false);
                    stockBinSearchPO.setPurchaseReceiptCode(purchaseReceiptCode);
                    stockBinSearchPO.setPurchaseReceiptRid(purchaseReceiptRid);

                    BigDecimal taxPrice = items.isEmpty() ? BigDecimal.ZERO : items.get(0).getTaxPrice();

                    List<StockBinVO> stockBinVOS = bizReportMapper.selectStockBinDetail(null, stockBinSearchPO);
                    dataFillService.fillAttr(stockBinVOS);
                    if (UtilCollection.isNotEmpty(stockBinVOS)) {
                        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = po.get(0).getPaymentPlanHeadDTO();
                        for (StockBinVO stockBinVO : stockBinVOS) {
                            BizReceiptPaymentLackMat paymentLackMat = new BizReceiptPaymentLackMat();
                            UtilBean.copy(stockBinVO, paymentLackMat);
                            paymentLackMat.setPurchaseReceiptCode(stockBinVO.getBatchInfo().getPurchaseCode());
                            paymentLackMat.setPurchaseReceiptRid(stockBinVO.getBatchInfo().getPurchaseRid());
                            paymentLackMat.setLackValue(paymentLackMat.getQty().multiply(taxPrice).multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).negate());
                            paymentLackMat.setCurrency(paymentPlanHeadDTO.getCurrency());
                            paymentLackMats.add(paymentLackMat);
                        }
                    }
                }
            }

        }
    }

    /**
     * 查询缺件信息
     *
     * @param ctx ctx
     */
    public void selectLackMat(BizContext ctx) {

        List<BizReceiptPaymentSettlementItemDTO> po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 限制YQ01 缺件库
        List<Long> locationIdList = new ArrayList<>();
        Long locationId = dictionaryService.getLocationIdCacheByCode("1104", "YQ01");
        locationIdList.add(locationId);
        List<StockBinVO> result = new ArrayList<>();
        for (BizReceiptPaymentSettlementItemDTO vo : po) {
            Map<String, Map<String, List<BizReceiptPaymentPlanItemDTO>>> groupedMap = vo.getPaymentPlanHeadDTO().getItemList().stream()
                    .collect(Collectors.groupingBy(
                            BizReceiptPaymentPlanItemDTO::getPurchaseReceiptCode,
                            Collectors.groupingBy(BizReceiptPaymentPlanItemDTO::getPurchaseReceiptRid)
                    ));

            for (Map.Entry<String, Map<String, List<BizReceiptPaymentPlanItemDTO>>> codeEntry : groupedMap.entrySet()) {
                String purchaseReceiptCode = codeEntry.getKey();
                Map<String, List<BizReceiptPaymentPlanItemDTO>> ridMap = codeEntry.getValue();

                for (Map.Entry<String, List<BizReceiptPaymentPlanItemDTO>> ridEntry : ridMap.entrySet()) {
                    String purchaseReceiptRid = ridEntry.getKey();
                    List<BizReceiptPaymentPlanItemDTO> items = ridEntry.getValue();

                    StockBinSearchPO stockBinSearchPO = new StockBinSearchPO();
                    stockBinSearchPO.setLocationIdList(locationIdList);
                    stockBinSearchPO.setContractCode(vo.getPaymentPlanHeadDTO().getContractCode());
                    stockBinSearchPO.setIsUnitized(false);
                    stockBinSearchPO.setPurchaseReceiptCode(purchaseReceiptCode);
                    stockBinSearchPO.setPurchaseReceiptRid(purchaseReceiptRid);

                    BigDecimal taxPrice = items.isEmpty() ? BigDecimal.ZERO : items.get(0).getTaxPrice();

                    List<StockBinVO> stockBinVOS = bizReportMapper.selectStockBinDetail(null, stockBinSearchPO);
                    if (UtilCollection.isNotEmpty(stockBinVOS)) {
                        stockBinVOS.forEach(c -> c.setTaxPrice(taxPrice));
                        result.addAll(stockBinVOS);
                    }
                }
            }
        }
        dataFillService.fillAttr(result);
        List<LackMatVO> lackMatVOList = new ArrayList<>();
        for (StockBinVO stockBinVO : result) {
            LackMatVO lackMatVO = UtilBean.newInstance(stockBinVO, LackMatVO.class);
            lackMatVO.setPurchaseReceiptCode(stockBinVO.getBatchInfo().getPurchaseCode());
            lackMatVO.setPurchaseReceiptRid(stockBinVO.getBatchInfo().getPurchaseRid());
            lackMatVO.setLackValue(lackMatVO.getQty().multiply(stockBinVO.getTaxPrice()).negate());
            lackMatVOList.add(lackMatVO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, lackMatVOList);
    }

    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentSettlementHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 保存-校验入参
     */
    public void checkSubmitData(BizContext ctx) {
        checkSaveData(ctx);
        // 入参上下文
        BizReceiptPaymentSettlementHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilCollection.isEmpty(po.getFileList())) {
            throw new WmsException("必须上传附件");
        }
        // 只有预付款节点可以没有发票
        if (po.getItemList().stream().anyMatch(c -> c.getPaymentPlanHeadDTO() != null &&
                !c.getPaymentPlanHeadDTO().getPaymentNode().equals(EnumContractPaymentNode.NODE_1.getCode()))) {
            if (UtilCollection.isEmpty(po.getInvoiceList())) {
                throw new WmsException("必须上传发票");
            }
            BizReceiptPaymentProgressDTO paymentProgressDTO = po.getProgressList().get(2);
            BigDecimal amount = paymentProgressDTO.getInvoiceAmount();
            List<Long> invoiceIds = po.getInvoiceList().stream().map(BizReceiptPaymentInvoiceDTO::getInvoiceId).collect(Collectors.toList());
            BigDecimal sum = invoiceDataWrap.listByIds(invoiceIds).stream().map(c -> c.getInvoiceAmount().subtract(c.getOccupyInvoiceAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (sum.compareTo(amount) < 0) {
                throw new WmsException("发票剩余可用金额不足抵扣");
            }
        }

        BizReceiptPaymentProgressDTO paymentProgressDTO1 = po.getProgressList().get(4);
        if (paymentProgressDTO1.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new WmsException("申请支付金额不可为负数");
        }
    }


    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentSettlementHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.PAYMENT_SETTLEMENT.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            paymentSettlementHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteItem(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_SETTLEMENT.getValue());
            po.setReceiptCode(code);
            po.setId(null);
            paymentSettlementHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }

        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptPaymentSettlementItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }
        paymentSettlementItemDataWrap.saveBatchDto(po.getItemList());

        /* ********************** item处理结束 *************************/
        AtomicInteger invoiceRid = new AtomicInteger(1);
        for (BizReceiptPaymentInvoiceDTO paymentInvoiceDTO : po.getInvoiceList()) {
            paymentInvoiceDTO.setId(null);
            paymentInvoiceDTO.setHeadId(po.getId());
            paymentInvoiceDTO.setRid(Integer.toString(invoiceRid.getAndIncrement()));

        }
        paymentInvoiceDataWrap.saveBatchDto(po.getInvoiceList());

        AtomicInteger progressRid = new AtomicInteger(1);
        BigDecimal qty = BigDecimal.ZERO;
        for (BizReceiptPaymentProgressDTO paymentProgressDTO : po.getProgressList()) {
            paymentProgressDTO.setId(null);
            paymentProgressDTO.setHeadId(po.getId());
            paymentProgressDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            paymentProgressDTO.setRid(Integer.toString(progressRid.getAndIncrement()));
            if ("Applied Payment申请支付金额".equals(paymentProgressDTO.getProgressDesc()) || "应支付的合同价款".equals(paymentProgressDTO.getProgressDesc())) {
                qty = paymentProgressDTO.getAmount();
            }

        }
        paymentProgressDataWrap.saveBatchDto(po.getProgressList());

        AtomicInteger Rid = new AtomicInteger(1);
        for (BizReceiptPaymentLackMat paymentLackMat : po.getLackMatVOList()) {
            paymentLackMat.setId(null);
            paymentLackMat.setHeadId(po.getId());
            paymentLackMat.setRid(Integer.toString(Rid.getAndIncrement()));

        }
        bizReceiptPaymentLackMatDataWrap.saveBatchDto(po.getLackMatVOList());

        AtomicInteger inputItemRid = new AtomicInteger(1);
        for (BizReceiptPaymentSettlementInputItemDTO inputItemDTO : po.getInputItemList()) {
            inputItemDTO.setId(null);
            inputItemDTO.setHeadId(po.getId());
            inputItemDTO.setRid(Integer.toString(inputItemRid.getAndIncrement()));
        }
        paymentSettlementInputItemDataWrap.saveBatchDto(po.getInputItemList());

        if (UtilNumber.isEmpty(po.getInvoiceCurrency()) && UtilCollection.isNotEmpty(po.getInvoiceList())) {
            po.setInvoiceCurrency(po.getInvoiceList().get(0).getInvoice().getInvoiceCurrency());
            po.setInvoiceAmount(po.getInvoiceList().get(0).getInvoice().getExchangeRate().multiply(qty));
        }
        // 更新申请支付金额
        paymentSettlementHeadDataWrap.updateDtoById(po.setPaymentAmount(qty));

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }

    private void deleteItem(BizReceiptPaymentSettlementHeadDTO headDTO) {
        UpdateWrapper<BizReceiptPaymentSettlementItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(BizReceiptPaymentSettlementItem::getHeadId, headDTO.getId());
        paymentSettlementItemDataWrap.physicalDelete(wrapper);

        UpdateWrapper<BizReceiptPaymentInvoice> wrapper1 = new UpdateWrapper<>();
        wrapper1.lambda().eq(BizReceiptPaymentInvoice::getHeadId, headDTO.getId());
        paymentInvoiceDataWrap.physicalDelete(wrapper1);

        UpdateWrapper<BizReceiptPaymentProgress> wrapper2 = new UpdateWrapper<>();
        wrapper2.lambda().eq(BizReceiptPaymentProgress::getHeadId, headDTO.getId());
        paymentProgressDataWrap.physicalDelete(wrapper2);

        UpdateWrapper<BizReceiptPaymentLackMat> wrapper3 = new UpdateWrapper<>();
        wrapper3.lambda().eq(BizReceiptPaymentLackMat::getHeadId, headDTO.getId());
        bizReceiptPaymentLackMatDataWrap.physicalDelete(wrapper3);

        UpdateWrapper<BizReceiptPaymentSettlementInputItem> wrapper4 = new UpdateWrapper<>();
        wrapper4.lambda().eq(BizReceiptPaymentSettlementInputItem::getHeadId, headDTO.getId());
        paymentSettlementInputItemDataWrap.physicalDelete(wrapper4);
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentSettlementHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存
        this.save(ctx);
        po.setSubmitTime(UtilDate.getNow()).setSubmitUserId(ctx.getCurrentUser().getId());
        // 更新付款结算head、item状态 - 审批中
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        // 更新住房结算状态（结算中）
        this.updateRoomSettlement(po, EnumReceiptStatus.RECEIPT_STATUS_SETTLEMENT_ING);
        // 更新付款预算-待结算
        this.updatePaymentPlanStatus(po, EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT);
    }



    public void updateStatus(BizReceiptPaymentSettlementHeadDTO headDTO, List<BizReceiptPaymentSettlementItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新付款结算head状态
     *
     * @param headDto 付款结算head
     */
    private void updateHead(BizReceiptPaymentSettlementHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            paymentSettlementHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新付款结算item状态
     *
     * @param itemDtoList 付款结算item
     */
    private void updateItem(List<BizReceiptPaymentSettlementItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            paymentSettlementItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要保持附件的不符合项单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的不符合项单
        BizReceiptPaymentSettlementHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存不符合项单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存操作日志的不符合项单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的不符合项单
        BizReceiptPaymentSettlementHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }


    public void startWorkFlow(BizContext ctx) {
        BizReceiptPaymentSettlementHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 审批人校验
        this.approveCheck(headDTO);
        // 发起流程审批
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();
        variables.put("userCode", headDTO.getApproveUserCode());

        // 付款结算：“请审批”[公司+部门]用户姓名+“提交的流程”+付款结算描述（取付款结算抬头付款结算描述）
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getSettlementDesc());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getSettlementDesc());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());

    }

    /**
     * 审批人校验
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    private void approveCheck(BizReceiptPaymentSettlementHeadDTO headDTO) {
        // 一级审批经营部主管
        if (UtilString.isNullOrEmpty(headDTO.getApproveUserCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_1_APPROVAL.getValue().toString());
        }
        // 二级审批经营部负责
        String deptCode = EnumDept.BMD.getCode();
        List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_2_APPROVAL.getValue().toString());
        }
        // 二级审批经营部部门分管领导
        List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_3_APPROVAL.getValue().toString());
        }

    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptPaymentSettlementHead head = paymentSettlementHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptPaymentSettlementHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptPaymentSettlementHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {

            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            // 更新支付进度信息已完成
            this.updateProgressStatus(headDTO);
            if (headDTO.getSettlementType().equals(3)) {
                for (BizReceiptPaymentSettlementItemDTO itemDTO : headDTO.getItemList()) {
                    // 更新入库单已创建付款结算
                    bizReceiptInputHeadDataWrap.update(new UpdateWrapper<BizReceiptInputHead>().
                            lambda().
                            set(BizReceiptInputHead::getSettlementStatus, EnumRealYn.TRUE.getIntValue()).
                            eq(BizReceiptInputHead::getId, itemDTO.getInputHeadId()));
                }
            }
            // 更新发票可用金额（以含税总计为标的）
            this.updateInvoice(headDTO);
            // 更新住房结算状态（已结算）
            this.updateRoomSettlement(headDTO, EnumReceiptStatus.RECEIPT_STATUS_SETTLEMENT_COMPLETED);
            // 更新付款预算单状态为已完成
            this.updatePaymentPlanStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_SETTLEMENT_COMPLETED);
        } else {

            // 如果驳回时携带了废弃标记，则直接关闭单据
            if(EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())){
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());

                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "付款结算的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            } else {
                // 更新状态已驳回
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());

                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "付款结算的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            }
        }
    }

    private void updateProgressStatus(BizReceiptPaymentSettlementHeadDTO headDTO) {
        headDTO.getProgressList().forEach(c -> c.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        paymentProgressDataWrap.updateBatchDtoById(headDTO.getProgressList());
    }

    private void updatePaymentPlanStatus(BizReceiptPaymentSettlementHeadDTO headDTO, EnumReceiptStatus enumReceiptStatus) {
        if (headDTO.getSettlementType().equals(1)) {
            List<Long> paymentPlanIds = headDTO.getItemList().stream().map(BizReceiptPaymentSettlementItemDTO::getPaymentPlanId).collect(Collectors.toList());
            bizReceiptPaymentPlanHeadDataWrap.update(new UpdateWrapper<BizReceiptPaymentPlanHead>().
                    lambda().
                    set(BizReceiptPaymentPlanHead::getReceiptStatus, enumReceiptStatus.getValue()).
                    in(BizReceiptPaymentPlanHead::getId, paymentPlanIds));
            bizReceiptPaymentPlanItemDataWrap.update(new UpdateWrapper<BizReceiptPaymentPlanItem>().
                    lambda().
                    set(BizReceiptPaymentPlanItem::getItemStatus, enumReceiptStatus.getValue()).
                    in(BizReceiptPaymentPlanItem::getHeadId, paymentPlanIds));

        }
    }

    private void updateRoomSettlement(BizReceiptPaymentSettlementHeadDTO headDTO, EnumReceiptStatus receiptStatus) {
        if (UtilString.isNotNullOrEmpty(headDTO.getRoomSettlementIds())) {
            List<String> list = Arrays.asList(headDTO.getRoomSettlementIds().split(Const.COMMA));
            if (UtilCollection.isNotEmpty(list)) {
                list.forEach(c -> bizRoomReceiptSettlementComponent.updateStatus(Long.valueOf(c), receiptStatus));

            }
        }
    }

    private void updateInvoice(BizReceiptPaymentSettlementHeadDTO headDTO) {
        // 含税合计
        // BizReceiptPaymentProgressDTO paymentProgressDTO = headDTO.getProgressList().stream().filter(c -> "3".equals(c.getRid())).findFirst().get();

        // 本次发票金额
        BigDecimal amount = headDTO.getInvoiceAmount();
        List<DicInvoiceDTO> updateList = new ArrayList<>();
        List<DicInvoiceOccupy> occupyList = new ArrayList<>();
        if(UtilCollection.isNotEmpty(headDTO.getInvoiceList())){
            for (BizReceiptPaymentInvoiceDTO paymentInvoiceDTO : headDTO.getInvoiceList()) {
                DicInvoiceDTO invoice = paymentInvoiceDTO.getInvoice();
                // 发票剩余可用金额 = 发票金额 - 已占用发票金额
                BigDecimal remain = invoice.getInvoiceAmount().subtract(invoice.getOccupyInvoiceAmount());
                // 抬头本次发票金额 小于等于 发票剩余可用金额
                if (amount.compareTo(remain) <= 0) {
                    // 已占用发票金额 = 已占用发票金额 + 抬头本次发票金额
                    invoice.setOccupyInvoiceAmount(invoice.getOccupyInvoiceAmount().add(amount));
                    updateList.add(invoice);
                    occupyList.add(new DicInvoiceOccupy()
                            .setInvoiceId(invoice.getId())
                            .setOccupyReceiptHeadId(headDTO.getId())
                            .setOccupyReceiptType(headDTO.getReceiptType())
                            .setRemark(headDTO.getReceiptCode())
                            .setOccupyAmount(amount));
                    break;
                } else {
                    // 已占用发票金额 = 发票金额
                    invoice.setOccupyInvoiceAmount(invoice.getInvoiceAmount());
                    amount = amount.subtract(remain);
                    updateList.add(invoice);
                    occupyList.add(new DicInvoiceOccupy()
                            .setInvoiceId(invoice.getId())
                            .setOccupyReceiptHeadId(headDTO.getId())
                            .setOccupyReceiptType(headDTO.getReceiptType())
                            .setRemark(headDTO.getReceiptCode())
                            .setOccupyAmount(remain));
                }
            }
            if (UtilCollection.isNotEmpty(updateList)) {
                invoiceDataWrap.updateBatchDtoById(updateList);
            }
            if (UtilCollection.isNotEmpty(occupyList)) {
                invoiceOccupyDataWrap.saveBatch(occupyList);
            }
        }
    }

    private void revokeInvoice(BizReceiptPaymentSettlementHeadDTO headDTO) {
        List<DicInvoiceOccupy> invoiceOccupyList = invoiceOccupyDataWrap.list(new LambdaQueryWrapper<DicInvoiceOccupy>().eq(DicInvoiceOccupy::getOccupyReceiptHeadId, headDTO.getId()));
        List<Long> invoiceIdList = invoiceOccupyList.stream().map(DicInvoiceOccupy::getInvoiceId).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(invoiceIdList)) {
            List<DicInvoice> invoiceList = invoiceDataWrap.listByIds(invoiceIdList);
            for (DicInvoiceOccupy invoiceOccupy : invoiceOccupyList) {
                for (DicInvoice invoice : invoiceList) {
                    if (invoiceOccupy.getInvoiceId().equals(invoice.getId())) {
                        invoice.setOccupyInvoiceAmount(invoiceOccupy.getOccupyAmount().subtract(invoiceOccupy.getOccupyAmount()));
                    }
                }
            }
            if (UtilCollection.isNotEmpty(invoiceList)) {
                invoiceDataWrap.updateBatchById(invoiceList);
            }
        }
        invoiceOccupyDataWrap.remove(new LambdaQueryWrapper<DicInvoiceOccupy>().eq(DicInvoiceOccupy::getOccupyReceiptHeadId, headDTO.getId()));
    }

    /**
     * 查询分项信息
     * 数量合并后
     *
     * @param ctx ctx
     */
    public void selectSubItem(BizContext ctx) {

        List<BizReceiptPaymentSettlementItemDTO> po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptContractSubItemDTO> subItemDTOList = new ArrayList<>();
        for (BizReceiptPaymentSettlementItemDTO itemDTO : po) {
            List<BizReceiptContractSubItem> list = bizReceiptContractSubItemDataWrap.list(new QueryWrapper<BizReceiptContractSubItem>().lambda().eq
                    (BizReceiptContractSubItem::getReceiveId, itemDTO.getPaymentPlanHeadDTO().getReceiveId()));
            Map<String, List<BizReceiptContractSubItem>> map = list.stream().collect(Collectors.groupingBy(BizReceiptContractSubItem::getSubItemName));

            map.forEach((k, v) -> {
                BizReceiptContractSubItemDTO subItemDTO = new BizReceiptContractSubItemDTO();
                UtilBean.copy(v.get(0), subItemDTO);
                // 数量取合并后的
                subItemDTO.setPaymentPlanQty(v.stream().map(BizReceiptContractSubItem::getPaymentPlanQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTO.setPaymentPlanValue(v.stream().map(BizReceiptContractSubItem::getPaymentPlanValue).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTOList.add(subItemDTO);
            });

        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, subItemDTOList);

    }


    /**
     * 查询离岸送货
     *
     * @param ctx
     */
    public void selectDelivery(BizContext ctx) {

        // 上下文入参
        BizReceiptDeliveryNoticeSearchPO po = ctx.getPoContextData();
        if (Objects.isNull(po)) {
            po = new BizReceiptDeliveryNoticeSearchPO();
        }
        List<DeliveryVO> resultList = paymentSettlementHeadDataWrap.selectDelivery(po);
        // dataFillService.fillAttr(resultList);
        // List<BizReceiptDeliveryNoticeHeadDTO> deliveryNoticeHeadDTOS = UtilCollection.toList(resultList, BizReceiptDeliveryNoticeHeadDTO.class);
        // List<BizReceiptPaymentSettlementItemDTO> itemList = new ArrayList<>();
        // deliveryNoticeHeadDTOS.forEach(item -> {
        //     BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO = new BizReceiptDeliveryNoticeHeadDTO();
        //     BizReceiptPaymentSettlementItemDTO bizReceiptPaymentSettlementItemDTO = new BizReceiptPaymentSettlementItemDTO();
        //     bizReceiptPaymentSettlementItemDTO.setDeliveryHeadId(item.getId());
        //     bizReceiptPaymentSettlementItemDTO.setDeliveryNoticeHeadDTO(item);
        //     itemList.add(bizReceiptPaymentSettlementItemDTO);
        //
        //
        // });
        // 设置返回信息到上下文
        ctx.setVoContextData(resultList);
    }


    /**
     * 查询合同列表
     *
     * @param ctx
     */
    public void getContract(BizContext ctx) {

        BizReceiptContractSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // if (UtilNumber.isEmpty(po.getSendType())) {
        //     throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        // }

        // 查询执行中,已终止的合同
        po.setReceiptStatusList(Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_EXECUTING.getValue(), EnumReceiptStatus.RECEIPT_STATUS_TERMINATED.getValue()));
        // 框架合同
        po.setReceiptType(EnumReceiptType.FRAMEWORK_CONTRACT.getValue());
        QueryWrapper<BizReceiptContractHead> headQueryWrapper = new QueryWrapper<>();
        headQueryWrapper.lambda()
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptContractHead::getReceiptCode, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptContractHead::getContractName, po.getContractName())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptContractHead::getReceiptCode, po.getReceiptCode())
                .in(UtilCollection.isNotEmpty(po.getContractSubTypeList()), BizReceiptContractHead::getContractSubType, po.getContractSubTypeList())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptContractHead::getReceiptStatus, po.getReceiptStatusList())
                .eq(UtilNumber.isNotEmpty(po.getSupplierId()), BizReceiptContractHead::getSupplierId, po.getSupplierId())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), BizReceiptContractHead::getReceiptType, po.getReceiptType())
        ;

        List<BizReceiptContractHead> headList = bizReceiptContractHeadDataWrap.list(headQueryWrapper);
        List<BizReceiptContractHeadDTO> bizReceiptContractHeadDTOS = UtilCollection.toList(headList, BizReceiptContractHeadDTO.class);
        dataFillService.fillAttr(bizReceiptContractHeadDTOS);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, bizReceiptContractHeadDTOS);
    }

    /**
     * 查询物资入库
     *
     * @param ctx
     */
    public void selectInput(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getPoContextData();

        List<BizReceiptPaymentSettlementInputItemDTO> resultList = paymentSettlementHeadDataWrap.selectInput(po);
        dataFillService.fillAttr(resultList);

        // 设置返回信息到上下文
        ctx.setVoContextData(resultList);
    }

    public void genPaymentSettlementByInput(BizContext ctx) {
        // 上下文入参
        BizReceiptPaymentSettlementHeadDTO headDTO = ctx.getPoContextData();
        CurrentUser currentUser = ctx.getCurrentUser();

        if (UtilCollection.isNotEmpty(headDTO.getInputItemList())) {
            headDTO.setReceiptType(EnumReceiptType.PAYMENT_SETTLEMENT.getValue());
            headDTO.setSettlementType(3);
            headDTO.setContractId(headDTO.getInputItemList().get(0).getContractId());
            BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(headDTO.getContractId());
            headDTO.setContractCode(contractHead.getReceiptCode());
            headDTO.setFirstParty(contractHead.getFirstParty());
            headDTO.setSupplierName(dicSupplierDataWrap.getById(contractHead.getSupplierId()).getSupplierName());
            headDTO.setContractCurrency(contractHead.getCurrency());
            headDTO.setContractCurrencyI18n(i18nTextCommonService.getNameMessage(getLangCodeFromRequest(), "currency", contractHead.getCurrency().toString()));
            headDTO.setPaymentMonth(this.getMonth());
            headDTO.setCreateUserName(currentUser.getUserName());
            headDTO.setCreateTime(new Date());
            List<BizReceiptPaymentSettlementHead> list = paymentSettlementHeadDataWrap.list(new QueryWrapper<BizReceiptPaymentSettlementHead>().lambda()
                    .eq(BizReceiptPaymentSettlementHead::getContractId, headDTO.getContractId())
                    .eq(BizReceiptPaymentSettlementHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
            // 历次提报金额
            headDTO.setEveryPaidAmount(list.stream().map(BizReceiptPaymentSettlementHead::getPaymentAmount).collect(Collectors.toList()));
            List<BizReceiptPaymentSettlementItemDTO> itemList = new ArrayList<>();
            BigDecimal sum = BigDecimal.ZERO;
            BigDecimal sum1 = BigDecimal.ZERO;

            Map<Long, List<BizReceiptPaymentSettlementInputItemDTO>> inputHeadIdItemListMap = headDTO.getInputItemList().stream().collect(Collectors.groupingBy(BizReceiptPaymentSettlementInputItemDTO::getInputHeadId));

            for (Map.Entry<Long, List<BizReceiptPaymentSettlementInputItemDTO>> entry : inputHeadIdItemListMap.entrySet()) {
                BizReceiptPaymentSettlementItemDTO itemDTO = new BizReceiptPaymentSettlementItemDTO();

                itemDTO.setInputHeadId(entry.getKey());
                List<BizReceiptInputItemDTO> inputItemDTOList = entry.getValue().stream().map(BizReceiptPaymentSettlementInputItemDTO::getInputItemDTO).collect(Collectors.toList());
                BigDecimal reduced = inputItemDTOList.stream().map(c -> c.getNoTaxPrice().multiply(c.getQty())).reduce(
                        BigDecimal.ZERO,
                        BigDecimal::add
                );
                sum = sum.add(reduced);
                BigDecimal reduced1 = inputItemDTOList.stream().map(c -> c.getTaxPrice().multiply(c.getQty())).reduce(
                        BigDecimal.ZERO,
                        BigDecimal::add
                );
                sum1 = sum1.add(reduced1);

                itemList.add(itemDTO);
            }
            dataFillService.fillAttr(itemList);
            headDTO.setItemList(itemList);

            List<BizReceiptPaymentInvoiceDTO> invoiceDTOList = new ArrayList<>();
            LambdaQueryWrapper<DicInvoice> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DicInvoice::getInvoiceNo, headDTO.getInputItemList().stream().map(BizReceiptPaymentSettlementInputItemDTO::getInputItemDTO).map(BizReceiptInputItemDTO::getInvoiceNo).collect(Collectors.toList()));
            List<DicInvoice> invoiceList = dicInvoiceDataWrap.list(queryWrapper);
            for (DicInvoice invoice : invoiceList) {
                BizReceiptPaymentInvoiceDTO invoiceDTO = UtilBean.newInstance(invoice, BizReceiptPaymentInvoiceDTO.class);
                invoiceDTO.setInvoiceId(invoice.getId());
                invoiceDTOList.add(invoiceDTO);
            }
            headDTO.setInvoiceList(invoiceDTOList);
            dataFillService.fillAttr(invoiceDTOList);

            List<BizReceiptPaymentProgressDTO> progressList = new ArrayList<>();
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Net Value不含税价格").setCurrency(contractHead.getCurrency()).setAmount(sum));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("GST 税码").setCurrency(contractHead.getCurrency()).setAmount(sum1.subtract(sum)));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Total with GST & Freight (PKR)含税总计").setCurrency(contractHead.getCurrency()).setAmount(sum1));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Invoice Value 本次发票金额").setCurrency(contractHead.getCurrency()));
            progressList.add(new BizReceiptPaymentProgressDTO().setProgressDesc("Applied Payment申请支付金额").setCurrency(contractHead.getCurrency()));

            headDTO.setProgressList(progressList);
            headDTO.setApproveUserList(this.getSysUserDTOS());
        }
        // 设置返回信息到上下文
        ctx.setVoContextData(headDTO);
    }

    /**
     * 审批撤销
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptPaymentSettlementHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 删除待办:必须在审批撤销前
        String id = workflowService.deleteTodo(headDTO.getId());
        // 审批撤销
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(id);
        workflowService.revoke(revokeDTO);

        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE, "", user.getId());

    }

    /**
     * 单据撤销
     *
     * @param ctx 上下文
     */
    public void revokeReceipt(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptPaymentSettlementHead head = paymentSettlementHeadDataWrap.getById(id);
        // 判断单据是否存在
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        // 判断单据状态是否为已完成
        if (!EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        // 查询是否存在下游单据 - 支付审批
        List<BizReceiptPaymentRegisterItem> paymentRegisterItemList = bizReceiptPaymentRegisterItemDataWrap.list(new QueryWrapper<BizReceiptPaymentRegisterItem>().lambda()
                .eq(BizReceiptPaymentRegisterItem::getSettlementHeadId, head.getId()));
        if (UtilCollection.isNotEmpty(paymentRegisterItemList)) {
            List<Long> headIdList = paymentRegisterItemList.stream().map(BizReceiptPaymentRegisterItem::getHeadId).collect(Collectors.toList());
            List<BizReceiptPaymentRegisterHead> paymentRegisterHeadList = bizReceiptPaymentRegisterHeadDataWrap.list(new QueryWrapper<BizReceiptPaymentRegisterHead>().lambda()
                    .in(BizReceiptPaymentRegisterHead::getId, headIdList));
            if (UtilCollection.isNotEmpty(paymentRegisterHeadList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_HAS_ASSOCIATED_RECEIPT_NOT_REVOKE, paymentRegisterHeadList.get(0).getReceiptCode());
            }
        }
        // 查询是否存在下游单据 - 发票预制
        BizReceiptInvoicePrecastHead paymentInvoiceHead = bizReceiptInvoicePrecastHeadDataWrap.getOne(new QueryWrapper<BizReceiptInvoicePrecastHead>().lambda()
                .eq(BizReceiptInvoicePrecastHead::getSettlementId, head.getId()), false);
        if (UtilObject.isNotNull(paymentInvoiceHead)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_HAS_ASSOCIATED_RECEIPT_NOT_REVOKE, paymentInvoiceHead.getReceiptCode());
        }
        // 属性填充
        BizReceiptPaymentSettlementHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptPaymentSettlementHeadDTO.class);
        dataFillService.fillAttr(headDTO);

        // 撤回发票剩余可用数量
        this.revokeInvoice(headDTO);

        // 更新单据状态 草稿
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE, "", user.getId());
    }

}
