package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedInconformityNumberMaintainService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.enums.CheckResultMapVO;
import com.inossem.wms.common.model.common.enums.inconformity.DisposalResultMapVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成套设备数量差异处置 前端控制器
 * </p>
 */
@RestController
public class UnitizedInconformityNumberMaintainController {

    @Autowired
    protected UnitizedInconformityNumberMaintainService unitizedInconformityMaintainService;

    /**
     * 查询处置结果下拉
     *
     * @return 处置结果下拉框
     */
    @ApiOperation(value = "查询处置结果下拉", tags = {"成套设备管理-成套设备数量差异处置"})
    @GetMapping(path = "/unitized/inconformity/inconformity-number-maintain/disposal-result-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DisposalResultMapVO>> getDisposalResultDown(BizContext ctx) {
        unitizedInconformityMaintainService.getDisposalResultDown(ctx);
        MultiResultVO<DisposalResultMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询成套设备数量差异处置列表-分页
     *
     * @param po 分页查询入参
     * @return 单据列表
     */
    @ApiOperation(value = "查询成套设备数量差异处置列表-分页", tags = {"成套设备管理-成套设备数量差异处置"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-maintain/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInconformityPageVO>> getPage(@RequestBody BizReceiptInconformitySearchPO po, BizContext ctx) {
        unitizedInconformityMaintainService.getPage(ctx);
        PageObjectVO<BizReceiptInconformityPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询成套设备数量差异处置详情
     *
     * @param id 成套设备数量差异处置抬头表主键
     * @return 成套设备数量差异处置详情
     */
    @ApiOperation(value = "查询成套设备数量差异处置详情", tags = {"成套设备管理-成套设备数量差异处置"})
    @GetMapping(value = "/unitized/inconformity/inconformity-number-maintain/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedInconformityMaintainService.getInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 成套设备数量差异处置-保存
     *
     * @param po 保存成套设备数量差异处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "成套设备数量差异处置-保存", tags = {"成套设备管理-成套设备数量差异处置"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-maintain/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityMaintainService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 成套设备数量差异处置-提交
     *
     * @param po 提交成套设备数量差异处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "成套设备数量差异处置-提交", tags = {"成套设备管理-成套设备数量差异处置"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-maintain/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityMaintainService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 成套设备数量差异处置-冲销
     *
     * @param po 提交成套设备数量差异处置表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "成套设备数量差异处置-冲销", tags = {"成套设备管理-成套设备数量差异处置"})
    @PostMapping(value = "/unitized/inconformity/inconformity-number-maintain/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult post(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityMaintainService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 查询供应商处理意见
     *
     * @return 查询供应商处理意见
     */
    @ApiOperation(value = "查询供应商处理意见", tags = {"成套设备管理-成套设备数量差异处置"})
    @GetMapping(path = "/unitized/inconformity/inconformity-number-maintain/supplier-solve-reason-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<CheckResultMapVO>> getSupplierSolveReasonDown(BizContext ctx) {
        unitizedInconformityMaintainService.getSupplierSolveReasonDown(ctx);
        MultiResultVO<CheckResultMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}
