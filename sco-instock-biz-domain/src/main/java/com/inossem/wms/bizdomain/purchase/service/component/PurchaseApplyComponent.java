package com.inossem.wms.bizdomain.purchase.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.budget.service.datawrap.AnnualBudgetDataWrap;
import com.inossem.wms.bizdomain.budget.service.datawrap.BudgetClassifyDataWrap;
import com.inossem.wms.bizdomain.budget.service.datawrap.BudgetSubjectDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractItemDataWrap;
import com.inossem.wms.bizdomain.demandplan.service.component.DemandPlanComponent;
import com.inossem.wms.bizdomain.purchase.service.datawrap.BizReceiptPurchaseApplyHeadDataWrap;
import com.inossem.wms.bizdomain.purchase.service.datawrap.BizReceiptPurchaseApplyItemDataWrap;
import com.inossem.wms.bizdomain.purchase.service.datawrap.BizReceiptPurchaseApplySupplierItemDataWrap;
import com.inossem.wms.bizdomain.srm.service.SrmService;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.apply.EnumBudget;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.enums.demandplan.EnumDemandPlanType;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.purchase.EnumPurchaseType;
import com.inossem.wms.common.enums.purchase.EnumSendType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemGroupDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemQtyDTO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplySupplierItemDTO;
import com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplyHead;
import com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplyItem;
import com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplySupplierItem;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplySearchPO;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplyUpdatePO;
import com.inossem.wms.common.model.bizdomain.purchase.vo.BizReceiptPurchaseApplyListVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.EnumBidMethodMapVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.budget.dto.DicAnnualBudgetDTO;
import com.inossem.wms.common.model.masterdata.budget.entity.DicAnnualBudget;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.file.service.biz.FileService;
import com.inossem.wms.system.file.service.datawrap.BizCommonFileDataWrap;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购申请Component
 */
@Slf4j
@Component
public class PurchaseApplyComponent {

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private BizReceiptPurchaseApplyHeadDataWrap headDataWrap;

    @Autowired
    private BizReceiptPurchaseApplyItemDataWrap itemDataWrap;

    @Autowired
    private BizReceiptPurchaseApplySupplierItemDataWrap supplierItemDataWrap;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private DemandPlanComponent demandPlanComponent;

    @Autowired
    private BizReceiptContractItemDataWrap bizReceiptContractItemDataWrap;

    @Autowired
    private BizReceiptPurchaseApplyHeadDataWrap bizReceiptPurchaseApplyHeadDataWrap;

    @Autowired
    private AnnualBudgetDataWrap annualBudgetDataWrap;

    @Autowired
    private BudgetClassifyDataWrap budgetClassifyDataWrap;

    @Autowired
    private BudgetSubjectDataWrap budgetSubjectDataWrap;

    @Autowired
    private FileService fileService;

    @Autowired
    @Lazy
    private SrmService srmService;

    @Autowired
    protected BizCommonFileDataWrap bizCommonFileDataWrap;

    @Autowired
    private DicSupplierDataWrap dicSupplierDataWrap;

    @Value("${wms.srm.attachment-url}")
    private String uploadUrl;

    @Value("${wms.srm.createMain-url}")
    private String createMainUrl;

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private SysUserDataWrap sysUserDataWrap;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;

    @Autowired
    private DictionaryService dictionaryService;


    /**
     * 获取预算分类预算科目列表
     */
    public void getEnumBudgetList(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumBudget.toList()));
    }

    /**
     * 采购申请分页查询
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplySearchPO"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#PageObjectVO<BizReceiptPurchaseApplyListVO>"})
    public void getPurchaseApplyPageVo(BizContext ctx) {
        log.info("开始采购申请分页查询");
        BizReceiptPurchaseApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        
        // 构建查询条件
        WmsQueryWrapper<BizReceiptPurchaseApplySearchPO> wrapper = buildQueryWrapper(po);
        log.debug("采购申请查询条件构建完成:{}", wrapper);

        // 分页查询
        IPage<BizReceiptPurchaseApplyListVO> page = po.getPageObj(BizReceiptPurchaseApplyListVO.class);
        headDataWrap.getPurchaseApplyPageVo(page, wrapper);
        
        // 设置分页结果
        PageObjectVO<BizReceiptPurchaseApplyListVO> pageVO = new PageObjectVO<>(page.getRecords(), page.getTotal());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, pageVO);
        log.info("采购申请分页查询完成, 共{}条记录", page.getTotal());
    }

    /**
     * 构建查询条件
     */
    private WmsQueryWrapper<BizReceiptPurchaseApplySearchPO> buildQueryWrapper(BizReceiptPurchaseApplySearchPO po) {
        if (po == null) {
            po = new BizReceiptPurchaseApplySearchPO();
        }

        return new WmsQueryWrapper<BizReceiptPurchaseApplySearchPO>()
            .eq("h.is_delete", 0)
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), "h.receipt_code", po.getReceiptCode())
            .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), "h.receipt_status", po.getReceiptStatusList())
            .like(UtilString.isNotNullOrEmpty(po.getPurchaseDescription()), "h.purchase_description", po.getPurchaseDescription())
            .eq(UtilObject.isNotNull(po.getPurchaseType()), "h.purchase_type", po.getPurchaseType())
            .eq(UtilObject.isNotNull(po.getSendType()), "h.send_type", po.getSendType())
                .eq(UtilObject.isNotNull(po.getReceiptType()), "h.receipt_type", po.getReceiptType())
            .eq(UtilString.isNotNullOrEmpty(po.getCreateUserCode()), "cu.user_code", po.getCreateUserCode())
            .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), "cu.user_name", po.getCreateUserName())
            .ge(UtilObject.isNotNull(po.getCreateTimeStart()), "h.create_time", po.getCreateTimeStart())
            .le(UtilObject.isNotNull(po.getCreateTimeEnd()), "h.create_time", po.getCreateTimeEnd())
            .orderByDesc("h.create_time");
    }

    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.OIL_PO_PURCHASE.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public void setExtendWf1(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.PURCHASE_APPLY.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 采购申请初始化
     */
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#BizResultVO<BizReceiptPurchaseApplyHeadDTO>"})
    public void init(BizContext ctx) {
        log.info("开始初始化新的采购申请单");
        CurrentUser user = ctx.getCurrentUser();

        // 创建采购申请单DTO对象
        BizReceiptPurchaseApplyHeadDTO headDTO = new BizReceiptPurchaseApplyHeadDTO();
        log.debug("创建采购申请DTO对象完成");

        // 设置基本信息
        // 设置单据类型为采购申请
        headDTO.setReceiptType(EnumReceiptType.PURCHASE_APPLY.getValue());
        // 设置状态为草稿
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 设置创建时间
        headDTO.setCreateTime(new Date());
        // 设置创建人ID
        headDTO.setCreateUserId(user.getId());
        // 设置创建人名称
        headDTO.setCreateUserName(user.getUserName());
        log.debug("基本信息设置完成");

        // 设置枚举列表
        // 设置采购类型列表
        headDTO.setPurchaseTypeList(EnumPurchaseType.toList());
        // 设置采购类别列表
        headDTO.setSendTypeList(EnumSendType.toList());
        // 设置招标方式列表
        List<EnumBidMethodMapVO> bidMethodList = EnumBidMethod.toList();
        // B11026 【采购申请】删除各采购申请类型下“采购方式”中的竞价项目与直接采购类型
        bidMethodList.removeIf(bidMethod -> EnumBidMethod.BIDDING_PROJECT.getCode().equals(bidMethod.getBidMethod()) || EnumBidMethod.DIRECT_PURCHASE.getCode().equals(bidMethod.getBidMethod()));
        headDTO.setBidMethodList(bidMethodList);
        headDTO.setSupplierList(dicSupplierDataWrap.list().stream()
                .filter(supplier -> EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue()
                        .equals(supplier.getReceiptStatus()))
                .collect(Collectors.toList()));
        log.debug("供应商列表设置完成");
        log.debug("枚举列表设置完成");

        // 设置按钮权限
        ButtonVO buttonVO = new ButtonVO()
                .setButtonSave(true)
                .setButtonSubmit(true)
                .setButtonDelete(false);

        // 创建扩展信息
        ExtendVO extendVO = new ExtendVO();
        extendVO.setAttachmentRequired(true);
        extendVO.setOperationLogRequired(true);
        extendVO.setRelationRequired(true);

        // 组装返回对象
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> resultVO = new BizResultVO<>(headDTO, extendVO, buttonVO);

        // 设置上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
        log.info("采购申请单初始化完成");
    }

    /**
     * 获取采购申请详情
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#BizResultVO<BizReceiptPurchaseApplyHeadDTO>"})
    public void getPurchaseApplyDetail(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("开始获取采购申请详情, ID:{}", id);

        BizReceiptPurchaseApplyHeadDTO headDTO = UtilBean.newInstance(
            headDataWrap.getById(id), 
            BizReceiptPurchaseApplyHeadDTO.class
        );
        if (UtilObject.isNull(headDTO)) {
            log.warn("采购申请数据不存在, ID:{}", id);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST);
        }

        // 填充关联属性
        dataFillService.fillAttr(headDTO);
        headDTO.setSupplierList(dicSupplierDataWrap.list().stream()
                .filter(supplier -> EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue()
                        .equals(supplier.getReceiptStatus()))
                .collect(Collectors.toList()));
        // 获取审批记录
        // List<BizApproveRecordDTO> approveList = bizApprovalReceiptInstanceRelDataWrap
        //         .getApproveRecord(headDTO.getReceiptCode());
        // headDTO.setApproveList(approveList);
        log.debug("采购申请[{}]关联属性填充完成", headDTO.getReceiptCode());
        // 设置按钮权限
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(headDTO.getReceiptStatus())) {
            // 草稿状态可以保存、提交、删除
            buttonVO.setButtonSave(true)
                   .setButtonSubmit(true)
                   .setButtonDelete(true);
            // 设置招标方式列表
            headDTO.setBidMethodList(EnumBidMethod.toList());
            headDTO.setSupplierList(dicSupplierDataWrap.list().stream()
                    .filter(supplier -> EnumReceiptStatus.RECEIPT_STATUS_ENABLED.getValue()
                            .equals(supplier.getReceiptStatus()))
                    .collect(Collectors.toList()));
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(headDTO.getReceiptStatus())) {
            // 已驳回可以提交
            buttonVO.setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(headDTO.getReceiptStatus())) {
            // 审批中可以撤销
            buttonVO.setButtonRevoke(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(headDTO.getReceiptStatus())) {
            // 未同步可以过账
            buttonVO.setButtonPost(true);
        }

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        // 创建扩展信息
        ExtendVO extendVO = new ExtendVO();
        extendVO.setAttachmentRequired(true)
                .setOperationLogRequired(true)
                .setRelationRequired(true);

        // 组装返回对象
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> resultVO = new BizResultVO<>(headDTO, extendVO, buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
        
        log.info("采购申请[{}]详情获取完成", headDTO.getReceiptCode());
    }

    /**
     * 保存前校验
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"})
    public void checkSave(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        
        // 校验行项目
        if (UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

        // 校验单据状态
        if (UtilObject.isNotNull(headDTO.getId())) {
            BizReceiptPurchaseApplyHead head = headDataWrap.getById(headDTO.getId());
            if (UtilObject.isNull(head)) {
                return;
            }
            // if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())) {
            //     throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
            // }
        }
    }

    /**
     * 保存采购申请
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"})
    @Out(parameter = {
        Const.BIZ_CONTEXT_KEY_CODE + "#String", 
        Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"
    })
    public void save(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        Date now = new Date();

        String receiptCode = headDTO.getReceiptCode();
        log.info("开始保存采购申请单, 单据编号:{}", receiptCode);
        // 获取原始采购申请数据(用于计算数量变化)
        Map<Long, BigDecimal> originalQtyMap = new HashMap<>();
        List<BizReceiptDemandPlanItemQtyDTO> updateItems = new ArrayList<>();
        
        // 新增时生成单号
        if (UtilObject.isNull(headDTO.getId())) {
            receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_PURCHASE_APPLY.getValue());
            headDTO.setReceiptCode(receiptCode);
            headDTO.setReceiptType(EnumReceiptType.PURCHASE_APPLY.getValue());
            headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            headDTO.setCreateTime(now);
            headDTO.setCreateUserId(user.getId());
            log.debug("新建采购申请单, 生成单号:{}", receiptCode);
            
            // 设置上下文单据日志 - 创建
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, 
                EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        } else {
            log.debug("修改采购申请单, 单据编号:{}", receiptCode);
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
                
            // 获取原有行项目数据
            List<BizReceiptPurchaseApplyItem> originalItems = itemDataWrap.list(
                new QueryWrapper<BizReceiptPurchaseApplyItem>()
                    .lambda()
                    .eq(BizReceiptPurchaseApplyItem::getHeadId, headDTO.getId())
            );

            // 构建原始数量Map
            originalQtyMap = originalItems.stream()
                .collect(Collectors.toMap(
                    BizReceiptPurchaseApplyItem::getPreReceiptItemId,
                    BizReceiptPurchaseApplyItem::getDemandQty,
                    (v1, v2) -> v1
                ));

            // 构建新的行项目Map
            Map<Long, BizReceiptPurchaseApplyItemDTO> newItemMap = headDTO.getItemList().stream()
                .collect(Collectors.toMap(
                    BizReceiptPurchaseApplyItemDTO::getPreReceiptItemId,
                    item -> item,
                    (v1, v2) -> v1
                ));

            // 处理删除的行项目 - 需要将采购数量减为0
            for (BizReceiptPurchaseApplyItem originalItem : originalItems) {
                Long preReceiptItemId = originalItem.getPreReceiptItemId();
                if (!newItemMap.containsKey(preReceiptItemId)) {
                    // 原有行项目在新数据中不存在,说明被删除了
                    BizReceiptDemandPlanItemQtyDTO qtyDTO = new BizReceiptDemandPlanItemQtyDTO();
                    qtyDTO.setId(preReceiptItemId);
                    qtyDTO.setPurchaseQty(originalItem.getDemandQty().negate()); // 设置负数,用于减少需求计划的已采购数量
                    updateItems.add(qtyDTO);
                    log.debug("采购申请单[{}]行项目[{}]被删除,需要更新需求计划采购数量", receiptCode, originalItem.getRid());
                }
            }

            // 删除原有行项目
            QueryWrapper<BizReceiptPurchaseApplyItem> itemWrapper = new QueryWrapper<>();
            itemWrapper.lambda().eq(BizReceiptPurchaseApplyItem::getHeadId, headDTO.getId());
            itemDataWrap.physicalDelete(itemWrapper);
            log.debug("采购申请单[{}]原有行项目删除完成", receiptCode);

            // 删除原有供应商行项目
            QueryWrapper<BizReceiptPurchaseApplySupplierItem> supplierItemWrapper = new QueryWrapper<>();
            supplierItemWrapper.lambda().eq(BizReceiptPurchaseApplySupplierItem::getHeadId, headDTO.getId());
            supplierItemDataWrap.physicalDelete(supplierItemWrapper);
            log.debug("直接采购单[{}]原有供应商行项目删除完成", receiptCode);
        }

        // 设置修改信息
        headDTO.setModifyTime(now);
        headDTO.setModifyUserId(user.getId());

        // 保存头表
        headDataWrap.saveOrUpdateDto(headDTO);
        log.debug("采购申请单[{}]头表保存完成", receiptCode);
        
        // 保存行项目
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            int rid = 10;
            for (BizReceiptPurchaseApplyItemDTO itemDTO : headDTO.getItemList()) {
                itemDTO.setHeadId(headDTO.getId());
                itemDTO.setRid(String.format("%05d", rid));
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                itemDTO.setCreateTime(now);
                itemDTO.setCreateUserId(user.getId());
                itemDTO.setModifyTime(now);
                itemDTO.setModifyUserId(user.getId());
                rid += 10;

                // 更新需求计划行项目数量和状态
                BizReceiptDemandPlanItemQtyDTO qtyDTO = new BizReceiptDemandPlanItemQtyDTO();
                qtyDTO.setId(itemDTO.getPreReceiptItemId());
                
                // 计算数量变化值
                BigDecimal originalQty = originalQtyMap.getOrDefault(itemDTO.getPreReceiptItemId(), BigDecimal.ZERO);
                BigDecimal qtyChange = itemDTO.getDemandQty().subtract(originalQty);
                qtyDTO.setPurchaseQty(qtyChange); // 设置数量变化值
                updateItems.add(qtyDTO);
            }
            itemDataWrap.saveBatchDto(headDTO.getItemList());
            log.debug("采购申请单[{}]保存{}个行项目完成", receiptCode, headDTO.getItemList().size());
        }

        // 保存供应商行项目
        if (UtilCollection.isNotEmpty(headDTO.getSupplierItemList())) {
            int rid = 1;
            for (BizReceiptPurchaseApplySupplierItemDTO supplierItemDTO : headDTO.getSupplierItemList()) {
                supplierItemDTO.setId(null);
                supplierItemDTO.setHeadId(headDTO.getId());
                supplierItemDTO.setRid(String.valueOf(rid++));
                supplierItemDTO.setCreateTime(now);
                supplierItemDTO.setCreateUserId(user.getId());
                supplierItemDTO.setModifyTime(now);
                supplierItemDTO.setModifyUserId(user.getId());
            }
            supplierItemDataWrap.saveBatchDto(headDTO.getSupplierItemList());
            log.debug("直接采购单[{}]保存{}个供应商行项目完成", receiptCode, headDTO.getSupplierItemList().size());
        }

        // 更新需求计划行项目数量和状态
        if (UtilCollection.isNotEmpty(updateItems)) {
            List<BizReceiptDemandPlanItemQtyDTO> newUpdateItems = new ArrayList<>();
            // 聚合需求计划行变化数量
            Map<Long, List<BizReceiptDemandPlanItemQtyDTO>> map = updateItems.stream().collect(Collectors.groupingBy(BizReceiptDemandPlanItemQtyDTO::getId));
            map.forEach((k, v) -> {
                BizReceiptDemandPlanItemQtyDTO bizReceiptDemandPlanItemQtyDTO = new BizReceiptDemandPlanItemQtyDTO();
                bizReceiptDemandPlanItemQtyDTO.setId(k);
                bizReceiptDemandPlanItemQtyDTO.setPurchaseQty(v.stream().map(BizReceiptDemandPlanItemQtyDTO::getPurchaseQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                newUpdateItems.add(bizReceiptDemandPlanItemQtyDTO);
            });

            ctx.setContextData(Const.BIZ_CONTEXT_KEY_LIST, newUpdateItems);
            demandPlanComponent.batchUpdateItemQtyAndStatus(ctx);
            log.debug("需求计划行项目数量和状态更新完成, 共{}条记录", updateItems.size());
        }

        // 设置返回值
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        log.info("采购申请单[{}]保存完成", receiptCode);
    }

    /**
     * 保存操作日志
     */
    @In(parameter = {
        Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO",
        Const.BIZ_CONTEXT_OPERATION_LOG_TYPE + "#EnumReceiptOperationType"
    })
    public void saveBizReceiptOperationLog(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        
        log.debug("开始保存采购申请单[{}]操作日志, 操作类型:{}", headDTO.getReceiptCode(), operationType);
        
        receiptOperationLogService.saveBizReceiptOperationLogList(
            headDTO.getId(),
            headDTO.getReceiptType(),
            operationType,
            "",
            ctx.getCurrentUser().getId()
        );
        
        log.debug("采购申请单[{}]操作日志保存完成", headDTO.getReceiptCode());
    }

    /**
     * 保存附件
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"})
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String receiptCode = headDTO.getReceiptCode();
        
        log.debug("开始保存采购申请单[{}]附件", receiptCode);
        
        receiptAttachmentService.saveBizReceiptAttachment(
            headDTO.getFileList(),
            headDTO.getId(),
            EnumReceiptType.PURCHASE_APPLY.getValue(),
            ctx.getCurrentUser().getId()
        );
        
        log.debug("采购申请单[{}]附件保存完成", receiptCode);
    }

    /**
     * 保存单据流
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"})
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String receiptCode = headDTO.getReceiptCode();
        
        log.debug("开始保存采购申请单[{}]单据流", receiptCode);
        
        List<BizCommonReceiptRelation> relationList = new ArrayList<>();
        for (BizReceiptPurchaseApplyItemDTO itemDTO : headDTO.getItemList()) {
            BizCommonReceiptRelation relation = new BizCommonReceiptRelation();
            relation.setReceiptType(headDTO.getReceiptType());
            relation.setReceiptHeadId(itemDTO.getHeadId());
            relation.setReceiptItemId(itemDTO.getId());
            relation.setPreReceiptType(itemDTO.getPreReceiptType());
            relation.setPreReceiptHeadId(itemDTO.getPreReceiptId());
            relation.setPreReceiptItemId(itemDTO.getPreReceiptItemId());
            relationList.add(relation);
        }
        
        receiptRelationService.multiSaveReceiptTree(relationList);
        log.debug("采购申请单[{}]单据流保存完成, 共{}条关系记录", receiptCode, relationList.size());
    }

    /**
     * 提交前校验
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"})
    public void checkSubmit(BizContext ctx) {
        // 校验必填字段
        checkSave(ctx);
        
    }

    /**
     * 提交采购申请
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"})
    @Out(parameter = {
        Const.BIZ_CONTEXT_KEY_CODE + "#String",
        Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyHeadDTO"
    })
    public void submit(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("开始提交采购申请单[{}]", headDTO.getReceiptCode());
    
        // 复用save方法的保存逻辑
        save(ctx);

        // 修改状态为审批中
        updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());

        // 设置返回值和日志状态
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        
        log.info("采购申请单[{}]提交完成", headDTO.getReceiptCode());

    }

    private void updateBudget(BizReceiptPurchaseApplyHeadDTO headDTO) {
        Long annualBudgetId = headDTO.getAnnualBudgetId();
        DicAnnualBudgetDTO dicAnnualBudgetDTO = headDTO.getDicAnnualBudgetDTO();
        // 按预算年份+分类分组统计合同项金额
        List<BizReceiptContractItemGroupDTO> amountGroupByYearAndBudgetClassify = bizReceiptContractItemDataWrap.getAmountGroupByYearAndBudgetClassify();

        // 创建复合分组键（预算年份_预算分类_预算科目）的分组映射
        Map<String, List<BizReceiptContractItemGroupDTO>> contractItemGroupDTOMap = amountGroupByYearAndBudgetClassify.stream()
                .collect(Collectors.groupingBy(
                        e -> generateGroupKey(e.getBudgetYear(), e.getBudgetClass(), e.getBudgetAccount())
                ));
        // 按分组键获取合同项金额分组
        List<BizReceiptContractItemGroupDTO> contractItemGroupDTOList = contractItemGroupDTOMap.get(generateGroupKey(dicAnnualBudgetDTO.getYear(),
                dicAnnualBudgetDTO.getBudgetClassifyName(),
                dicAnnualBudgetDTO.getBudgetSubjectName()));

        BigDecimal totalContractAmount = BigDecimal.ZERO;
        // 获取合同项总金额
        if (UtilCollection.isNotEmpty(contractItemGroupDTOList)) {
            totalContractAmount = contractItemGroupDTOList.stream().map(BizReceiptContractItemGroupDTO::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 更新年度预算已提报预算金额 = 已有的已提报预算金额 + 本次提报金额
        // 更新年度预算未生成合同金额 = 已提报预算金额 - 已生成的合同金额
        // 更新年度预算可提报最大金额 = 年度预算金额 - 已提报预算金额 - 本次提报金额
        BigDecimal budgetAmount = this.getExchangeRateUSDBudgetAmount(dicAnnualBudgetDTO.getCurrency(), headDTO.getApplyCurrency(), headDTO.getBudgetAmount());
        annualBudgetDataWrap.update(new UpdateWrapper<DicAnnualBudget>().lambda().
                set(DicAnnualBudget::getExistingPurchaseAmount, dicAnnualBudgetDTO.getExistingPurchaseAmount().add(budgetAmount)).
                set(DicAnnualBudget::getNotGenerateContractAmount, dicAnnualBudgetDTO.getExistingPurchaseAmount().add(budgetAmount).subtract(totalContractAmount)).
                set(DicAnnualBudget::getCanDeclareAmount, dicAnnualBudgetDTO.getBudgetAmount().subtract(dicAnnualBudgetDTO.getExistingPurchaseAmount()).subtract(budgetAmount)).
                eq(DicAnnualBudget::getId, annualBudgetId));
    }

    /**
     * 根据币种计算汇率后的预算金额
     *
     * @param currency 年度预算币种
     * @param applyCurrency 申请币种
     * @param budgetAmount 预算金额
     * @return 转换后的预算金额
     */
    public BigDecimal getExchangeRateUSDBudgetAmount(String currency, String applyCurrency, BigDecimal budgetAmount) {
        // 如果预算金额为空或为0，直接返回
        if (budgetAmount == null || budgetAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        // 如果申请币种和目标币种相同，直接返回原金额
        if (UtilString.isNullOrEmpty(applyCurrency) || applyCurrency.equals(currency)) {
            return budgetAmount.setScale(2, RoundingMode.HALF_UP);
        }

        // 定义汇率
        final BigDecimal USD_TO_CNY_RATE = new BigDecimal("7");      // 1 USD = 7 CNY
        final BigDecimal USD_TO_PKR_RATE = new BigDecimal("280");    // 1 USD = 280 PKR

        BigDecimal convertedAmount;

        // 根据申请币种和目标币种进行转换
        if (EnumContractCurrency.USD.getDesc().equals(applyCurrency)) {
            // 从 USD 转换到其他币种
            if (EnumContractCurrency.RMB.getDesc().equals(currency)) {
                // USD -> CNY
                convertedAmount = budgetAmount.multiply(USD_TO_CNY_RATE);
            } else if (EnumContractCurrency.PKR.getDesc().equals(currency)) {
                // USD -> PKR
                convertedAmount = budgetAmount.multiply(USD_TO_PKR_RATE);
            } else {
                convertedAmount = budgetAmount;
            }
        } else if (EnumContractCurrency.RMB.getDesc().equals(applyCurrency)) {
            // 从 CNY 转换到其他币种
            if (EnumContractCurrency.USD.getDesc().equals(currency)) {
                // CNY -> USD
                convertedAmount = budgetAmount.divide(USD_TO_CNY_RATE, 4, RoundingMode.HALF_UP);
            } else if (EnumContractCurrency.PKR.getDesc().equals(currency)) {
                // CNY -> PKR (先转USD，再转PKR)
                BigDecimal usdAmount = budgetAmount.divide(USD_TO_CNY_RATE, 4, RoundingMode.HALF_UP);
                convertedAmount = usdAmount.multiply(USD_TO_PKR_RATE);
            } else {
                convertedAmount = budgetAmount;
            }
        } else if (EnumContractCurrency.PKR.getDesc().equals(applyCurrency)) {
            // 从 PKR 转换到其他币种
            if (EnumContractCurrency.USD.getDesc().equals(currency)) {
                // PKR -> USD
                convertedAmount = budgetAmount.divide(USD_TO_PKR_RATE, 4, RoundingMode.HALF_UP);
            } else if (EnumContractCurrency.RMB.getDesc().equals(currency)) {
                // PKR -> CNY (先转USD，再转CNY)
                BigDecimal usdAmount = budgetAmount.divide(USD_TO_PKR_RATE, 4, RoundingMode.HALF_UP);
                convertedAmount = usdAmount.multiply(USD_TO_CNY_RATE);
            } else {
                convertedAmount = budgetAmount;
            }
        } else {
            convertedAmount = budgetAmount;
        }

        // 保留两位小数，四舍五入
        return convertedAmount.setScale(2, RoundingMode.HALF_UP);
    }

    private static String generateGroupKey(Object year, Object classify, Object subject) {
        return Objects.toString(year, "") + Objects.toString(classify, "") + Objects.toString(subject, "");
    }

    /**
     * 删除前校验
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        
        // 获取采购申请信息
        BizReceiptPurchaseApplyHead head = headDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST);
        }
        
        // 校验单据状态 - 只有草稿状态可以删除
        if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
        }
    }

    /**
     * 删除采购申请
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void delete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("开始删除采购申请单, ID:{}", id);
        
        // 获取采购申请head信息
        BizReceiptPurchaseApplyHead head = headDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST);
        }
        
        // 获取采购申请item信息,用于更新需求计划数量
        List<BizReceiptPurchaseApplyItem> items = itemDataWrap.list(
            new QueryWrapper<BizReceiptPurchaseApplyItem>()
                .lambda()
                .eq(BizReceiptPurchaseApplyItem::getHeadId, id)
        );
        
        // 构建需求计划数量更新DTO
        List<BizReceiptDemandPlanItemQtyDTO> updateItems = new ArrayList<>();
        if (UtilCollection.isNotEmpty(items)) {
            for (BizReceiptPurchaseApplyItem item : items) {
                // 只有存在前序需求计划的才需要更新数量
                if (EnumReceiptType.DEMAND_PLAN.getValue().equals(item.getPreReceiptType()) 
                    && UtilObject.isNotNull(item.getPreReceiptItemId())) {
                    BizReceiptDemandPlanItemQtyDTO qtyDTO = new BizReceiptDemandPlanItemQtyDTO();
                    qtyDTO.setId(item.getPreReceiptItemId());
                    // 设置负数,用于减少需求计划的已采购数量
                    qtyDTO.setPurchaseQty(item.getDemandQty().negate());
                    updateItems.add(qtyDTO);
                }
            }
        }
        
        // 删除采购申请head
        headDataWrap.removeById(id);
        log.debug("采购申请单[{}]头表删除完成", head.getReceiptCode());
        
        // 删除采购申请item
        UpdateWrapper<BizReceiptPurchaseApplyItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(BizReceiptPurchaseApplyItem::getHeadId, id);
        itemDataWrap.remove(wrapper);
        log.debug("采购申请单[{}]行项目删除完成", head.getReceiptCode());
        
        // 更新需求计划数量
        if (UtilCollection.isNotEmpty(updateItems)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_LIST, updateItems);
            demandPlanComponent.batchUpdateItemQtyAndStatus(ctx);
            log.debug("需求计划行项目数量更新完成, 共{}条记录", updateItems.size());
        }
        
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(
            id,
            EnumReceiptType.PURCHASE_APPLY.getValue(), 
            EnumReceiptOperationType.RECEIPT_OPERATION_DELETE,
            "",
            ctx.getCurrentUser().getId()
        );
        log.debug("采购申请单[{}]操作日志保存完成", head.getReceiptCode());
        
        log.info("采购申请单[{}]删除完成", head.getReceiptCode());
    }

    /**
     * 删除单据流
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void deleteReceiptTree(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 删除单据流
        receiptRelationService.deleteReceiptTree(
            EnumReceiptType.PURCHASE_APPLY.getValue(), 
            id
        );
    }

    /**
     * 删除单据附件
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_ID + "#Long"})
    public void deleteReceiptAttachment(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(
            id,
            EnumReceiptType.PURCHASE_APPLY.getValue()
        );
    }

    /**
     * 更新单据状态
     * 批量更新头表和行项目状态
     * 
     * @param headDTO 采购申请头表DTO
     * @param itemList 采购申请行项目列表
     * @param status 目标状态
     */
    @In(parameter = {
        "headDTO#BizReceiptPurchaseApplyHeadDTO",
        "itemList#List<BizReceiptPurchaseApplyItemDTO>",
        "status#Integer"
    })
    private void updateStatus(BizReceiptPurchaseApplyHeadDTO headDTO, List<BizReceiptPurchaseApplyItemDTO> itemList, Integer status) {
        String receiptCode = headDTO.getReceiptCode();
        log.debug("开始更新采购申请单[{}]状态为:{}", receiptCode, status);
        headDTO.setReceiptStatus(status);
        // 更新头表状态
        BizReceiptPurchaseApplyHead head = new BizReceiptPurchaseApplyHead();
        head.setId(headDTO.getId());
        head.setReceiptStatus(status);
        head.setModifyTime(new Date());
        headDataWrap.updateById(head);
        log.debug("采购申请单[{}]头表状态更新完成", receiptCode);
        
        // 批量更新行项目状态
        if (UtilCollection.isNotEmpty(itemList)) {
            List<BizReceiptPurchaseApplyItem> updateItems = itemList.stream()
                .map(item -> {
                    BizReceiptPurchaseApplyItem updateItem = new BizReceiptPurchaseApplyItem();
                    updateItem.setId(item.getId());
                    updateItem.setItemStatus(status);
                    updateItem.setModifyTime(new Date());
                    return updateItem;
                }).collect(Collectors.toList());
            
            // 使用批量更新提升性能
            itemDataWrap.updateBatchById(updateItems);
            log.debug("采购申请单[{}]{}个行项目状态更新完成", receiptCode, itemList.size());
        }
    }

    /**
     * 获取需求计划行项目列表
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptDemandPlanSearchPO"})
    @Out(parameter = {Const.BIZ_CONTEXT_KEY_VO + "#PageObjectVO<BizReceiptPurchaseApplyItemDTO>"})
    public void getDemandPlanItems(BizContext ctx) {
        log.info("开始获取需求计划行项目列表");
        
        // 先获取需求计划行项目
        demandPlanComponent.getUnMergeItems(ctx);
        IPage<BizReceiptDemandPlanItemDTO> demandPlanItems = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 转换为采购申请行项目DTO
        List<BizReceiptPurchaseApplyItemDTO> itemList = demandPlanItems.getRecords().stream()
            .map(this::convertToPurchaseApplyItem)
            .collect(Collectors.toList());

        // 设置分页结果
        PageObjectVO<BizReceiptPurchaseApplyItemDTO> pageVO = new PageObjectVO<>(itemList, demandPlanItems.getTotal());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, pageVO);
        log.info("需求计划行项目列表获取完成, 共{}条记录", demandPlanItems.getTotal());
    }

    /**
     * 将需求计划行项目转换为采购申请行项目
     */
    private BizReceiptPurchaseApplyItemDTO convertToPurchaseApplyItem(BizReceiptDemandPlanItemDTO demandPlanItem) {
        BizReceiptPurchaseApplyItemDTO purchaseApplyItem = new BizReceiptPurchaseApplyItemDTO();

        // 设置物料信息
        purchaseApplyItem.setMatId(demandPlanItem.getMatId());
        purchaseApplyItem.setMatCode(demandPlanItem.getMatCode());
        purchaseApplyItem.setMatName(demandPlanItem.getMatName());
        purchaseApplyItem.setMatNameEn(demandPlanItem.getMatNameEn());
        purchaseApplyItem.setMatGroupId(demandPlanItem.getMatGroupId());
        purchaseApplyItem.setMatGroupCode(demandPlanItem.getMatGroupCode());
        purchaseApplyItem.setMatGroupName(demandPlanItem.getMatGroupName());
        purchaseApplyItem.setProductName(demandPlanItem.getProductName());

        
        // 设置单位信息
        purchaseApplyItem.setUnitId(demandPlanItem.getUnitId());
        purchaseApplyItem.setUnitCode(demandPlanItem.getUnitCode());
        purchaseApplyItem.setUnitName(demandPlanItem.getUnitName());
        
        // 设置工厂信息
        purchaseApplyItem.setFtyId(demandPlanItem.getFtyId());
        purchaseApplyItem.setFtyCode(demandPlanItem.getFtyCode());
        purchaseApplyItem.setFtyName(demandPlanItem.getFtyName());

        // 设置需求信息
        purchaseApplyItem.setDemandQty(demandPlanItem.getDemandQty());
        purchaseApplyItem.setDemandDeptId(demandPlanItem.getDemandDeptId());
        purchaseApplyItem.setDemandDeptCode(demandPlanItem.getDemandDeptCode());
        purchaseApplyItem.setDemandDeptName(demandPlanItem.getDemandDeptName());
        purchaseApplyItem.setDemandUserId(demandPlanItem.getDemandUserId());
        purchaseApplyItem.setDemandUserCode(demandPlanItem.getDemandUserCode());
        purchaseApplyItem.setDemandUserName(demandPlanItem.getDemandUserName());

        // 设置前序单据信息
        purchaseApplyItem.setPreReceiptType(EnumReceiptType.DEMAND_PLAN.getValue());
        purchaseApplyItem.setPreReceiptId(demandPlanItem.getHeadId());
        purchaseApplyItem.setPreReceiptItemId(demandPlanItem.getId());
        purchaseApplyItem.setPreReceiptCode(demandPlanItem.getReceiptCode());
        purchaseApplyItem.setPreReceiptRid(demandPlanItem.getRid());


        // 设置数量相关字段
        purchaseApplyItem.setUnClearedQty(demandPlanItem.getDemandQty()); // 未清数量初始为需求数量
        purchaseApplyItem.setContractQty(BigDecimal.ZERO); // 已创建合同数量初始为0
        purchaseApplyItem.setContractChangeQty(BigDecimal.ZERO); // 合同变更数量初始为0
        purchaseApplyItem.setDeliveryQty(BigDecimal.ZERO); // 已送货数量初始为0
        purchaseApplyItem.setInputQty(BigDecimal.ZERO); // 已入库数量初始为0

        // 去年采购量，消耗量设置
        purchaseApplyItem.setStockQty(demandPlanItem.getStockQty());
        purchaseApplyItem.setTransferQty(demandPlanItem.getTransferQty());
        purchaseApplyItem.setLastYearPurchaseQty(demandPlanItem.getLastYearPurchaseQty());
        purchaseApplyItem.setLastYearConsumeQty(demandPlanItem.getLastYearConsumeQty());

        purchaseApplyItem.setWbsId(demandPlanItem.getWbsId());
        purchaseApplyItem.setWbsNo(demandPlanItem.getWbsNo());
        purchaseApplyItem.setWbsName(demandPlanItem.getWbsName());
        purchaseApplyItem.setCostCenterId(demandPlanItem.getCostCenterId());
        purchaseApplyItem.setCostCenter(demandPlanItem.getCostCenter());
        purchaseApplyItem.setCostCenterName(demandPlanItem.getCostCenterName());

        purchaseApplyItem.setAssetCardNo(demandPlanItem.getAssetCardNo());
        purchaseApplyItem.setAssetCardDesc(demandPlanItem.getAssetCardDesc());

        return purchaseApplyItem;
    }

    /**
     * 批量更新采购申请行项目数量
     * 可以更新未清数量、已创建合同数量、合同变更数量、已送货数量、已入库数量
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_LIST + "#List<BizReceiptPurchaseApplyItemDTO>"})
    public void batchUpdateItemQty(BizContext ctx) {
        List<BizReceiptPurchaseApplyItemDTO> updateList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_LIST);
        CurrentUser user = ctx.getCurrentUser();
        Date now = new Date();
        
        // 校验入参
        if (UtilCollection.isEmpty(updateList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        
        log.info("开始批量更新采购申请行项目数量, 共{}条记录", updateList.size());

        // 获取所有需要更新的行项目ID
        List<Long> itemIds = updateList.stream()
            .map(BizReceiptPurchaseApplyItemDTO::getId)
            .collect(Collectors.toList());

        // 批量查询行项目
        List<BizReceiptPurchaseApplyItem> items = itemDataWrap.listByIds(itemIds);
        if (UtilCollection.isEmpty(items)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 校验数量是否完整
        if (items.size() != itemIds.size()) {
            log.error("部分采购申请行项目不存在, 请求行数量:{}, 实际行数量:{}", itemIds.size(), items.size());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 构建ID到Item的映射,方便后续使用
        Map<Long, BizReceiptPurchaseApplyItem> itemMap = items.stream()
            .collect(Collectors.toMap(BizReceiptPurchaseApplyItem::getId, item -> item));

        // 构建批量更新列表
        List<BizReceiptPurchaseApplyItem> updateItems = new ArrayList<>();
        
        for (BizReceiptPurchaseApplyItemDTO updateDTO : updateList) {
            BizReceiptPurchaseApplyItem item = itemMap.get(updateDTO.getId());
            if (item == null) {
                log.error("采购申请行项目[{}]不存在", updateDTO.getId());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
            }

            // 构建更新对象
            BizReceiptPurchaseApplyItem updateItem = new BizReceiptPurchaseApplyItem();
            updateItem.setId(updateDTO.getId());
            updateItem.setModifyTime(now);
            updateItem.setModifyUserId(user.getId());

            // 更新数量
            boolean needUpdate = false;
            
            // 更新未清数量
            if (UtilObject.isNotNull(updateDTO.getUnClearedQty())) {
                BigDecimal newUnClearedQty = item.getUnClearedQty().add(updateDTO.getUnClearedQty());
                // 校验未清数量不能小于0
                if (newUnClearedQty.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("采购申请行项目[{}]未清数量不能小于0", updateDTO.getId());
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_APPLY_ITEM_UNCLEARED_QTY_CANNOT_BE_NEGATIVE);
                }
                updateItem.setUnClearedQty(newUnClearedQty);
                needUpdate = true;
            }

            // 更新已创建合同数量
            if (UtilObject.isNotNull(updateDTO.getContractQty())) {
                BigDecimal newContractQty = item.getContractQty().add(updateDTO.getContractQty());
                // 校验已创建合同数量不能小于0
                if (newContractQty.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("采购申请行项目[{}]已创建合同数量不能小于0", updateDTO.getId());
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PURCHASE_APPLY_ITEM_CONTRACT_QTY_CANNOT_BE_NEGATIVE);
                }
                updateItem.setContractQty(newContractQty);
                needUpdate = true;
            }

            // 更新合同变更数量
            if (UtilObject.isNotNull(updateDTO.getContractChangeQty())) {
                BigDecimal newContractChangeQty = item.getContractChangeQty().subtract(updateDTO.getContractChangeQty());
                updateItem.setContractChangeQty(newContractChangeQty);

                BigDecimal newContractQty = item.getContractQty().add(updateDTO.getContractChangeQty());
                // 校验已创建合同数量不能小于0
                if (newContractQty.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("采购申请行项目[{}]已创建合同数量不能小于0", updateDTO.getId());
                    throw new WmsException(
                            EnumReturnMsg.RETURN_CODE_PURCHASE_APPLY_ITEM_CONTRACT_QTY_CANNOT_BE_NEGATIVE);
                }
                updateItem.setContractQty(newContractQty);

                needUpdate = true;
            }

            // 更新已送货数量
            if (UtilObject.isNotNull(updateDTO.getDeliveryQty())) {
                BigDecimal newDeliveryQty = item.getDeliveryQty().add(updateDTO.getDeliveryQty());
                // 校验已送货数量不能小于0
                if (newDeliveryQty.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("采购申请行项目[{}]已送货数量不能小于0", updateDTO.getId());
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_MUST_GT_ZERO);
                }
                updateItem.setDeliveryQty(newDeliveryQty);
                needUpdate = true;
            }

            // 更新已入库数量
            if (UtilObject.isNotNull(updateDTO.getInputQty())) {
                BigDecimal newInputQty = item.getInputQty().add(updateDTO.getInputQty());
                // 校验已入库数量不能小于0
                if (newInputQty.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("采购申请行项目[{}]已入库数量不能小于0", updateDTO.getId());
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_MUST_GT_ZERO);
                }
                updateItem.setInputQty(newInputQty);
                needUpdate = true;
            }

            if (needUpdate) {
                updateItems.add(updateItem);
            }
        }

        // 执行批量更新
        if (!updateItems.isEmpty()) {
            boolean success = itemDataWrap.updateBatchById(updateItems);
            if (!success) {
                log.error("采购申请行项目批量更新失败");
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DB_UPDATE_ERROR);
            }
            log.debug("批量更新{}条采购申请行项目完成", updateItems.size());
        }

        log.info("采购申请行项目数量批量更新完成");
    }

    /**
     * 取消采购申请行项目
     * 校验行项目已创建合同数量必须为0，验证通过后删除行项目并同步修改前序单据中相关行项目的已采购申请数量
     */
    @In(parameter = {Const.BIZ_CONTEXT_KEY_PO + "#BizReceiptPurchaseApplyUpdatePO"})
    public void cancelItems(BizContext ctx) {
        BizReceiptPurchaseApplyUpdatePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptPurchaseApplyItemDTO> itemList = po.getItemList();
        CurrentUser user = ctx.getCurrentUser();
        Date now = new Date();

        // 校验入参
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        log.info("开始取消采购申请行项目, 共{}条记录", itemList.size());

        // 获取所有需要取消的行项目ID
        List<Long> itemIds = itemList.stream()
            .map(BizReceiptPurchaseApplyItemDTO::getId)
            .collect(Collectors.toList());

        // 批量查询行项目
        List<BizReceiptPurchaseApplyItem> items = itemDataWrap.listByIds(itemIds);
        if (UtilCollection.isEmpty(items)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 校验数量是否完整
        if (items.size() != itemIds.size()) {
            log.error("部分采购申请行项目不存在, 请求行数量:{}, 实际行数量:{}", itemIds.size(), items.size());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_NOT_EXIST);
        }

        // 校验已创建合同数量必须为0
        for (BizReceiptPurchaseApplyItem item : items) {
            if (item.getContractQty().compareTo(BigDecimal.ZERO) > 0) {
                log.error("采购申请行项目[{}]已创建合同数量不为0,不能取消", item.getId());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_HAS_CONTRACT);
            }
        }

        // 收集需要更新的需求计划行项目
        List<BizReceiptDemandPlanItemQtyDTO> updateDemandPlanItems = new ArrayList<>();
        for (BizReceiptPurchaseApplyItem item : items) {
            // 只处理前序单据为需求计划的行项目
            if (EnumReceiptType.DEMAND_PLAN.getValue().equals(item.getPreReceiptType()) 
                && UtilObject.isNotNull(item.getPreReceiptItemId())) {
                
                BizReceiptDemandPlanItemQtyDTO qtyDTO = new BizReceiptDemandPlanItemQtyDTO();
                qtyDTO.setId(item.getPreReceiptItemId());
                // 设置负数,用于减少需求计划的已采购数量
                qtyDTO.setPurchaseQty(item.getDemandQty().negate());
                updateDemandPlanItems.add(qtyDTO);
                
                log.debug("需求计划行项目[{}]需要更新采购数量:{}", 
                    item.getPreReceiptItemId(), 
                    qtyDTO.getPurchaseQty());
            }
        }

        // 删除采购申请行项目
        boolean success = itemDataWrap.removeByIds(itemIds);
        if (!success) {
            log.error("采购申请行项目删除失败");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_CAN_NOT_DELETE);
        }
        log.debug("删除{}条采购申请行项目完成", itemIds.size());

        // 检查头表下是否还有未删除的行项目
        List<Long> headIds = items.stream()
            .map(BizReceiptPurchaseApplyItem::getHeadId)
            .distinct()
            .collect(Collectors.toList());

        for (Long headId : headIds) {
            int remainingItems = (int)itemDataWrap.count(
                new QueryWrapper<BizReceiptPurchaseApplyItem>()
                    .lambda()
                    .eq(BizReceiptPurchaseApplyItem::getHeadId, headId)
            );
            
            if (remainingItems == 0) {
                // 如果没有剩余行项目,逻辑删除头表
                headDataWrap.removeById(headId);
                log.debug("采购申请单[{}]头表逻辑删除完成", headId);
            }
        }

        // 更新需求计划行项目数量
        if (UtilCollection.isNotEmpty(updateDemandPlanItems)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_LIST, updateDemandPlanItems);
            demandPlanComponent.batchUpdateItemQtyAndStatus(ctx);
            log.debug("需求计划行项目数量更新完成, 共{}条记录", updateDemandPlanItems.size());
        }

        // 保存操作日志
        for (BizReceiptPurchaseApplyItem item : items) {
            receiptOperationLogService.saveBizReceiptOperationLogList(
                item.getHeadId(),
                EnumReceiptType.PURCHASE_APPLY.getValue(),
                EnumReceiptOperationType.RECEIPT_OPERATION_CANCEL,
                String.format("取消行项目[%s]", item.getRid()),
                user.getId()
            );
        }

        log.info("采购申请行项目取消完成");
    }

    public BizCommonFile upload(MultipartFile fileInClient, CurrentUser user) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String refId = srmService.uploadFile(fileInClient, uuid, uploadUrl);
        BizCommonFile file = fileService.upload(fileInClient, user);
        if (UtilString.isNotNullOrEmpty(refId)) {
            file.setFileRefId(refId);
        }
        bizCommonFileDataWrap.updateById(file);
        return file;
    }

    /**
     * WMS推送“一般性需求计划”与“框架协议采购”采购申请大类下的所有已完成的采购申请单据以及附件信息
     * 新增直接采购
     * @param ctx
     */
    public void pullSrm(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        dataFillService.fillAttr(headDTO);
        List<Integer> demandPlanTypeList = new ArrayList<>();
        demandPlanTypeList.add(EnumDemandPlanType.NORMAL_PLAN.getCode());
        demandPlanTypeList.add(EnumDemandPlanType.FRAMEWORK_AGREEMENT_PROCUREMENT_PLAN.getCode());
        // demandPlanTypeList.add(EnumDemandPlanType.DIRECT_PURCHASE.getCode());
        List<Integer> receiptStatusList = Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        if (!receiptStatusList.contains(headDTO.getReceiptStatus()) || !demandPlanTypeList.contains(headDTO.getDemandPlanType())) {
            return;
        }
        // 更新状态未同步
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());

        JSONObject headJson = this.getParam(headDTO);
        headDTO.setSrmRequest(headJson.toJSONString());
        bizReceiptPurchaseApplyHeadDataWrap.updateDtoById(headDTO);
        Map<String, String> response = srmService.call(headJson.toJSONString(), createMainUrl);

        if (response.isEmpty()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, "response empty");
        } else if (response.containsKey("fail")) {
            throw new WmsException("寻源竞价调用失败：" + response.get("fail"));
        } else {
            JSONObject jsonObject = JSONObject.parseObject(response.get("success"));
            // 获取 bidMain 对象
            JSONObject bidMain = jsonObject.getJSONObject("bidMain");

            // 从 bidMain 中获取 id 的值
            Integer idValue = bidMain.getInteger("id");
            if (UtilNumber.isNotEmpty(idValue)) {
                headDTO.setSrmStatus(1);
                bizReceiptPurchaseApplyHeadDataWrap.updateDtoById(headDTO);
            }
        }

        // 更新状态已完成
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

    }


    private JSONObject getParam(BizReceiptPurchaseApplyHeadDTO headDTO) {
        JSONObject headJson = new JSONObject();
        // 创建人工号
        headJson.put("creatorCode", headDTO.getCreateUserCode());
        if (UtilCollection.isNotEmpty(headDTO.getFileList())) {
            // 附件信息
            headJson.put("fileId", headDTO.getFileList().get(0).getFileRefId());
        }
        // 采购申请描述
        headJson.put("bidName", headDTO.getPurchaseDescription());
        // 采购主体
        headJson.put("companyCode", headDTO.getPurchaseSubject() == 2 ? "2058" : "1104");
        // 采购类型
        headJson.put("detailedProjectClassification", headDTO.getPurchaseType() * 10);
        // 项目经理名称
        headJson.put("pmName", headDTO.getCreateUserName());
        // 小数位数
        headJson.put("decimalDigit", headDTO.getItemList().get(0).getDecimalPlace());
        // 税率填写人
        // 10	采购人
        // 20	供应商
        headJson.put("taxWay", 10);
        // 计划类型
        headJson.put("purchaseTypeInfo", headDTO.getDemandPlanType());
        // 是否密封报价
        headJson.put("isQuoteSeal", 1);
        // 是否报所有项
        headJson.put("isQuoteAll", 1);

        // 项目经理工号
        headJson.put("pmId", headDTO.getCreateUserCode());
        // 招标方式
        headJson.put("purchaseMode", headDTO.getBidMethod());
        if (headDTO.getBidMethod().equals(EnumBidMethod.DIRECT_PURCHASE.getCode())) {
            // 是否评审
            headJson.put("isEva", 0);
            // 评审办法
            // 10	综合评价法
            // 20	最低评标价法
            // 30	经评审的最低投标价法
            // 40	最低投标价法
            // headJson.put("evaMethod", 10);
        }
        // 邀请供应商名称
        List<String> bidderNameList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(headDTO.getSupplierItemList())) {
            bidderNameList = headDTO.getSupplierItemList().stream().map(BizReceiptPurchaseApplySupplierItemDTO::getSupplierName).collect(Collectors.toList());
        }
        headJson.put("bidderNameList", bidderNameList);
        // 采购申请单号
        headJson.put("thirdPartKey", headDTO.getReceiptCode());
        // 创建时间
        headJson.put("createTime", headDTO.getCreateTime());
        List<BizReceiptPurchaseApplyItemDTO> itemList = headDTO.getItemList();
        JSONObject itemMap = new JSONObject();
        // 排序
        itemMap.put("itemSeq", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getRid).collect(Collectors.toList()));
        // 物料编码
        itemMap.put("itemCode", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getMatCode).collect(Collectors.toList()));
        // 物料描述
        itemMap.put("itemDescCn", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getMatName).collect(Collectors.toList()));
        // 物料描述英文
        itemMap.put("itemDescEn", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getMatNameEn).collect(Collectors.toList()));
        // 资产卡片号
        itemMap.put("assetCardNumber", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getAssetCardNo).collect(Collectors.toList()));
        // 资产卡片描述
        itemMap.put("assetCardDesc", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getAssetCardDesc).collect(Collectors.toList()));
        // 品名
        itemMap.put("productName", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getProductName).collect(Collectors.toList()));
        // 物料组
        itemMap.put("itemGroup", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getMatGroupName).collect(Collectors.toList()));
        // 成本中心
        itemMap.put("costCenter", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getCostCenterName).collect(Collectors.toList()));
        // wbs
        itemMap.put("wbs", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getWbsName).collect(Collectors.toList()));
        // 计量单位
        itemMap.put("meteringUnit", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getUnitName).collect(Collectors.toList()));
        // 数量
        itemMap.put("requestQuantity", itemList.stream().map(c -> c.getDemandQty().stripTrailingZeros().toPlainString()).collect(Collectors.toList()));
        // 需求计划单号
        itemMap.put("requirePlanNo", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getPreReceiptCode).collect(Collectors.toList()));
        // 需求计划行号
        itemMap.put("requirePlanLineNo", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getPreReceiptRid).collect(Collectors.toList()));
        // 需求部门
        itemMap.put("requireDept", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getDemandDeptName).collect(Collectors.toList()));
        // 需求人
        itemMap.put("requirePerson", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getCreateUserName).collect(Collectors.toList()));
        // 第三方id
        itemMap.put("thirdPartId", itemList.stream().map(BizReceiptPurchaseApplyItemDTO::getId).collect(Collectors.toList()));
        headJson.put("fieldsMaps", itemMap);
        return headJson;
    }


    /**
     * 发起审批
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 发起流程审批
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        String receiptTypeStr = String.valueOf(headDTO.getReceiptType());
        if (UtilNumber.isNotEmpty(headDTO.getPurchaseSubject()) && headDTO.getPurchaseSubject().equals(1)) {
            // 当采购申请基础信息中的“采购主体”为华信采购时，审批流程如下：开始-经营部门领导-需求部门分管领导-财务部门领导-经营部分管领导-华信（公司领导）李吉根-结束
            receiptTypeStr = headDTO.getReceiptType() + "1";
        } else if (UtilNumber.isNotEmpty(headDTO.getPurchaseSubject()) && headDTO.getPurchaseSubject().equals(2)) {
            // 当采购申请基础信息中的“采购主体”为能殷采购时，审批流程如下：开始-经营部门领导-需求部门分管领导-经营部分管领导-华信公司领导李吉根-能殷（公司领导）林申晟-结束
            receiptTypeStr = headDTO.getReceiptType() + "2";
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }

        Map<String, Object> variables = new HashMap<>();
        // 审批人校验
        this.approveCheck(headDTO, variables);

        // 采购申请：“请审批”[公司+部门]用户姓名+“提交的流程”：+采购方案描述（取采购申请抬头的采购方案描述）
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getPurchaseDescription());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, Integer.valueOf(receiptTypeStr), ctx, headDTO.getPurchaseDescription());
        workflowService.startWorkFlow(receiptId, receiptCode, Integer.valueOf(receiptTypeStr), variables);

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());
    }

    /**
     * 审批人校验
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    private void approveCheck(BizReceiptPurchaseApplyHeadDTO headDTO, Map<String, Object> variables) {

        // 一级审批经营部部门领导
        String deptCode = EnumDept.BMD.getCode();
        List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        // 二级审批业务需求部门分管领导
        String demandDeptCode = headDTO.getItemList().get(0).getDemandDeptCode();
        variables.put("demandDeptCode", demandDeptCode);
        List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(demandDeptCode, null, EnumApprovalLevel.LEVEL_3);
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
        if (UtilNumber.isNotEmpty(headDTO.getPurchaseSubject()) && headDTO.getPurchaseSubject().equals(1)) {
            // 三级审批财务部部门领导
            List<String> userList3 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD.getCode(), null, EnumApprovalLevel.LEVEL_2);
            if (UtilCollection.isEmpty(userList3)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
            }
            // 四级审批经营部分管领导
            List<String> userList4 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
            if (UtilCollection.isEmpty(userList4)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "4");
            }
            // 四级审批 -华信（公司领导）李吉根 10100212
            List<SysUser> userList5 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "10100212"));
            if (UtilCollection.isEmpty(userList5)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "5");
            }
            variables.put("userCode1", userList5.get(0).getUserCode());
        } else if (UtilNumber.isNotEmpty(headDTO.getPurchaseSubject()) && headDTO.getPurchaseSubject().equals(2)) {
            // 三级审批经营部分管领导
            List<String> userList3 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
            if (UtilCollection.isEmpty(userList3)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
            }
            // 四级审批 -华信（公司领导）李吉根 10100212
            List<SysUser> userList4 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "10100212"));
            if (UtilCollection.isEmpty(userList4)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "4");
            }
            variables.put("userCode1", userList4.get(0).getUserCode());
            // 五级审批 -能殷（公司领导）林申晟 10100714
            List<SysUser> userList5 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "10100714"));
            if (UtilCollection.isEmpty(userList5)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "5");
            }
            variables.put("userCode2", userList5.get(0).getUserCode());
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptPurchaseApplyHead head = bizReceiptPurchaseApplyHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptPurchaseApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptPurchaseApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            // 推送srm寻源竞价
            this.pullSrm(ctx);
            // 自动更新已提报预算金额、已提报,未生成合同金额；
            this.updateBudget(headDTO);
        } else {

            // 如果驳回时携带了废弃标记，则直接关闭单据
            if(EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())){
                // 更新状态已关闭
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());

                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "采购申请的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getCreateUserId()).getUserCode()), head.getReceiptCode());
            } else {

                // 更新状态已驳回
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());

                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "采购申请的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getCreateUserId()).getUserCode()), head.getReceiptCode());
            }
        }
    }

    /**
     * 单据撤销
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptPurchaseApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 删除待办:必须在审批撤销前
        String id = workflowService.deleteTodo(headDTO.getId());
        // 审批撤销
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(id);
        workflowService.revoke(revokeDTO);
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE, "", user.getId());

    }

}
