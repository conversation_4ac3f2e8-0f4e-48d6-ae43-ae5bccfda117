package com.inossem.wms.bizdomain.transport.service.component;

import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportApplyHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportApplyItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportApplyHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportApplyItem;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调拨申请
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportApplyComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService bizReceiptAttachmentService;

    @Autowired
    private BizReceiptTransportApplyHeadDataWrap bizReceiptTransportApplyHeadDataWrap;

    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;

    @Autowired
    private BizReceiptTransportApplyItemDataWrap bizReceiptTransportApplyItemDataWrap;

    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        BizReceiptTransportApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        headDTO.setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName());
        ButtonVO buttonVO = new ButtonVO();
        // 草稿状态,按钮保存、提交、删除
        buttonVO.setButtonSave(true);
        buttonVO.setButtonSubmit(true);
        buttonVO.setButtonDelete(false);
        // tab页签默认全不启用
        ExtendVO extend = new ExtendVO();
        extend.setWfRequired(false);
        extend.setAttachmentRequired(false);
        extend.setOperationLogRequired(false);
        extend.setRelationRequired(false);
        // 返回空行项目对象
        BizResultVO<BizReceiptTransportApplyHeadDTO> vo = new BizResultVO<>(headDTO, extend, buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 开启审批
     */
    public void setExtendWf(BizContext ctx) {
        BizResultVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setWfRequired(false);
    }

    /**
     * 开启附件
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setAttachmentRequired(true);
    }

    /**
     * 开启操作日志
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setOperationLogRequired(true);
    }

    /**
     * 开启单据流
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        vo.getExtend().setRelationRequired(true);
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    private void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptTransportApplyHead head = new BizReceiptTransportApplyHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptTransportApplyHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptTransportApplyItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptTransportApplyItem::getItemStatus, receiptStatus)
            .eq(BizReceiptTransportApplyItem::getHeadId, id);
        bizReceiptTransportApplyItemDataWrap.update(wrapper);
    }

    /**
     * 查询库存
     */
    public void getStock(BizContext ctx) {
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            // 物料编码不是空时, 根据编码查询id
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (UtilNumber.isEmpty(matId)) {
                // 物料不存在
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
            }
            po.setMatId(matId);
        }
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        // 根据单据类型获取特性
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCode(
            EnumReceiptType.STOCK_TRANSPORT_OUT_APPLY.getValue(), po.getFtyId(), po.getLocationId(), po.getMatId(),
            EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), "");
        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (Collections.isEmpty(assembleDTOList)) {
            // 未查询到库存信息
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        } else {
            if (UtilCollection.isNotEmpty(po.getItemDTOList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptTransportItemDTO itemDTO : po.getItemDTOList()) {
                    for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                        for (BizReceiptAssembleDTO dto : assembleDTOList) {
                            if (dto.getSpecCode().equals(assembleDTO.getSpecCode())
                                && dto.getSpecValue().equals(assembleDTO.getSpecValue())) {
                                dto.setStockQty(dto.getStockQty().subtract(assembleDTO.getQty()));
                            }
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(po.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                            // 存储单元
                            stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                        && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue())) && // 并且不是物料转性-343,344
                        !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_343)
                        && !dicMoveType.getMoveTypeCode().equals(Const.MOVE_TYPE_344)) {
                        stockBinDTOList.add(stockBinDTO);
                    }
                }
                // 批量查询标签列表
                if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                    List<BizLabelData> labelDataVOList = labelDataService.getList(stockBinDTOList);
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                        for (int i = 0; i < codeList.size(); i++) {
                            if (codeList.get(i).equals(tableFieldNameBatchId)) {
                                // 批次
                                batchInfoId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                                // 存储类型
                                typeId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                                // 存储单元
                                cellId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                                // 仓位
                                binId = Long.parseLong(valueList.get(i));
                            }
                        }
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        for (BizLabelData labelData : labelDataVOList) {
                            if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                && labelData.getMatId().equals(assembleDTO.getMatId())
                                && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                && labelData.getBatchId().equals(batchInfoId) && labelData.getTypeId().equals(typeId)
                                && labelData.getCellId().equals(cellId) && labelData.getBinId().equals(binId)) {
                                // 唯一键相同时,匹配
                                BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                                labelReceiptRelDTO.setLabelId(labelData.getId());
                                labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                                labelReceiptRelDTO.setQty(labelData.getQty());
                                labelDataList.add(labelReceiptRelDTO);
                            }
                        }
                        assembleDTO.setLabelDataList(labelDataList);
                    }
                }
            }
            // 返回对象
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
        }
    }

    /**
     * 列表 - 分页
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询条件设置：单据号精确搜索，状态列表，申请单创建时间范围，创建时间倒序
        QueryWrapper<BizReceiptTransportApplyHead> wrapper = new QueryWrapper<>();
        Date startTime = null;
        Date endTime = null;
        if (UtilObject.isNotNull(po.getApplyStartTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getApplyStartTime());
            endTime = UtilLocalDateTime.getEndTime(po.getApplyEndTime());
        }
        wrapper.lambda()
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportApplyHead::getReceiptCode,
                po.getReceiptCode())
            .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptTransportApplyHead::getReceiptStatus,
                po.getReceiptStatusList())
            .between((UtilObject.isNotNull(startTime)), BizReceiptTransportApplyHead::getCreateTime, startTime,
                endTime);
        // 若无排序则默认按时间倒序
        if (UtilString.isNullOrEmpty(po.getDescSortColumn()) && UtilString.isNullOrEmpty(po.getAscSortColumn())) {
            wrapper.lambda().orderByDesc(BizReceiptTransportApplyHead::getCreateTime);
        }
        // 分页处理
        IPage<BizReceiptTransportApplyHead> page = new Page<>(po.getPageIndex(), po.getPageSize());
        bizReceiptTransportApplyHeadDataWrap.page(page, wrapper);
        // 转dto
        List<BizReceiptTransportApplyHeadDTO> dtoList =
            UtilCollection.toList(page.getRecords(), BizReceiptTransportApplyHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 列表 - 没有分页
     */
    public void getList(BizContext ctx) {
        // 上下文入参
        BizReceiptTransportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询条件设置：单据号精确搜索，状态列表，申请单创建时间范围，创建时间倒序
        QueryWrapper<BizReceiptTransportApplyHead> wrapper = new QueryWrapper<>();
        Date startTime = null;
        Date endTime = null;
        if (UtilObject.isNotNull(po.getApplyStartTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getApplyStartTime());
            endTime = UtilLocalDateTime.getEndTime(po.getApplyEndTime());
        }
        wrapper.lambda()
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptTransportApplyHead::getReceiptCode,
                po.getReceiptCode())
            .in(BizReceiptTransportApplyHead::getReceiptStatus, po.getReceiptStatusList()).between(
                (UtilObject.isNotNull(startTime)), BizReceiptTransportApplyHead::getCreateTime, startTime, endTime);
        // 若无排序则默认按时间倒序
        if (UtilString.isNullOrEmpty(po.getDescSortColumn()) && UtilString.isNullOrEmpty(po.getAscSortColumn())) {
            wrapper.lambda().orderByDesc(BizReceiptTransportApplyHead::getCreateTime);
        }
        // 查询列表
        List<BizReceiptTransportApplyHead> bizReceiptTransportHeadList =
            bizReceiptTransportApplyHeadDataWrap.list(wrapper);
        // 转dto
        List<BizReceiptTransportApplyHeadDTO> dtoList =
            UtilCollection.toList(bizReceiptTransportHeadList, BizReceiptTransportApplyHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dtoList));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptTransportApplyHead bizReceiptTransportApplyHead =
            bizReceiptTransportApplyHeadDataWrap.getById(headId);
        BizReceiptTransportApplyHeadDTO bizReceiptTransportApplyHeadDTO =
            UtilBean.newInstance(bizReceiptTransportApplyHead, BizReceiptTransportApplyHeadDTO.class);
        // 填充属性
        dataFillService.fillAttr(bizReceiptTransportApplyHeadDTO);
        // 查询库存数量
        if (UtilCollection.isNotEmpty(bizReceiptTransportApplyHeadDTO.getItemDTOList())) {
            BizReceiptTransportApplyItemDTO itemDTO = bizReceiptTransportApplyHeadDTO.getItemDTOList().get(0);
            BizReceiptAssembleRuleDTO bizReceiptAssembleRuleDTO =
                stockCommonService.getStockByFeatureCode(bizReceiptTransportApplyHeadDTO.getReceiptType(), // 发出方工厂id
                    itemDTO.getOutputFtyId(), // 发出方库存地点id
                    itemDTO.getOutputLocationId(), // itemDTO.getOutputMatId(), //发出方物料id
                    null, // 非限制
                    EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), Const.STRING_EMPTY);
            List<BizReceiptAssembleDTO> assembleDTOList = bizReceiptAssembleRuleDTO.getAssembleDTOList();
            Map<Long, List<BizReceiptAssembleDTO>> matQtyMap =
                assembleDTOList.stream().collect(Collectors.groupingBy(BizReceiptAssembleDTO::getMatId));
            for (BizReceiptTransportApplyItemDTO dto : bizReceiptTransportApplyHeadDTO.getItemDTOList()) {
                if (matQtyMap.containsKey(dto.getOutputMatId())) {
                    dto.setStockQty(matQtyMap.get(dto.getOutputMatId()).stream().map(BizReceiptAssembleDTO::getStockQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        }
        // 按钮
        Integer receiptStatus = bizReceiptTransportApplyHeadDTO.getReceiptStatus();
        // 草稿状态与已完成状态都可删除，被调拨出库或调拨入库引用的申请单不可以删除
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonSave(true);
            buttonVO.setButtonSubmit(true);
            buttonVO.setButtonDelete(true);
        }
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            QueryWrapper<BizReceiptTransportItem> wrapper = new QueryWrapper<>();
            // 这里其实应该用Head表里的preReceiptHeadId判断的，但是保存出库、入库单时preReceiptHeadId没传给我，所以才用preReceiptItemId判断的
            wrapper.lambda().in(BizReceiptTransportItem::getPreReceiptItemId, bizReceiptTransportApplyHeadDTO
                .getItemDTOList().stream().map(BizReceiptTransportApplyItemDTO::getId).collect(Collectors.toList()));
            List<BizReceiptTransportItem> bizReceiptTransportItemList = bizReceiptTransportItemDataWrap.list(wrapper);
            if (UtilCollection.isEmpty(bizReceiptTransportItemList)) {
                // 已完成状态并且不被后续引用，可以删除
                buttonVO.setButtonDelete(true);
            }
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(bizReceiptTransportApplyHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 校验数据
     */
    public void check(BizContext ctx) {
        BizReceiptTransportApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (headDTO == null) {
            log.warn("提交的单据缺少必要的参数。无法验证信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            log.warn("提交的单据没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 校验：申请数量不可以大于现有库存数量
        if (UtilCollection.isNotEmpty(headDTO.getItemDTOList())) {
            BizReceiptTransportApplyItemDTO itemDTO = headDTO.getItemDTOList().get(0);
            // 查询库存数量
            BizReceiptAssembleRuleDTO bizReceiptAssembleRuleDTO =
                stockCommonService.getStockByFeatureCode(headDTO.getReceiptType(), // 发出方工厂id
                    itemDTO.getOutputFtyId(), // 发出方库存地点id
                    itemDTO.getOutputLocationId(), // itemDTO.getOutputMatId(), // 非限制库存
                    null, // 非限制库存
                    EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), Const.STRING_EMPTY);
            List<BizReceiptAssembleDTO> assembleDTOList = bizReceiptAssembleRuleDTO.getAssembleDTOList();
            Map<Long, List<BizReceiptAssembleDTO>> matQtyMap =
                assembleDTOList.stream().collect(Collectors.groupingBy(BizReceiptAssembleDTO::getMatId));
            for (BizReceiptTransportApplyItemDTO dto : headDTO.getItemDTOList()) {
                if (!matQtyMap.containsKey(dto.getOutputMatId())) {
                    log.warn("物料不存在");
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST);
                }
                if (matQtyMap.get(dto.getOutputMatId()).stream().map(BizReceiptAssembleDTO::getStockQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(dto.getApplyQty()) < 0) {
                    log.warn("申请数量大于现有库存数量");
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_ON_THE_WAY);
                }
            }
        }
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {
        BizReceiptTransportApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // head处理
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(id)) {
            // 根据id更新
            bizReceiptTransportApplyHeadDataWrap.updateDtoById(headDTO);
            // 特征表物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(id);
            // item物理删除
            bizReceiptTransportApplyItemDataWrap.deleteByHeadId(id);
            // 单据日志 - 修改
            receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_SAVE, "", ctx.getCurrentUser().getId());
        } else {
            // 新增
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_APPLY.getValue());
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptTransportApplyHeadDataWrap.saveDto(headDTO);
            id = headDTO.getId();
            // 单据日志 - 新增
            receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH, "", ctx.getCurrentUser().getId());
        }
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, code);
        // item处理
        List<BizReceiptTransportApplyItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptTransportApplyItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(id);
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(ctx.getCurrentUser().getId());
        }
        bizReceiptTransportApplyItemDataWrap.saveBatchDto(itemDTOList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptTransportApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                bizReceiptAssembleDTO.setReceiptType(headDTO.getReceiptType());
                bizReceiptAssembleDTO.setReceiptHeadId(id);
                bizReceiptAssembleDTO.setReceiptItemId(itemDto.getId());
                bizReceiptAssembleDTO.setId(null);
                bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                    ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                    : bizReceiptAssembleDTO.getSpecType());
                assembleDTOList.add(bizReceiptAssembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        List<Long> labelRelIdList = new ArrayList<>();
        for (BizReceiptTransportApplyItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                if (UtilCollection.isNotEmpty(bizReceiptAssembleDTO.getLabelDataList())) {
                    for (BizLabelReceiptRelDTO labelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(labelReceiptRelDTO.getLabelId());
                        labelReceiptRel.setReceiptType(headDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(itemDto.getHeadId());
                        labelReceiptRel.setReceiptItemId(itemDto.getId());
                        labelReceiptRel.setReceiptBinId(bizReceiptAssembleDTO.getId());
                        bizLabelReceiptRelList.add(labelReceiptRel);
                        labelRelIdList.add(labelReceiptRelDTO.getId());
                    }
                }
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.removeByIds(labelRelIdList);
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptTransportApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        bizReceiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 逻辑删除附件
        bizReceiptAttachmentService.deleteBizReceiptAttachment(headId, headDTO.getReceiptType());
    }

    /**
     * 状态变更已提交
     */
    public void updateStatusSubmitted(BizContext ctx) {
        BizReceiptTransportApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptTransportApplyHead head = bizReceiptTransportApplyHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptTransportApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptTransportApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        List<Long> itemIds = new ArrayList<>();
        for (BizReceiptTransportApplyItemDTO itemDto : headDTO.getItemDTOList()) {
            List<Long> binIds = new ArrayList<>();
            if (UtilCollection.isNotEmpty(itemDto.getAssembleDTOList())) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDto.getAssembleDTOList()) {
                    binIds.add(bizReceiptAssembleDTO.getId());
                }
            }
            bizReceiptAssembleDataWrap.removeByIds(binIds);
            itemIds.add(itemDto.getId());
        }
        bizReceiptTransportApplyItemDataWrap.removeByIds(itemIds);
        bizReceiptTransportApplyHeadDataWrap.removeById(headId);
    }

    /**
     * 提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        this.save(ctx);
    }

}