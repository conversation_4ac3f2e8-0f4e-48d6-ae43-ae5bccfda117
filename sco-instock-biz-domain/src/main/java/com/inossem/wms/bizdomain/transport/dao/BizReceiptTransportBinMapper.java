package com.inossem.wms.bizdomain.transport.dao;

import org.apache.ibatis.annotations.Delete;

import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportBin;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;

/**
 * <p>
 * 转储单配货明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
public interface BizReceiptTransportBinMapper extends WmsBaseMapper<BizReceiptTransportBin> {

    /**
     * item物理删除
     *
     * @param headId 主表主键
     */
    @Delete("DELETE FROM biz_receipt_transport_bin WHERE item_id IN (SELECT id FROM biz_receipt_transport_item WHERE head_id = #{headId})")
    void deleteByHeadId(Long headId);
}
