package com.inossem.wms.bizdomain.transport.service.biz;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import com.inossem.wms.bizdomain.transport.service.component.TransportInComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * 调拨入库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class TransportInService {

    @Autowired
    private TransportInComponent transportInComponent;

    /**
     * 页面初始化
     */
    @Entrance(call = {"transportInComponent#init", "transportInComponent#setExtendWf",
        "transportInComponent#setExtendAttachment", "transportInComponent#setExtendOperationLog",
        "transportInComponent#setExtendRelation"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportInComponent.init(ctx);

        // 开启审批
        transportInComponent.setExtendWf(ctx);

        // 开启附件
        transportInComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportInComponent.setExtendOperationLog(ctx);

        // 开启单据流
        transportInComponent.setExtendRelation(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transportInComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportInComponent.getStock(ctx);
    }

    /**
     * 查询出库单的库存
     */
    @Entrance(call = {"transportInComponent#getOutStock"})
    public void getOutStock(BizContext ctx) {

        // 查询出库单的库存
        transportInComponent.getOutStock(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transportInComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportInComponent.getPage(ctx);
    }

    /**
     * 列表 - 没有分页
     */
    @Entrance(call = {"transportInComponent#getList"})
    public void getList(BizContext ctx) {

        // 列表 - 没有分页
        transportInComponent.getList(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transportInComponent#getInfo", "transportInComponent#setRecommendBin",
        "transportInComponent#setExtendAttachment", "transportInComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 详情，包含按钮组和扩展功能
        transportInComponent.getInfo(ctx);

        // 开启附件
        transportInComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportInComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transportInComponent#check", "transportInComponent#save",
        "transportInComponent#saveBizReceiptAttachment", "transportInComponent#saveBizReceiptOperationLog"})
    public void save(BizContext ctx) {

        // 校验数据
        transportInComponent.check(ctx);

        // 保存
        transportInComponent.save(ctx);

        // 保存附件
        transportInComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportInComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 提交-基于调拨申请
     */
    @Entrance(call = {"transportInComponent#check", "transportInComponent#setAssembleInputBatchId",
        "transportInComponent#submit", "transportInComponent#saveBizReceiptAttachment",
        "transportInComponent#saveReceiptTree", "transportInComponent#saveBizReceiptOperationLog",
        "transportInComponent#updateStatusSubmitted", "transportInComponent#saveOutputBinByMoveType",
        "transportInComponent#generateNoTaskInsDocToPost", "transportInComponent#generateInsDocToPostByAssemble",
        "transportInComponent#checkAndComputeForModifyStock", "transportInComponent#updateStatusUnsync",
        "transportInComponent#post", "transportInComponent#modifyStock", "transportInComponent#modifyLabel",
        "transportInComponent#updateStatusPosted", "transportInComponent#updateStatusCompleted",
        "transportInComponent#generateInputTaskReq"})
    public void submit(BizContext ctx) {

        // 校验数据
        transportInComponent.check(ctx);

        //
        transportInComponent.setAssembleInputBatchId(ctx);

        // 提交
        transportInComponent.submit(ctx);

        // 保存附件
        transportInComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        transportInComponent.saveReceiptTree(ctx);

        // 保存操作日志
        transportInComponent.saveBizReceiptOperationLog(ctx);

        // 【先过账模式】根据assemble生成ins凭证
        transportInComponent.generateInsDocToPostByAssemble(ctx);

        // 过账前校验和数量计算
        transportInComponent.checkAndComputeForModifyStock(ctx);

        // 状态变更-未同步
        transportInComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transportInComponent.post(ctx);

        // 修改库存
        transportInComponent.modifyStock(ctx);

        // 修改标签
        transportInComponent.modifyLabel(ctx);

        // 【先过账模式】状态变更-已记账
        transportInComponent.updateStatusPosted(ctx);

        // 上架推送
        transportInComponent.generateInputTaskReq(ctx);
    }

    /**
     * 上架完成后推送MQ,接收到信息后处理
     */
    @Entrance(call = {"transportInComponent#saveOutputBinByTask", "transportInComponent#updateLoadQty",
        "transportInComponent#updateStatusCompletedByLoadQty", "transportInComponent#checkCanPost", "this#post",
        "transportInComponent#updateStatusCompleted"})
    @WmsMQListener(tags = TagConst.TASK_TRANSPORT_IN_LOAD_CALLBACK)
    public void saveByLoadMq(BizContext ctx) {

        // 根据下架作业单生成bin表
        transportInComponent.saveOutputBinByTask(ctx);

        // 修改item上的已上架数量
        transportInComponent.updateLoadQty(ctx);

        // 【先过账模式】根据已下架数量判断修改单据状态已完成
        transportInComponent.updateStatusCompletedByLoadQty(ctx);

        // 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
        transportInComponent.checkCanPost(ctx);

        // 状态变更-已完成
        transportInComponent.updateStatusCompleted(ctx);
    }

    /**
     * 过账
     */
    @Entrance(call = {"transportInComponent#saveOutputBinByMoveType", "transportInComponent#generateInsDocToPost",
        "transportInComponent#generateNoTaskInsDocToPost", "transportInComponent#generateInsDocToPostByAssemble",
        "transportInComponent#checkAndComputeForModifyStock", "transportInComponent#updateStatusUnsync",
        "transportInComponent#post", "transportInComponent#modifyStock", "transportInComponent#updateStatusCompleted",
        "transportInComponent#updateStatusPosted"})
    public void post(BizContext ctx) {

        // 【先过账模式】根据assemble生成ins凭证
        transportInComponent.generateInsDocToPostByAssemble(ctx);

        // 过账前校验和数量计算
        transportInComponent.checkAndComputeForModifyStock(ctx);

        // 状态变更-未同步
        transportInComponent.updateStatusUnsync(ctx);

        // 调用sap接口过账
        transportInComponent.post(ctx);

        // 修改库存
        transportInComponent.modifyStock(ctx);

        // 状态变更-已完成
        transportInComponent.updateStatusCompleted(ctx);

        // 【先过账模式】状态变更-已记账
        transportInComponent.updateStatusPosted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transportInComponent#delete", "transportInComponent#deleteBizReceiptAttachment"})
    public void delete(BizContext ctx) {

        // 删除
        transportInComponent.delete(ctx);

        // 逻辑删除附件
        transportInComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 冲销
     */
    @Entrance(call = {"transportInComponent#checkWriteOffData", "transportInComponent#generateNoTaskInsDocToPost",
        "transportInComponent#generateInsDocToPostWriteOffByAssemble",
        "transportInComponent#checkAndComputeForModifyStock", "transportInComponent#writeOff",
        "transportInComponent#modifyStock", "transportInComponent#modifyLabel",
        "transportInComponent#addWriteOffRequest", "transportInComponent#modifyTransferableQtyWriteOff",
        "transportInComponent#updateStatusWriteOff"})
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 冲销时校验数据
        transportInComponent.checkWriteOffData(ctx);

        // 【先过账模式】生成ins凭证-冲销
        transportInComponent.generateInsDocToPostWriteOffByAssemble(ctx);

        // 过账前校验和数量计算
        transportInComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账-冲销
        transportInComponent.writeOff(ctx);

        // 修改库存
        transportInComponent.modifyStock(ctx);

        // 修改标签
        transportInComponent.modifyLabel(ctx);

        // 推送冲销修改请求
        transportInComponent.addWriteOffRequest(ctx);

        // 冲销--前置单据为出库单时，回填出库单的“可调拨数量”
        transportInComponent.modifyTransferableQtyWriteOff(ctx);

        // 行项目状态变更-已冲销
        transportInComponent.updateStatusWriteOff(ctx);
    }

    /**
     * 调拨入库-前续单据【调拨申请】
     */
    @Entrance(call = {"transportInComponent#getReferReceiptItemList",
        "transportInComponent#getReferReceiptItemListBySameMode"})
    public void getReferReceiptItemList(BizContext ctx) {

        // 【非同时模式】调拨入库-前续单据【调拨申请】
        transportInComponent.getReferReceiptItemList(ctx);
    }

    /**
     * 调拨入库-前续单据【调拨出库】
     */
    @Entrance(call = {"transportInComponent#getReferReceiptItemListByOut",
        "transportInComponent#getReferReceiptItemListByOutSameMode"})
    public void getReferReceiptItemListByOut(BizContext ctx) {

        // 【非同时模式】调拨入库-前续单据【调拨出库】
        transportInComponent.getReferReceiptItemListByOut(ctx);
    }

}