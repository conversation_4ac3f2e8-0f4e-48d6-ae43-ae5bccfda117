<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.transport.dao.BizReceiptTransportHeadMapper">

    <select id="selectTransportPageVoListByPo" resultType="com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO"
            parameterType="com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO">
        SELECT
        head.*, sys_user.user_name AS create_user_name,dic_move_type.move_type_name,
        case when st_wftask.data_id is null then 1 else 0 end is_review
        FROM
        biz_receipt_transport_head head
        INNER JOIN sys_user ON head.create_user_id = sys_user.id
        INNER JOIN biz_receipt_transport_item ti ON head.id = ti.head_id
        INNER JOIN dic_material dm ON dm.id = ti.output_mat_id
        LEFT JOIN dic_material in_dm ON in_dm.id = ti.input_mat_id
        LEFT JOIN dic_move_type ON dic_move_type.id = head.move_type_id
        left join  st_wftask on head.id=st_wftask.data_id and task_status='活动'
        <where>
            head.receipt_type = #{po.receiptType}  AND head.is_delete = 0  AND sys_user.is_delete = 0
            <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
                AND head.receipt_status in
                <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="po.receiptCode != null and po.receiptCode != '' ">
                AND head.receipt_code like concat( '%',#{po.receiptCode}, '%')
            </if>
            <if test="po.moveTypeId != null and po.moveTypeId != '' ">
                AND head.move_type_id = #{po.moveTypeId}
            </if>
            <if test="po.createUserName != null and po.createUserName != '' ">
                AND sys_user.user_name = #{po.createUserName}
            </if>
            <if test="po.matCode != null and po.matCode != '' ">
                AND dm.mat_code like concat( '%',#{po.matCode}, '%')
            </if>
            <if test="po.matName != null and po.matName != '' ">
                AND dm.mat_name like concat( '%',#{po.matName}, '%')
            </if>
            <if test="po.inputMatCode != null and po.inputMatCode != '' ">
                AND in_dm.mat_code = #{po.inputMatCode}
            </if>
            <if test="po.des != null and po.des != '' ">
                AND head.remark like concat( '%',#{po.des}, '%')
            </if>
            <if test="po.matDocCode != null and po.matDocCode != '' ">
                AND ti.mat_doc_code = #{po.matDocCode}
            </if>
            <if test="po.postingStartDate != null and po.postingEndDate != null">
                AND DATE(ti.posting_date)
                BETWEEN #{po.postingStartDate, jdbcType=TIMESTAMP}
                    AND DATE_ADD(#{po.postingEndDate, jdbcType=TIMESTAMP},INTERVAL 1 DAY)
            </if>
            <if test="po.createStartTime != null and po.createEndTime != null">
                AND DATE(head.create_time)
                BETWEEN #{po.createStartTime, jdbcType=TIMESTAMP}
                    AND DATE_ADD(#{po.createEndTime, jdbcType=TIMESTAMP},INTERVAL 1 DAY)
            </if>
            <if test="po.isReview != null  ">
                AND case when st_wftask.data_id is null then 1 else 0 end  = #{po.isReview}
            </if>
            <if test="po.locationIdList != null and po.locationIdList.size() > 0">
                AND ti.output_location_id in
                <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by head.id
        order by head.create_time desc
    </select>




    <select id="selectTransportPageVoListByPoUnitized" resultType="com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO"
            parameterType="com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO">
        SELECT
        head.*, sys_user.user_name AS create_user_name,dic_move_type.move_type_name
        FROM
        biz_receipt_transport_head head
        INNER JOIN sys_user ON head.create_user_id = sys_user.id
        INNER JOIN biz_receipt_transport_item ti ON head.id = ti.head_id
        INNER JOIN dic_material dm ON dm.id = ti.output_mat_id
        LEFT JOIN  dic_material pdm on pdm.id = dm.parent_mat_id
        LEFT JOIN dic_move_type ON dic_move_type.id = head.move_type_id
        <where>
            head.receipt_type = #{po.receiptType}  AND head.is_delete = 0  AND sys_user.is_delete = 0
            <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
                AND head.receipt_status in
                <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="po.receiptCode != null and po.receiptCode != '' ">
                AND head.receipt_code = #{po.receiptCode}
            </if>
            <if test="po.moveTypeId != null and po.moveTypeId != '' ">
                AND head.move_type_id = #{po.moveTypeId}
            </if>
            <if test="po.createUserName != null and po.createUserName != '' ">
                AND sys_user.user_name = #{po.createUserName}
            </if>
            <if test="po.childMatCode != null and po.childMatCode != '' ">
                AND dm.mat_code = #{po.childMatCode}
            </if>
            <if test="po.matCode != null and po.matCode != '' ">
                AND pdm.mat_code = #{po.matCode}
            </if>
            <if test="po.des != null and po.des != '' ">
                AND head.remark like concat( '%',#{po.des}, '%')
            </if>
            <if test="po.createStartTime != null and po.createEndTime != null">
                AND DATE(head.create_time)
                BETWEEN #{po.createStartTime, jdbcType=TIMESTAMP} AND DATE_ADD(#{po.createEndTime, jdbcType=TIMESTAMP},INTERVAL
                1 DAY)
            </if>
            <if test="po.locationIdList != null and po.locationIdList.size() > 0">
                AND ti.output_location_id in
                <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by head.id
        order by head.create_time desc
    </select>


    <select id="getLeisurePageVOList" resultType="com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO">
        SELECT
        head.*, sys_user.user_name AS create_user_name
        FROM
        biz_receipt_transport_head head
        INNER JOIN biz_receipt_transport_item item ON item.head_id = head.id
        INNER JOIN sys_user ON head.create_user_id = sys_user.id
        INNER JOIN dic_material dm ON item.input_mat_id = dm.id AND dm.is_delete = 0
        INNER JOIN biz_receipt_apply_head appHead ON appHead.id = head.pre_receipt_head_id
        INNER JOIN biz_receipt_apply_item appItem ON appItem.id = item.pre_receipt_item_id
        <where>
            head.receipt_type = #{po.receiptType}
            AND item.is_delete = 0
            AND head.is_delete = 0
            AND sys_user.is_delete = 0
            <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
                AND head.receipt_status in
                <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="po.receiptCode != null and po.receiptCode != '' ">
                AND head.receipt_code = #{po.receiptCode}
            </if>
            <if test="po.preReceiptCode != null and po.preReceiptCode != '' ">
                AND appHead.receipt_code = #{po.preReceiptCode}
            </if>
            <if test="po.matName != null and po.matName != '' ">
                AND dm.mat_name LIKE concat( '%',#{po.matName}, '%')
            </if>
            <if test="po.matCode != null and po.matCode != '' ">
                AND dm.mat_code = #{po.matCode}
            </if>
            <if test="po.createUserName != null and po.createUserName != '' ">
                AND sys_user.user_name = #{po.createUserName}
            </if>
            <if test="po.createStartTime != null and po.createEndTime != null">
                AND DATE(head.create_time)
                BETWEEN #{po.createStartTime, jdbcType=TIMESTAMP} AND DATE_ADD(#{po.createEndTime, jdbcType=TIMESTAMP},INTERVAL
                1 DAY)
            </if>
            <if test="po.locationIdList != null and po.locationIdList.size() > 0">
                AND item.output_location_id in
                <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by head.id
        order by head.create_time desc
    </select>
</mapper>
