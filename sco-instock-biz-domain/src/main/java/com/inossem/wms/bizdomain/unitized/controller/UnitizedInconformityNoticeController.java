package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedInconformityNoticeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.po.BizReceiptInconformitySearchPO;
import com.inossem.wms.common.model.bizdomain.inconformity.vo.BizReceiptInconformityPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.enums.inconformity.DifferentTypeMapVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 成套设备不符合项通知 前端控制器
 * </p>
 */
@RestController
public class UnitizedInconformityNoticeController {

    @Autowired
    protected UnitizedInconformityNoticeService unitizedInconformityNoticeService;

    /**
     * 初始化
     */
    @ApiOperation(value = "初始化", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-notice/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> init(BizContext ctx) {
        unitizedInconformityNoticeService.init(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询差异类型下拉
     *
     * @return 差异类型下拉框
     */
    @ApiOperation(value = "查询差异类型下拉", tags = {"验收管理-不符合项通知"})
    @GetMapping(path = "/unitized/inconformity/inconformity-notice/different-type-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DifferentTypeMapVO>> getDifferentTypeDown(BizContext ctx) {
        unitizedInconformityNoticeService.getDifferentTypeDown(ctx);
        MultiResultVO<DifferentTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询不符合项通知列表-分页
     *
     * @param po 分页查询入参
     * @return 单据列表
     */
    @ApiOperation(value = "查询不符合项通知列表-分页", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-notice/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInconformityPageVO>> getPage(@RequestBody BizReceiptInconformitySearchPO po, BizContext ctx) {
        unitizedInconformityNoticeService.getPage(ctx);
        PageObjectVO<BizReceiptInconformityPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询不符合项通知详情
     *
     * @param id 不符合项通知抬头表主键
     * @return 不符合项通知详情
     */
    @ApiOperation(value = "查询不符合项通知详情", tags = {"验收管理-不符合项通知"})
    @GetMapping(value = "/unitized/inconformity/inconformity-notice/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedInconformityNoticeService.getInfo(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 不符合项通知-保存
     *
     * @param po 保存不符合项通知表单参数;l
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项通知-保存", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-notice/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNoticeService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 不符合项通知-提交
     *
     * @param po 提交不符合项通知表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项通知-提交", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-notice/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNoticeService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 不符合项通知-过账
     *
     * @param po 提交不符合项通知表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "不符合项通知-过账", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-notice/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult post(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNoticeService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS, po.getReceiptCode());
    }

    /**
     * 不符合项通知-前续单据
     *
     * @param po 查询条件
     * @return 质检会签单
     */
    @ApiOperation(value = "不符合项通知-前续单据", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-notice/pre-receipt", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> getPreReceipt(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        unitizedInconformityNoticeService.getPreReceipt(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 不符合项通知-前续单据
     *
     * @param po 查询条件
     * @return 质检会签单
     */
    @ApiOperation(value = "不符合项通知-前续单据", tags = {"验收管理-不符合项通知"})
    @PostMapping(value = "/unitized/inconformity/inconformity-notice/fillEntity", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInconformityHeadDTO>> fillEntity(@RequestBody BizReceiptInconformityHeadDTO po, BizContext ctx) {
        unitizedInconformityNoticeService.fillEntity(ctx);
        BizResultVO<BizReceiptInconformityHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}

