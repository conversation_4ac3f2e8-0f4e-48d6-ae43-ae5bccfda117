package com.inossem.wms.bizdomain.transport.service.datawrap;

import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportApplyHead;
import com.inossem.wms.bizdomain.transport.dao.BizReceiptTransportApplyHeadMapper;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 调拨申请单抬头表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-06
 */
@Service
public class BizReceiptTransportApplyHeadDataWrap extends BaseDataWrap<BizReceiptTransportApplyHeadMapper, BizReceiptTransportApplyHead> {

}
