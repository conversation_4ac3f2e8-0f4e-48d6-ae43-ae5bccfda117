package com.inossem.wms.bizdomain.transport.service.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.inossem.wms.bizdomain.transport.service.component.TransportMatComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.common.base.BizContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 物料转码
 */

@Service
@Slf4j
public class TransportMatService {

    @Autowired
    @Lazy
    private TransportMatComponent transportMatComponent;




    /**
     * 移动类型列表
     */
    @Entrance(call = {"transportComponent#getMoveTypeList"})
    public void getMoveTypeList(BizContext ctx) {

        // 移动类型列表
        transportMatComponent.getMoveTypeList(ctx);
    }

    /**
     * 页面初始化
     */
    @Entrance(call = {"transportComponent#init", "transportComponent#setExtendWf",
        "transportComponent#setExtendAttachment", "transportComponent#setExtendOperationLog"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportMatComponent.init(ctx);

        // 开启审批
        //transportMatComponent.setExtendWf(ctx);

        // 开启附件
        transportMatComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportMatComponent.setExtendOperationLog(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transportComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportMatComponent.getStock(ctx);
    }

    /**
     * 转储物料导入
     */
    @Transactional(rollbackFor = Exception.class)
    public void importMaterial(BizContext ctx) {
        // 查询库存
        transportMatComponent.importItem(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transportComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportMatComponent.getPage(ctx);
    }

    /**
     * 获取盘点人列表
     *
     * @return 用户列表
     */
    @Entrance(call = {"stocktakingComponent#setUserList"})
    public void getUserList(BizContext ctx) {

        // 查询盘点人列表
        transportMatComponent.setUserList(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transportComponent#getInfo", "transportComponent#setExtendWf",
        "transportComponent#setExtendAttachment", "transportComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 查询单据详情,包含按钮组和扩展功能
        transportMatComponent.getInfo(ctx);

        // 开启审批
        transportMatComponent.setExtendWf(ctx);

        // 开启附件
        transportMatComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportMatComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transportComponent#checkSaveData", "transportComponent#setAssembleInputBatchId",
        "transportComponent#save", "transportComponent#saveBizReceiptAttachment",
        "transportComponent#saveBizReceiptOperationLog"})
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存时校验数据
        transportMatComponent.checkSaveData(ctx);

        // 【先过账模式】设置批次id-用于生成接收方批次
        transportMatComponent.setAssembleInputBatchId(ctx);

        // 保存
        transportMatComponent.save(ctx);

        // 保存附件
        transportMatComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportMatComponent.saveBizReceiptOperationLog(ctx);
    }

    /**
     * 提交时逻辑处理
     */
    @Entrance(call = {"transportComponent#checkSubmitData", "transportComponent#setAssembleInputBatchId",
        "transportComponent#submit", "transportComponent#occupyStock", "transportComponent#saveBizReceiptAttachment",
        "transportComponent#saveBizReceiptOperationLog", "transportComponent#updateStatusSubmitted",
        "transportComponent#generateInsDocToPost", "transportComponent#generateInsDocToPostByAssemble",
        "transportComponent#checkAndComputeForModifyStock", "transportComponent#post", "transportComponent#modifyStock",
        "transportComponent#modifyLabel", "transportComponent#updateStatusPosted",
        "transportComponent#updateStatusCompleted", "transportComponent#generateOutputTaskReq",
        "transportComponent#saveOutputBinByMoveType", "transportComponent#setMoveTypeListCache"})
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 提交时校验数据
        transportMatComponent.checkSubmitData(ctx);

        // 【先过账模式】设置批次id-用于生成接收方批次
        transportMatComponent.setAssembleInputBatchId(ctx);

        // 提交
        transportMatComponent.submit(ctx);

        // 保存附件
        transportMatComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        transportMatComponent.saveBizReceiptOperationLog(ctx);

        //快速模式 生成bin表
        transportMatComponent.saveOutputBinQuickModel(ctx);

        // 发起审批
        transportMatComponent.startWorkFlow(ctx);


//        // 生成ins凭证 - 转储接收发出一次过账
//        transportMatComponent.generateInsDocToPostQuickModel(ctx);
//
//        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
//        transportMatComponent.checkAndComputeForModifyStock(ctx);
//
//        // 【同时模式-提交】【先过账模式】调用sap接口过账
//        transportMatComponent.post(ctx);
//
//        // 【同时模式-提交】【先过账模式】修改库存
//        transportMatComponent.modifyStock(ctx);
//
//        // 修改标签
//        transportMatComponent.modifyLabel(ctx);
//
//        // 【先过账模式】状态变更-已完成
//        transportMatComponent.updateStatusCompleted(ctx);

    }

    /**
     * 过账
     */
    @Entrance(call = {"transportComponent#generateInsDocToPost", "transportComponent#generateInsDocToPostByAssemble",
        "transportComponent#checkAndComputeForModifyStock", "transportComponent#updateStatusUnsync",
        "transportComponent#post", "transportComponent#modifyStock", "transportComponent#deleteOccupyStock",
        "transportComponent#updateStatusCompleted", "transportComponent#updateStatusPosted",
        "transportComponent#setMoveTypeListCache"})
    public void post(BizContext ctx) {

        // 生成ins凭证 - 转储接收发出一次过账
        transportMatComponent.generateInsDocToPostQuickModel(ctx);

        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
        transportMatComponent.checkAndComputeForModifyStock(ctx);

        // 状态变更-未同步
        transportMatComponent.updateStatusUnsync(ctx);

        // 【同时模式-提交】【先过账模式】调用sap接口过账
        transportMatComponent.processPost(ctx);

        // 【同时模式-提交】【先过账模式】修改库存
        transportMatComponent.modifyStock(ctx);

        // 修改标签
        transportMatComponent.modifyLabel(ctx);

        // 状态变更-已完成
        transportMatComponent.updateStatusCompleted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transportComponent#checkTaskStatus", "transportComponent#delete",
        "transportComponent#deleteOccupyStock", "transportComponent#deleteBizReceiptAttachment",
        "transportComponent#cancelTaskRequest"})
    public void delete(BizContext ctx) {

        // 删除
        transportMatComponent.delete(ctx);

        // 逻辑删除附件
        transportMatComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 冲销
     */
    @Entrance(call = {"transportComponent#checkWriteOffData", "transportComponent#generateInsDocToPostWriteOff",
        "transportComponent#generateInsDocToPostWriteOffByAssemble", "transportComponent#checkAndComputeForModifyStock",
        "transportComponent#writeOff", "transportComponent#modifyStock", "transportComponent#modifyLabel",
        "transportComponent#updateStatusWriteOff", "transportComponent#saveBizReceiptOperationLog",
        "transportComponent#addWriteOffRequest", "transportComponent#setMoveTypeListCache"})
    public void writeOff(BizContext ctx) {

        // 冲销时校验数据
        transportMatComponent.checkWriteOffData(ctx);

        // 【先过账模式】生成ins凭证-冲销
        transportMatComponent.generateInsDocToPostWriteOffByAssemble(ctx);

        // 【同时模式-提交】【先过账模式】过账前校验和数量计算
        transportMatComponent.checkAndComputeForModifyStock(ctx);

        // 调用sap接口过账-冲销
        transportMatComponent.writeOff(ctx);

        // 【同时模式-提交】【先过账模式】修改库存
        transportMatComponent.modifyStock(ctx);

        // 修改标签
        transportMatComponent.modifyLabel(ctx);

        // 行项目状态变更-已冲销
        transportMatComponent.updateStatusWriteOff(ctx);

        // 保存操作日志
        //transportComponent.saveBizReceiptOperationLog(ctx);

        // 【先过帐模式】推送冲销修改请求
        transportMatComponent.addWriteOffRequest(ctx);
    }

    /**
     * 过门后推送MQ
     */
    @Entrance(call = {"transportComponent#updateFinishQty", "transportComponent#checkCanPost",
        "transportComponent#generatePalletSorting"})
    public void saveUnloadByPassDoorMq(BizContext ctx) {

        // 修改item上的发完成数量
        transportMatComponent.updateFinishQty(ctx);

        // 判断单据是否可以过账 普通标签全部作业 rfid标签全部过门
        transportMatComponent.checkCanPost(ctx);

        // 生成接收方码盘数据,关联表
        transportMatComponent.generatePalletSorting(ctx);
    }

    /**
     * 获取WBS集合
     */
    @Entrance(call = {"transportComponent#getWbsList"})
    public void getWbsList(BizContext ctx) {

        // 获取WBS集合
        transportMatComponent.getWbsList(ctx);
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo BizApprovalReceiptInstanceRelDTO
     */
    @WmsMQListener(tags = TagConst.APPROVAL_TRANSPORT_MAT_APPLY)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        if (EnumApprovalStatus.FINISH.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 更新未同步
            BizContext ctx = new BizContext();
            BizReceiptTransportHeadDTO headDTO = new BizReceiptTransportHeadDTO();
            headDTO.setId(wfReceiptCo.getReceiptHeadId());
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO,headDTO);
            transportMatComponent.updateStatusUnsync(ctx);
            // 审批通过
            transportMatComponent.doTransPortPost(wfReceiptCo);
        } else if (EnumApprovalStatus.REJECT.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批拒绝
            transportMatComponent.updateStatusOnTransactional(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }

    }
    /**
     * 转码-阅知
     */
    @Transactional(rollbackFor = Exception.class)
    public void review(BizContext ctx) {
        transportMatComponent.completeUmsTask(ctx);
    }

    /**
     * 审批回调手动调用
     */
//    @Transactional(rollbackFor = Exception.class)
    public void manualApprovalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo, BizContext ctx) {
        wfReceiptCo.setInitiator(ctx.getCurrentUser());
        if (EnumApprovalStatus.FINISH.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 更新未同步
            BizContext ctx1 = new BizContext();
            BizReceiptTransportHeadDTO headDTO = new BizReceiptTransportHeadDTO();
            headDTO.setId(wfReceiptCo.getReceiptHeadId());
            ctx1.setContextData(Const.BIZ_CONTEXT_KEY_PO,headDTO);
            transportMatComponent.updateStatusUnsync(ctx1);
            // 审批通过
            transportMatComponent.doTransPortPost(wfReceiptCo);
        } else if (EnumApprovalStatus.REJECT.getValue().equals(wfReceiptCo.getApproveStatus())) {
            // 审批拒绝
            transportMatComponent.updateStatusOnTransactional(wfReceiptCo.getReceiptHeadId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }

    }
}