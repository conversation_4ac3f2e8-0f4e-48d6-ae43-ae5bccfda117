package com.inossem.wms.bizdomain.transport.dao;

import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportApplyItem;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Delete;

/**
 * <p>
 * 调拨申请单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-06
 */
public interface BizReceiptTransportApplyItemMapper extends WmsBaseMapper<BizReceiptTransportApplyItem> {

    /**
     * item物理删除
     *
     * @param headId 主表主键
     */
    @Delete("DELETE FROM biz_receipt_transport_apply_item WHERE head_id = #{headId}")
    void deleteByHeadId(Long headId);
}
