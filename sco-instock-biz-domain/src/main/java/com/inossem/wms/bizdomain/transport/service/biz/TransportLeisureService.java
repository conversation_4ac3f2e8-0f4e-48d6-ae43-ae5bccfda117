package com.inossem.wms.bizdomain.transport.service.biz;

import com.inossem.wms.bizdomain.transport.service.component.TransportApplyComponent;
import com.inossem.wms.bizdomain.transport.service.component.TransportLeisureComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 闲置转库
 *
 * <AUTHOR>
 */

@Service
@Slf4j
@Deprecated
public class TransportLeisureService {

    @Autowired
    private TransportLeisureComponent transportLeisureComponent;

    /**
     * 页面初始化
     */
    @Entrance(call = {"transportApplyComponent#init", "transportApplyComponent#setExtendWf",
        "transportApplyComponent#setExtendAttachment", "transportApplyComponent#setExtendOperationLog",
        "transportApplyComponent#setExtendRelation"})
    public void init(BizContext ctx) {

        // 页面初始化
        transportLeisureComponent.init(ctx);

        // 开启审批
        transportLeisureComponent.setExtendWf(ctx);

        // 开启附件
        transportLeisureComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportLeisureComponent.setExtendOperationLog(ctx);

        // 开启单据流
        transportLeisureComponent.setExtendRelation(ctx);
    }

    /**
     * 查询库存
     */
    @Entrance(call = {"transportApplyComponent#getStock"})
    public void getStock(BizContext ctx) {

        // 查询库存
        transportLeisureComponent.getStock(ctx);
    }

    /**
     * 列表 - 分页
     */
    @Entrance(call = {"transportApplyComponent#getPage"})
    public void getPage(BizContext ctx) {

        // 列表 - 分页
        transportLeisureComponent.getPage(ctx);
    }

    /**
     * 列表 - 没有分页
     */
    @Entrance(call = {"transportApplyComponent#getList"})
    public void getList(BizContext ctx) {

        // 列表 - 没有分页
        transportLeisureComponent.getList(ctx);
    }

    /**
     * 详情
     */
    @Entrance(call = {"transportApplyComponent#getInfo", "transportApplyComponent#setExtendAttachment",
        "transportApplyComponent#setExtendOperationLog"})
    public void getInfo(BizContext ctx) {

        // 详情
        transportLeisureComponent.getInfo(ctx);

        // 开启附件
        transportLeisureComponent.setExtendAttachment(ctx);

        // 开启操作日志
        transportLeisureComponent.setExtendOperationLog(ctx);
    }

    /**
     * 保存
     */
    @Entrance(call = {"transportApplyComponent#check", "transportApplyComponent#save",
        "transportApplyComponent#saveBizReceiptAttachment"})
    public void save(BizContext ctx) {

        // 校验数据
        transportLeisureComponent.check(ctx);

        // 保存
        transportLeisureComponent.save(ctx);

        // 保存附件
        transportLeisureComponent.saveBizReceiptAttachment(ctx);
    }

    /**
     * 提交
     */
    @Entrance(call = {"transportApplyComponent#check", "transportApplyComponent#submit",
        "transportApplyComponent#saveBizReceiptAttachment", "transportApplyComponent#updateStatusSubmitted"})
    public void submit(BizContext ctx) {

        // 校验数据
        transportLeisureComponent.check(ctx);

        // 提交
        transportLeisureComponent.submit(ctx);

        // 保存附件
        transportLeisureComponent.saveBizReceiptAttachment(ctx);

        // 状态变更已完成
        transportLeisureComponent.updateStatusCompleted(ctx);
    }

    /**
     * 删除
     */
    @Entrance(call = {"transportApplyComponent#delete", "transportApplyComponent#deleteBizReceiptAttachment"})
    public void delete(BizContext ctx) {

        // 删除
        transportLeisureComponent.delete(ctx);

        // 逻辑删除附件
        transportLeisureComponent.deleteBizReceiptAttachment(ctx);
    }

    /**
     * 闲置物资转库打印
     * @param ctx
     */
    public void boxApplyLabelPrint(BizContext ctx) {
        // 打印物料标签校验
        transportLeisureComponent.checkPrint(ctx);
        // 填充打印数据
        transportLeisureComponent.fillPrintData(ctx);
    }
}