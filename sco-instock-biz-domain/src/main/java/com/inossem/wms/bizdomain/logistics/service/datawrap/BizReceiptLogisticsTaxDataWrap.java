package com.inossem.wms.bizdomain.logistics.service.datawrap;

import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsTax;
import com.inossem.wms.bizdomain.logistics.dao.BizReceiptLogisticsTaxMapper;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 物流清关费用税费表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20
 */
@Service
public class BizReceiptLogisticsTaxDataWrap extends BaseDataWrap<BizReceiptLogisticsTaxMapper, BizReceiptLogisticsTax> {

}
