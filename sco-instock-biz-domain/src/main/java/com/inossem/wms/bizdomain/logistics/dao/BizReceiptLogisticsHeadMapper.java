package com.inossem.wms.bizdomain.logistics.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsHead;
import com.inossem.wms.common.model.bizdomain.logistics.po.BizReceiptLogisticsSearchPO;
import com.inossem.wms.common.model.bizdomain.logistics.vo.BizReceiptLogisticsListVo;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物流清关费用抬头表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
public interface BizReceiptLogisticsHeadMapper extends WmsBaseMapper<BizReceiptLogisticsHead> {


    /**
     * 物流清关费用单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 物流清关费用单
     */
    List<BizReceiptLogisticsListVo> selectLogisticsPageVo(IPage<BizReceiptLogisticsListVo> pageData,
                                                               @Param(Constants.WRAPPER) WmsQueryWrapper<BizReceiptLogisticsSearchPO> pageWrapper);

    /**
     * 物流清关费用单 - 分页
     *
     * @param pageData    分页数据
     * @param pageWrapper 分页查新条件
     * @return 物流清关费用单
     */
    List<BizReceiptLogisticsListVo> selectLogisticsPageVoUnitized(IPage<BizReceiptLogisticsListVo> pageData,
                                                                       @Param(Constants.WRAPPER) WmsQueryWrapper<BizReceiptLogisticsSearchPO> pageWrapper);



    List<BizReceiptLogisticsHead> selectLogistics(BizReceiptLogisticsSearchPO po);

}
