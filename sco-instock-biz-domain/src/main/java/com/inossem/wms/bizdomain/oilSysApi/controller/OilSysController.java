package com.inossem.wms.bizdomain.oilSysApi.controller;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.bizdomain.oilSysApi.service.biz.OilSysService;
import com.inossem.wms.common.model.common.base.BizContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 加油站平台对接 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@RestController
@Api(tags = "加油站平台")
public class OilSysController {

    @Autowired
    protected OilSysService oilSysService;

    @ApiOperation(value = "新增出库", tags = {"加油站平台-接口2"})
    @PostMapping(value = "/oilSys/saveOutput", produces = MediaType.APPLICATION_JSON_VALUE)
    public JSONObject saveOutput(@RequestBody String po, BizContext ctx) {
        return oilSysService.saveOutput(ctx);
    }

    @ApiOperation(value = "新增仓位整理", tags = {"加油站平台-接口4"})
    @PostMapping(value = "/oilSys/saveBinArrange", produces = MediaType.APPLICATION_JSON_VALUE)
    public JSONObject saveBinArrange(@RequestBody String po, BizContext ctx) {
        return oilSysService.saveBinArrange(ctx);
    }

    @ApiOperation(value = "库存查询", tags = {"加油站平台-接口5"})
    @PostMapping(value = "/oilSys/getMatBinStock", produces = MediaType.APPLICATION_JSON_VALUE)
    public JSONObject getMatBinStock(@RequestBody String po, BizContext ctx) {
        return oilSysService.getMatBinStock(ctx);
    }
}

