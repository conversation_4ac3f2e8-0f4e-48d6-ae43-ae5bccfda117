package com.inossem.wms.bizdomain.unitized.controller;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.bizbasis.erp.service.biz.ErpWbsService;
import com.inossem.wms.bizdomain.unitized.po.WbsQueryPO;
import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedMaterialOrderOutApplyService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyBinDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptApplyQueryListPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptApplyPageVO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizMatApplyLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.erp.entity.SapWbs;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySearchPO;
import com.inossem.wms.common.model.masterdata.mat.fty.vo.DicMaterialFactoryPageVO;
import com.inossem.wms.common.model.print.label.LabelReceiptRegisterBox;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR> wang
 * <p>成套领料出库单申请</p>
 * @date 2022/4/24 13:24
 */
@RestController
public class UnitizedMaterialOrderOutApplyController {
    // TODO-BO: 2022/5/12 成套领料出库单申请保存提交国际化

    @Autowired
    private UnitizedMaterialOrderOutApplyService unitizedMaterialOrderOutApplyService;
    @Autowired
    private ErpWbsService erpWbsService;

    @ApiOperation(value = "成套领料出库单申请初始化-sdw", tags = {"出库管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/init")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> init(BizContext ctx) {
        unitizedMaterialOrderOutApplyService.init(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    @ApiOperation(value = "成套领料出库单申请查询列表（分页）-sdw", tags = {"出库管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/results")
    public BaseResult<PageObjectVO<BizReceiptApplyPageVO>> getPage(@RequestBody BizReceiptApplyQueryListPO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getPage(ctx);
        PageObjectVO<BizReceiptApplyPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "成套领料出库单申请详情-sdw", tags = {"出库管理-领料出库apply"})
    @GetMapping(value = "/outputs/unitized-mat-order/req/{id}")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getInfo(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "成套领料出库单申请提交-sdw", tags = {"出库管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/submit")
    public BaseResult submit(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "成套领料出库单申请获取配货信息", tags = {"出库管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }


    @ApiOperation(value = "成套领料出库单申请单查询物料库存", tags = {"出库管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStock(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getMatStock(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @ApiOperation(value = "成套领料出库单申请删除", tags = {"出库管理-领料出库apply"})
    @DeleteMapping(value = "/outputs/unitized-mat-order/req/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.delete(ctx);
        BizReceiptApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_DELETE_SUCCESS, headDTO.getReceiptCode());
    }


    /**
     * 领料申请单打印
     *
     * @param po  打印入参
     * @param ctx 请求上下文
     * @return 领料申请单数据
     */
    @ApiOperation(value = "领料申请单打印", tags = {"出库管理-领料出库"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/ox-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizMatApplyLabelPrintPO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.boxApplyLabelPrint(ctx);
        List<LabelReceiptRegisterBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

    /**
     * 调试sap接口用
     */
    @ApiOperation(value = "领料申请单打印", tags = {"出库管理-领料出库"})
    @PostMapping(value = "/test-sap-interface")
    public BaseResult<?> testSAPInterface(@RequestBody JSONObject po, BizContext ctx) {
        JSONObject returnJson = unitizedMaterialOrderOutApplyService.testSAPInterface(po);
        return BaseResult.success(new SingleResultVO<>(returnJson));
    }

    @ApiOperation(value = "成套领料出库单申请提交-sdw", tags = {"出库管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/save")
    public BaseResult saveNew(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.saveNew(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SAVE_SUCCESS, code);
    }

    /**
     * 成套领料申请合并转性功能
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "成套成套领料申请单申请提交-sdw", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/submit-new")
    public BaseResult submitNew(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.submitNew(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "成套领料申请单创建预留单-sdw", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/sync-new")
    public BaseResult syncNew(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.syncNew(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "成套领料申请单查询wbs-sdw", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/wbs")
    public BaseResult wbs(@RequestBody WbsQueryPO po, BizContext ctx) {
        List<SapWbs> dtoList = erpWbsService.findWBS(po.getFuzzyParam(), po.getWbsCode(), po.getFtyCode());
        return BaseResult.success(dtoList);
    }

    @ApiOperation(value = "成套领料出库单申请单查询物料库存", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/mat-stock/list-new")
    public BaseResult<MatStockDTO> getMatStockNew(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getMatStockNew(ctx);
        MatStockDTO matStockDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(matStockDTO);
    }

    @GetMapping(value = "/outputs/unitized-mat-order/req/wbs-manual")
    public BaseResult manualSyncWbs(@RequestParam String startDate, @RequestParam String endDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            erpWbsService.syncWBS(formatter.parse(startDate), formatter.parse(endDate));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResult.success();
    }

    @PostMapping(value = "/outputs/unitized-mat-order/req/mat-stock/list-main")
    public BaseResult<PageObjectVO<DicMaterialFactoryPageVO>> getMatMain(@RequestBody DicMaterialFactorySearchPO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getMatMain(ctx);
        PageObjectVO<DicMaterialFactoryPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "成套设备-需求库存信息自动匹配", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/stock")
    public BaseResult<MultiResultVO<BizReceiptApplyBinDTO>> getStock(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getStock(ctx);
        MultiResultVO<BizReceiptApplyBinDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "成套设备-领料申请-初始化", tags = {"成套设备管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/initByRequire")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> initByRequire(BizContext ctx) {
        unitizedMaterialOrderOutApplyService.initByRequire(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "成套设备-领料申请-保存", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/saveByRequire")
    public BaseResult saveByRequire(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.saveByRequire(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "成套设备-领料申请-提交", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/submitByRequire")
    public BaseResult submitByRequire(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.submitByRequire(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "成套设备-领料申请-过账", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/postByRequire")
    public BaseResult postByRequire(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.postByRequire(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "成套-拆分申请-查询领用申请分页", tags = {"出库管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/results-apply")
    public BaseResult<PageObjectVO<BizReceiptApplyHeadDTO>> getPageByApply(@RequestBody BizReceiptApplyQueryListPO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.getPageByApply(ctx);
        PageObjectVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "成套设备-领料申请-初始化", tags = {"成套设备管理-领料出库apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/initBySpilt")
    public BaseResult<BizResultVO<BizReceiptApplyHeadDTO>> initBySpilt(BizContext ctx) {
        unitizedMaterialOrderOutApplyService.initBySpilt(ctx);
        BizResultVO<BizReceiptApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "成套设备-领料申请-提交", tags = {"成套设备管理-领料申请apply"})
    @PostMapping(value = "/outputs/unitized-mat-order/req/submitBySpilt")
    public BaseResult submitBySpilt(@RequestBody BizReceiptApplyHeadDTO po, BizContext ctx) {
        unitizedMaterialOrderOutApplyService.submitBySpilt(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_APPLY_SUBMIT_SUCCESS, code);
    }
}
