package com.inossem.wms.bizdomain.transport.dao;

import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.vo.BizReceiptTransportExportVO;
import org.apache.ibatis.annotations.Delete;

import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 转储单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-22
 */
public interface BizReceiptTransportItemMapper extends WmsBaseMapper<BizReceiptTransportItem> {

    /**
     * item物理删除
     *
     * @param headId 主表主键
     */
    @Delete("DELETE FROM biz_receipt_transport_item WHERE head_id = #{headId}")
    void deleteByHeadId(Long headId);

    List<BizReceiptTransportExportVO> selectExportItemList(@Param("po") BizReceiptTransportHeadSearchPO po);
}
