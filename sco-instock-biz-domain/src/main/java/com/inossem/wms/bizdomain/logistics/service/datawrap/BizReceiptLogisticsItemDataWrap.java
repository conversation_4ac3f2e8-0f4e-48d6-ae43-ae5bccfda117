package com.inossem.wms.bizdomain.logistics.service.datawrap;

import com.inossem.wms.bizdomain.logistics.dao.BizReceiptLogisticsItemMapper;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsItem;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 物流清关费用行项目表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Service
public class BizReceiptLogisticsItemDataWrap extends BaseDataWrap<BizReceiptLogisticsItemMapper, BizReceiptLogisticsItem> {

}
