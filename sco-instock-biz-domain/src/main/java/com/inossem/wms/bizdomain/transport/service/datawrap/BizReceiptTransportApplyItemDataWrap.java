package com.inossem.wms.bizdomain.transport.service.datawrap;

import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportApplyItem;
import com.inossem.wms.bizdomain.transport.dao.BizReceiptTransportApplyItemMapper;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 调拨申请单明细表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-06
 */
@Service
public class BizReceiptTransportApplyItemDataWrap extends BaseDataWrap<BizReceiptTransportApplyItemMapper, BizReceiptTransportApplyItem> {

    public void deleteByHeadId(Long headId) {
        this.getBaseMapper().deleteByHeadId(headId);
    }
}
