package com.inossem.wms.bizdomain.contract.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialGroupDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizdomain.contract.service.datawrap.*;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.purchase.service.datawrap.BizReceiptPurchaseApplyItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.contract.EnumContractFirstParty;
import com.inossem.wms.common.enums.contract.EnumContractPaymentNode;
import com.inossem.wms.common.enums.purchase.EnumPurchaseType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.contract.dto.*;
import com.inossem.wms.common.model.bizdomain.contract.entity.*;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractReceivingSearchPO;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractReceivingPageVO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanItem;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.sap.posting.HXPostingHeader;
import com.inossem.wms.common.model.sap.posting.HXPostingItem;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/29
 */
@Service
@Slf4j
public class ContractReceivingComponent {


    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private BizReceiptContractReceivingHeadDataWrap bizReceiptContractReceivingHeadDataWrap;

    @Autowired
    private BizReceiptContractReceivingItemDataWrap bizReceiptContractReceivingItemDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap bizReceiptDeliveryNoticeHeadDataWrap;

    @Autowired
    private BizReceiptPaymentPlanHeadDataWrap bizReceiptPaymentPlanHeadDataWrap;

    @Autowired
    private BizReceiptPaymentPlanItemDataWrap bizReceiptPaymentPlanItemDataWrap;

    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;

    @Autowired
    private BizReceiptContractItemDataWrap bizReceiptContractItemDataWrap;

    @Autowired
    private HXSapIntegerfaceService hxInterfaceService;

    @Autowired
    private BizReceiptPurchaseApplyItemDataWrap bizReceiptPurchaseApplyItemDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private BizReceiptContractSubItemDataWrap bizReceiptContractSubItemDataWrap;
    @Autowired
    private DicMaterialGroupDataWrap dicMaterialGroupDataWrap;
    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap bizReceiptDeliveryNoticeItemDataWrap;


    /**
     * 页面初始化:
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptContractReceivingHeadDTO().setReceiptType(EnumReceiptType.CONTRACT_RECEIVING.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 设置单据流
     *
     * @param ctx 上下文
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                    .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptContractReceivingSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptContractReceivingSearchPO> wrapper = this.setQueryWrapper(po, user);
        // 分页处理
        IPage<BizReceiptContractReceivingPageVO> page = po.getPageObj(BizReceiptContractReceivingPageVO.class);

        bizReceiptContractReceivingHeadDataWrap.selectPage(page, wrapper);

        List<BizReceiptContractReceivingPageVO> dtoList = page.getRecords();
        // 填充关联属性
        dataFillService.fillAttr(dtoList);

        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    private WmsQueryWrapper<BizReceiptContractReceivingSearchPO> setQueryWrapper(BizReceiptContractReceivingSearchPO po, CurrentUser user) {
        if (null == po) {
            po = new BizReceiptContractReceivingSearchPO();
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptContractReceivingSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper
                .likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), "biz_receipt_contract_receiving_head.receipt_code", po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), "biz_receipt_contract_receiving_head.receipt_type", po.getReceiptType())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
                        "biz_receipt_contract_receiving_head.receipt_status", po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getDesc()), "biz_receipt_contract_receiving_head.description", po.getDesc())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), "sys_user.user_name", po.getCreateUserName());
        return wrapper;
    }

    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptContractReceivingHeadDTO headDTO =
                UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        if (UtilCollection.isNotEmpty(headDTO.getSubItemList())) {
            headDTO.getSubItemList().forEach(c -> c.setCanDeliveryQty(c.getQty().subtract(c.getTotalQty())));
        }
        // 设置申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 合同收货单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptContractReceivingHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -过账
            return buttonVO.setButtonPost(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 单据状态为已完成：冲销；
            return buttonVO.setButtonWriteOff(true);
        }
        return buttonVO;
    }

    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        po.getItemList().forEach(c -> c.setQty(c.getQty().setScale(3, RoundingMode.HALF_UP)));

    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.CONTRACT_RECEIVING.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新合同收货
            bizReceiptContractReceivingHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteItem(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_INPUT.getValue());
            po.setReceiptCode(code);
            po.setId(null);
            bizReceiptContractReceivingHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存合同收货head成功!单号{},主键{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptContractReceivingItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }

        for (BizReceiptContractSubItemDTO subItem : po.getSubItemList()) {
            subItem.setId(null);
            subItem.setReceiveId(po.getId());
        }
        bizReceiptContractReceivingItemDataWrap.saveBatchDto(po.getItemList());
        // 保存分项信息
        bizReceiptContractSubItemDataWrap.saveBatchDto(po.getSubItemList());
        log.debug("批量保存合同收货item成功,code{},headId{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(ctx);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }

    /**
     * 删除合同收货行项目
     *
     * @param headDTO 合同收货
     */
    private void deleteItem(BizReceiptContractReceivingHeadDTO headDTO) {
        UpdateWrapper<BizReceiptContractReceivingItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptContractReceivingItem::getHeadId,
                headDTO.getId());
        bizReceiptContractReceivingItemDataWrap.physicalDelete(wrapper);

        UpdateWrapper<BizReceiptContractSubItem> wrapper1 = new UpdateWrapper<>();
        wrapper1.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptContractSubItem::getReceiveId,
                headDTO.getId());
        bizReceiptContractSubItemDataWrap.physicalDelete(wrapper1);
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptContractReceivingHeadDTO : "要保持单据流的申请单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文 - 要保持单据流的申请单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptContractReceivingItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的合同收货
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存合同收货附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.CONTRACT_RECEIVING.getValue(), user.getId());
        log.debug("保存合同收货附件成功!");
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存合同收货
        this.save(ctx);
        po.setSubmitTime(UtilDate.getNow()).setSubmitUserId(ctx.getCurrentUser().getId());
        // 更新合同收货head、item状态 - 已提交
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
    }

    public void updateSubItemQty(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 过账时
        if (EnumReceiptOperationType.RECEIPT_OPERATION_POSTING.equals(operationLogType)) {
            if (po.getReceiveType().equals(1)) {
                for (BizReceiptContractSubItemDTO subItem : po.getSubItemList()) {
                    bizReceiptContractSubItemDataWrap.update(new UpdateWrapper<BizReceiptContractSubItem>().lambda()
                            .setSql("total_qty = total_qty + " + subItem.getReceiveQty())
                            .setSql("total_value = total_value + " + subItem.getReceiveValue())
                            .eq(BizReceiptContractSubItem::getHeadId, po.getContractId())
                            .eq(BizReceiptContractSubItem::getRid, subItem.getRid()));
                    //.eq(BizReceiptContractSubItem::getReceiveId, 0));
                }
            }
        } else if (EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF.equals(operationLogType)) {
            if (po.getReceiveType().equals(1) && UtilCollection.isNotEmpty(po.getSubItemList())) {
                for (BizReceiptContractSubItemDTO subItem : po.getSubItemList()) {
                    bizReceiptContractSubItemDataWrap.update(new UpdateWrapper<BizReceiptContractSubItem>().lambda()
                            .setSql("total_qty = total_qty - " + subItem.getReceiveQty())
                            .setSql("total_value = total_value - " + subItem.getReceiveValue())
                            .eq(BizReceiptContractSubItem::getHeadId, po.getContractId())
                            .eq(BizReceiptContractSubItem::getRid, subItem.getRid()));
                    //.eq(BizReceiptContractSubItem::getReceiveId, 0));
                }
            }
        }


    }

    /**
     * 更新合同收货状态
     *
     * @param headDTO     合同收货head
     * @param itemDTOList 合同收货item
     */
    public void updateStatus(BizReceiptContractReceivingHeadDTO headDTO, List<BizReceiptContractReceivingItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新合同收货head状态
     *
     * @param headDto 合同收货head
     */
    private void updateHead(BizReceiptContractReceivingHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptContractReceivingHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新合同收货item状态
     *
     * @param itemDtoList 合同收货item
     */
    private void updateItem(List<BizReceiptContractReceivingItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptContractReceivingItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }


    /**
     * 查询合同列表
     *
     * @param ctx
     */
    public void getContract(BizContext ctx) {

        BizReceiptContractSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 只查“其他合同”中，甲方为华信，且状态为执行中的非生产物资类&服务类&施工类
        po.setFirstParty(EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode());
        po.setPurchaseTypeList(Arrays.asList(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode(), EnumPurchaseType.SERVICE.getCode(), EnumPurchaseType.CONSTRUCTION.getCode()));
        po.setReceiptStatusList(Collections.singletonList(EnumReceiptStatus.RECEIPT_STATUS_EXECUTING.getValue()));
        po.setReceiptType(EnumReceiptType.OTHER_CONTRACT.getValue());
        QueryWrapper<BizReceiptContractHead> headQueryWrapper = new QueryWrapper<>();
        headQueryWrapper.lambda()
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptContractHead::getReceiptCode, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptContractHead::getContractName, po.getContractName())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptContractHead::getReceiptCode, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getFirstParty()), BizReceiptContractHead::getFirstParty, po.getFirstParty())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), BizReceiptContractHead::getReceiptType, po.getReceiptType())
                .in(UtilCollection.isNotEmpty(po.getPurchaseTypeList()), BizReceiptContractHead::getPurchaseType, po.getPurchaseTypeList())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptContractHead::getReceiptStatus, po.getReceiptStatusList())
                .eq(UtilNumber.isNotEmpty(po.getSupplierId()), BizReceiptContractHead::getSupplierId, po.getSupplierId())
        ;

        List<BizReceiptContractHead> headList = bizReceiptContractHeadDataWrap.list(headQueryWrapper);
        List<BizReceiptContractHeadDTO> bizReceiptContractHeadDTOS = UtilCollection.toList(headList, BizReceiptContractHeadDTO.class);
        dataFillService.fillAttr(bizReceiptContractHeadDTOS);
        List<BizReceiptContractReceivingHeadDTO> result = new ArrayList<>();

        for (BizReceiptContractHeadDTO head : bizReceiptContractHeadDTOS) {
            BizReceiptContractReceivingHeadDTO dto = new BizReceiptContractReceivingHeadDTO();
            List<BizReceiptContractReceivingItemDTO> itemList = new ArrayList<>();
            head.getItemList().forEach(item -> {
                BizReceiptContractReceivingItemDTO itemDTO = UtilBean.newInstance(item, BizReceiptContractReceivingItemDTO.class);
                itemDTO.setPreReceiptHeadId(head.getId());
                itemDTO.setPreReceiptItemId(item.getId());
                itemDTO.setPreReceiptQty(item.getQty());
                itemDTO.setContractCode(head.getReceiptCode());
                itemDTO.setContractRid(item.getRid());
                itemDTO.setPreReceiptType(head.getReceiptType());
                itemDTO.setQty(BigDecimal.ZERO);
                itemDTO.setCanDeliveryQty(item.getUnContractQty());
                itemDTO.setPurchaseRid(item.getPurchaseReceiptRid());
                // BizReceiptPurchaseApplyItem one = bizReceiptPurchaseApplyItemDataWrap.getOne(new QueryWrapper<BizReceiptPurchaseApplyItem>().lambda().eq(BizReceiptPurchaseApplyItem::getId, item.getPreReceiptItemId()));
                itemDTO.setFtyId(1L);
                itemDTO.setFtyCode(dictionaryService.getFtyCacheById(itemDTO.getFtyId()).getFtyCode());
                itemList.add(itemDTO);
            });
            dto.setContractId(head.getId());
            dto.setReceiveType(1);
            dto.setContractCode(head.getReceiptCode());
            dto.setContractName(head.getContractName());
            dto.setPurchaseReceiptCode(head.getItemList().get(0).getPurchaseReceiptCode());
            List<BizReceiptContractSubItem> subItems = bizReceiptContractSubItemDataWrap.list(new QueryWrapper<BizReceiptContractSubItem>().lambda().eq(BizReceiptContractSubItem::getHeadId, dto.getContractId())
                    .eq(BizReceiptContractSubItem::getReceiveId, 0)
            );
            List<BizReceiptContractSubItemDTO> subItemDTOS = UtilCollection.toList(subItems, BizReceiptContractSubItemDTO.class);
            subItemDTOS.forEach(c -> c.setCanDeliveryQty(c.getQty().subtract(c.getTotalQty())));
            dataFillService.fillAttr(dto);
            dto.setItemList(itemList);
            dto.setSubItemList(subItemDTOS);
            dto.setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName());
            dto.setDescription(po.getDescription());
            result.add(dto);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, result);
    }

    /**
     * 查询送货通知
     *
     * @param ctx
     */
    public void getDelivery(BizContext ctx) {
        BizReceiptDeliveryNoticeSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询送货管理“托收PO批次审批”单，单据状态为已完成，并且有采购订单的托收PO批次审批单；
        po.setSendType(EnumSendType.OFFSHORE_PROCUREMENT.getValue());
        po.setReceiptStatusList(Collections.singletonList(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        po.setReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue());

        List<BizReceiptContractReceivingHeadDTO> result = new ArrayList<>();

        List<BizReceiptContractHead> bizReceiptContractHeads = bizReceiptContractHeadDataWrap.list(new QueryWrapper<BizReceiptContractHead>().lambda().ne
                (BizReceiptContractHead::getDeliveryCode, Const.STRING_EMPTY).eq(BizReceiptContractHead::getPurchaseType, EnumPurchaseType.ASSET.getCode()));
        if (UtilCollection.isNotEmpty(bizReceiptContractHeads)) {
            List<String> deliveryCodes = bizReceiptContractHeads.stream().map(BizReceiptContractHead::getDeliveryCode).collect(Collectors.toList());
            QueryWrapper<BizReceiptDeliveryNoticeHead> headQueryWrapper = new QueryWrapper<>();
            headQueryWrapper.lambda()
                    .like(UtilString.isNotNullOrEmpty(po.getDeliveryCode()), BizReceiptDeliveryNoticeHead::getReceiptCode, po.getDeliveryCode())
                    .eq(UtilNumber.isNotEmpty(po.getSendType()), BizReceiptDeliveryNoticeHead::getSendType, po.getSendType())
                    .ne(BizReceiptDeliveryNoticeHead::getPurchaseCode, Const.STRING_EMPTY)
                    .in(UtilCollection.isNotEmpty(deliveryCodes), BizReceiptDeliveryNoticeHead::getReceiptCode, deliveryCodes)
                    .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptDeliveryNoticeHead::getReceiptStatus, po.getReceiptStatusList());
            List<BizReceiptDeliveryNoticeHead> deliveryNoticeHeads = bizReceiptDeliveryNoticeHeadDataWrap.list(headQueryWrapper);
            List<BizReceiptDeliveryNoticeHeadDTO> deliveryNoticeHeadDTOS = UtilCollection.toList(deliveryNoticeHeads, BizReceiptDeliveryNoticeHeadDTO.class);
            dataFillService.fillAttr(deliveryNoticeHeadDTOS);

            for (BizReceiptDeliveryNoticeHeadDTO head : deliveryNoticeHeadDTOS) {
                BizReceiptContractReceivingHeadDTO dto = new BizReceiptContractReceivingHeadDTO();

                BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getOne(new QueryWrapper<BizReceiptContractHead>().lambda().eq(BizReceiptContractHead::getDeliveryCode, head.getReceiptCode()));

                if (UtilObject.isNotNull(contractHead)) {

                    List<BizReceiptContractReceivingItemDTO> itemList = new ArrayList<>();

                    head.getItemList().forEach(item -> {
                        BizReceiptContractReceivingItemDTO itemDTO = UtilBean.newInstance(item, BizReceiptContractReceivingItemDTO.class);
                        // BizReceiptContractItem contractItem = bizReceiptContractItemDataWrap.getById(item.getReferReceiptItemId());

                        itemDTO.setPreReceiptHeadId(head.getId());
                        itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                        itemDTO.setContractCode(item.getContractCode());
                        itemDTO.setContractRid(item.getContractRid());
                        itemDTO.setPreReceiptItemId(item.getId());
                        itemDTO.setPreReceiptQty(item.getQty());

                        itemDTO.setPreReceiptType(head.getReceiptType());
                        itemDTO.setQty(BigDecimal.ZERO);
                        itemDTO.setFtyId(1L);
                        itemDTO.setFtyCode(dictionaryService.getFtyCacheById(itemDTO.getFtyId()).getFtyCode());
                        itemDTO.setLocationCode(dictionaryService.getLocationCacheById(itemDTO.getLocationId()).getLocationCode());
                        itemDTO.setPurchaseReceiptCode(item.getPurchaseCode());
                        itemDTO.setPurchaseRid(item.getPurchaseRid());

                        itemDTO.setMatGroupName(dicMaterialGroupDataWrap.getById(itemDTO.getMatGroupId()).getMatGroupName());
                        itemList.add(itemDTO);
                    });

                    // head.getItemList().forEach(item -> {
                    //     BizReceiptContractReceivingItemDTO itemDTO = UtilBean.newInstance(contractItem, BizReceiptContractReceivingItemDTO.class);
                    //     itemDTO.setPreReceiptHeadId(head.getId());
                    //     // 这是子合同
                    //     itemDTO.setContractCode(item.getContractCode());
                    //     itemDTO.setContractRid(item.getContractRid());
                    //     itemDTO.setPreReceiptItemId(item.getId());
                    //     itemDTO.setPreReceiptQty(item.getQty());
                    //     itemDTO.setPreReceiptType(head.getReceiptType());
                    //     itemDTO.setQty(BigDecimal.ZERO);
                    //     itemDTO.setFtyId(item.getFtyId());
                    //     itemDTO.setFtyCode(dictionaryService.getFtyCacheById(itemDTO.getFtyId()).getFtyCode());
                    //     itemDTO.setLocationId(item.getLocationId());
                    //     itemDTO.setLocationCode(dictionaryService.getLocationCacheById(itemDTO.getLocationId()).getLocationCode());
                    //     itemDTO.setPurchaseReceiptCode(item.getPurchaseCode());
                    //     itemDTO.setPurchaseRid(item.getPurchaseRid());
                    //     itemDTO.setMatGroupName(dicMaterialGroupDataWrap.getById(itemDTO.getMatGroupId()).getMatGroupName());
                    //     itemList.add(itemDTO);
                    //     }
                    // );
                    dto.setContractId(contractHead.getId());
                    dto.setReceiveType(2);
                    dto.setDeliveryCode(head.getReceiptCode());
                    dto.setDeliveryId(head.getId());
                    dto.setDeliveryNoticeDescribe(head.getDeliveryNoticeDescribe());
                    dto.setPurchaseReceiptCode(head.getPurchaseCode());
                    dataFillService.fillAttr(dto);
                    dto.setItemList(itemList);
                    dto.setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName());
                    dto.setDescription(po.getDescription());
                    result.add(dto);
                }
            }
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, result.stream().filter(e -> UtilCollection.isNotEmpty(e.getItemList())).collect(Collectors.toList()));
    }

    public void postInputToSap(BizContext ctx) {
        // 入参上下文 - 收货单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_POSTING);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        // 未同步sap行项目
        List<BizReceiptContractReceivingItemDTO> itemListNotSync = headDTO.getItemList().stream()
                // B10847 服务类、施工类、非生产类 合同收货 过账时，只传大于0的行，为0的行需要过滤
                .filter(e -> headDTO.getReceiveType().equals(2) || UtilNumber.isNotEmpty(e.getQty()))
                .filter(e -> UtilObject.isEmpty(e.getIsPost()) || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
                .collect(Collectors.toList());
        /* ******** 设置账期 ******** */
        itemListNotSync.forEach(p -> p.setPostingDate(headDTO.getPostingDate()));
        this.setInPostDate(itemListNotSync, user);
        HXPostingReturn returnObj = new HXPostingReturn();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            HXPostingHeader header = this.transferPosting(headDTO, itemListNotSync, false);
            returnObj = hxInterfaceService.posting(header, false);
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {

                int i =1 ;
                for (BizReceiptContractReceivingItemDTO itemDTO : itemListNotSync) {
                    String matDocCode = returnObj.getMatDocCode();
                    itemDTO.setMatDocCode(matDocCode);
                    itemDTO.setMatDocRid(String.format("%04d",i));
                    i++;
                    itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
                    itemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                }
                // 更新收货单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                this.updateItem(itemListNotSync);

                // 更新收货单状态 - 已记账
                this.updateStatus(headDTO, itemListNotSync, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());

                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("收货单{}SAP过账失败", headDTO.getReceiptCode());
                // 防止已完成状态被覆盖
                if (!checkCompletedStatus(headDTO)) {
                    // 更新收货单head、item状态-未同步
                    this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                }
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync = headDTO.getItemList().stream().map(BizReceiptContractReceivingItemDTO::getMatDocCode)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    public void checkStatusCompleted(BizContext ctx) {
        // 入参上下文 - 收货单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        boolean allPost = headDTO.getItemList().stream().allMatch(e -> e.getIsPost().equals(EnumRealYn.TRUE.getIntValue()));
        if (allPost) {
            // 更新单据,行项目状态为已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
    }


    public void checkStatusWriteOff(BizContext ctx) {
        // 入参上下文 - 收货单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        boolean allWriteOff = headDTO.getItemList().stream().allMatch(e -> e.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()));
        if (allWriteOff) {
            // 整单全部冲销，显示已冲销 更新单据
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
        }
    }

    /**
     * 生成付款计划
     *
     * @param ctx
     */
    public void genPaymentPlan(BizContext ctx) {
        // 入参上下文 - 收货单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        if (EnumReceiptOperationType.RECEIPT_OPERATION_POSTING.equals(operationLogType)) {
            // 按照行项目的合同号分组（注意这里的合同号和抬头的不一致）
            Map<String, List<BizReceiptContractReceivingItemDTO>> contractMap = headDTO.getItemList().stream().collect(Collectors.groupingBy(BizReceiptContractReceivingItemDTO::getContractCode));
            contractMap.forEach((k, v) -> {
                BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getOne(new QueryWrapper<BizReceiptContractHead>().lambda().eq(BizReceiptContractHead::getReceiptCode, k));
                BizReceiptContractHeadDTO contractHeadDTO = UtilBean.newInstance(contractHead, BizReceiptContractHeadDTO.class);
                dataFillService.fillAttr(contractHeadDTO);
                // 1非生产物资类&服务类&施工类合同收货并且合同含有工程服务进度款支付节点
                if (headDTO.getReceiveType() == 1 && UtilCollection.isNotEmpty(contractHeadDTO.getNodeList())) {
                    // 处理工程服务进度款
                    Optional<BizReceiptContractPaymentNodeDTO> node7 = contractHeadDTO.getNodeList().stream()
                            .filter(e -> e.getPaymentNode().equals(EnumContractPaymentNode.NODE_7.getCode()))
                            .findFirst();
                    node7.ifPresent(nodeDTO -> generatePaymentPlan(v, headDTO, contractHead, nodeDTO));
                }
                // 资产类合同收货并且合同含有验收款支付节点
                if (headDTO.getReceiveType() == 2 && UtilCollection.isNotEmpty(contractHeadDTO.getNodeList())) {
                    // 处理验收款
                    Optional<BizReceiptContractPaymentNodeDTO> node5 = contractHeadDTO.getNodeList().stream()
                            .filter(e -> e.getPaymentNode().equals(EnumContractPaymentNode.NODE_5.getCode()))
                            .findFirst();
                    node5.ifPresent(nodeDTO -> generatePaymentPlan(v, headDTO, contractHead, nodeDTO));
                }
            });

        }
    }

    private void generatePaymentPlan(List<BizReceiptContractReceivingItemDTO> v, BizReceiptContractReceivingHeadDTO headDTO, BizReceiptContractHead contractHead, BizReceiptContractPaymentNodeDTO node) {
        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiveId(headDTO.getId());
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(1L);
        paymentPlanHeadDTO.setPaymentNode(node.getPaymentNode());
        paymentPlanHeadDTO.setPlanType(headDTO.getReceiveType() == 2 ? 4 : 2);

        List<BizReceiptContractItem> contractItems = bizReceiptContractItemDataWrap.list(new QueryWrapper<BizReceiptContractItem>().lambda().eq(BizReceiptContractItem::getHeadId, contractHead.getId()));
        paymentPlanHeadDTO.setTaxCodeRate(contractItems.get(0).getTaxCodeRate());
        paymentPlanHeadDTO.setContractAmount(contractHead.getContractAmountExcludeTax().add(contractHead.getTaxAmount()));
        paymentPlanHeadDTO.setContractId(contractHead.getId());
        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
        paymentPlanHeadDTO.setRate(node.getRate());
        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);
        BigDecimal zero = BigDecimal.ZERO;
        List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();
        for (BizReceiptContractReceivingItemDTO itemDTO : v) {
            BizReceiptPaymentPlanItemDTO paymentPlanItemDTO = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, paymentPlanItemDTO);
            paymentPlanItemDTO.setId(null);
            paymentPlanItemDTO.setHeadId(paymentPlanHeadDTO.getId());
            paymentPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            paymentPlanItemDTO.setContractQty(itemDTO.getPreReceiptQty());
            // paymentPlanItemDTO.setQty(paymentPlanItemDTO.getContractQty());
            paymentPlanItemDTO.setCreateUserId(1L);
            paymentPlanItemDTO.setPurchaseReceiptRid(itemDTO.getPurchaseRid());
            paymentPlanItemDTO.setPreReceiptType(headDTO.getReceiptType());
            paymentPlanItemDTO.setPreReceiptItemId(itemDTO.getId());
            paymentPlanItemDTO.setPreReceiptHeadId(headDTO.getId());
            paymentPlanItemDTO.setTaxAmount(paymentPlanItemDTO.getTaxPrice().multiply(paymentPlanItemDTO.getQty()).setScale(2, RoundingMode.HALF_UP));
            paymentPlanItemDTOS.add(paymentPlanItemDTO);
            zero = zero.add(paymentPlanItemDTO.getQty().multiply(itemDTO.getTaxPrice()));
        }
        paymentPlanHeadDTO.setQty(zero.multiply(node.getRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        // 更新清款金额* 支付比例
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(paymentPlanHeadDTO);
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(paymentPlanItemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : paymentPlanItemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(paymentPlanHeadDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 获取下个月字符串
     *
     * @return
     */
    private String getMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取下个月
        YearMonth nextMonth = YearMonth.from(currentDate).plusMonths(1);
        // 格式化为 "yyyy-MM" 的字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return nextMonth.format(formatter);

    }

    public void updatePreReceiptQty(BizContext ctx) {
        // 入参上下文 - 收货单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 过账时
        if (EnumReceiptOperationType.RECEIPT_OPERATION_POSTING.equals(operationLogType)) {
            if (headDTO.getReceiveType().equals(2)) {
                for (BizReceiptContractReceivingItemDTO itemDTO : headDTO.getItemList()) {
                    bizReceiptDeliveryNoticeItemDataWrap.update(new UpdateWrapper<BizReceiptDeliveryNoticeItem>().lambda()
                            .setSql("can_delivery_qty = can_delivery_qty -" + itemDTO.getQty())
                            .eq(BizReceiptDeliveryNoticeItem::getId, itemDTO.getPreReceiptItemId()));
                }
            }
            // 冲销时
        } else if (EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF.equals(operationLogType)) {
            if (headDTO.getReceiveType().equals(2)) {
                for (BizReceiptContractReceivingItemDTO itemDTO : headDTO.getItemList()) {
                    bizReceiptDeliveryNoticeItemDataWrap.update(new UpdateWrapper<BizReceiptDeliveryNoticeItem>().lambda()
                            .setSql("can_delivery_qty = can_delivery_qty +" + itemDTO.getQty())
                            .eq(BizReceiptDeliveryNoticeItem::getId, itemDTO.getPreReceiptItemId()));
                }
            }
        }

    }


    public boolean checkCompletedStatus(BizReceiptContractReceivingHeadDTO headDTO) {
        return bizReceiptContractReceivingHeadDataWrap.getById(headDTO.getId()).getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 过账前设置行项目账期
     *
     * @param inputItemDTOList 未同步sap收货单行项目
     * @param user             当前用户
     */
    public void setInPostDate(List<BizReceiptContractReceivingItemDTO> inputItemDTOList, CurrentUser user) {
        if (UtilCollection.isNotEmpty(inputItemDTOList)) {
            Date postingDate = inputItemDTOList.get(0).getPostingDate();
            Date writeOffPostingDate = inputItemDTOList.get(0).getWriteOffPostingDate();
            if (UtilObject.isNull(postingDate)) {
                postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            if (UtilObject.isNull(writeOffPostingDate)) {
                writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }

            for (BizReceiptContractReceivingItemDTO inputItemDTO : inputItemDTOList) {
                if (EnumRealYn.FALSE.getIntValue().equals(inputItemDTO.getIsWriteOff())) {
                    inputItemDTO.setDocDate(UtilDate.getNow());
                    inputItemDTO.setPostingDate(postingDate);
                } else {
                    inputItemDTO.setWriteOffDocDate(UtilDate.getNow());
                    inputItemDTO.setWriteOffPostingDate(writeOffPostingDate);

                }
            }
        }
    }


    public HXPostingHeader transferPosting(BizReceiptContractReceivingHeadDTO headDTO, List<BizReceiptContractReceivingItemDTO> itemList, boolean isWriteOff) {

        HXPostingHeader header = new HXPostingHeader();
        header.setReceiptCode(headDTO.getReceiptCode());
        header.setReceiptType(headDTO.getReceiptType());
        header.setReceiptId(headDTO.getId());
        if (isWriteOff) {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffDocDate()));
        } else {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getDocDate()));
        }
        List<HXPostingItem> items = itemList.stream().map(input -> {
            HXPostingItem item = new HXPostingItem();
            item.setReceiptRid(input.getRid());
            item.setFtyCode(input.getFtyCode());
            item.setLocationCode1(input.getLocationCode());
            item.setMatCode(input.getMatCode());
            item.setQty(UtilBigDecimal.getString(input.getQty()));
            item.setUnitCode(input.getUnitCode());
            if (isWriteOff) {
                item.setMoveType(EnumMoveType.MOVE_TYPE_102.getValue());
            } else {
                item.setMoveType(EnumMoveType.MOVE_TYPE_101.getValue());
            }
            item.setPurchaseOrderCode(headDTO.getPurchaseReceiptCode());
            item.setPurchaseOrderItemCode(input.getPurchaseRid());
            if (isWriteOff) {
                // 冲销传入 物料凭证信息
                item.setMatDocCode(input.getMatDocCode());
                item.setMatDocYear(String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));
            }
            return item;
        }).collect(Collectors.toList());
        header.setItems(items);
        return header;
    }


    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptContractReceivingHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        bizReceiptContractReceivingItemDataWrap.removeByIds(headDTO.getItemList().stream().map(BizReceiptContractReceivingItemDTO::getId).collect(Collectors.toList()));
        bizReceiptContractReceivingHeadDataWrap.removeById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 逻辑删除附件
        receiptAttachmentService.deleteBizReceiptAttachment(headId, EnumReceiptType.CONTRACT_RECEIVING.getValue());
    }

    /**
     * 冲销前校验
     *
     * @param ctx
     */
    public void checkWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId()) || UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取收货单行项目
        List<BizReceiptContractReceivingItem> itemList = bizReceiptContractReceivingItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptContractReceivingItemDTO> itemDTOList =
                UtilCollection.toList(itemList, BizReceiptContractReceivingItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(itemDTOList);
        // 校验行项目单据类型及状态
        Set<String> ridSet = itemDTOList.stream().filter(e -> !this.itemCanWriteOff(e))
                .map(BizReceiptContractReceivingItemDTO::getRid).collect(Collectors.toSet());
        if (!ridSet.isEmpty()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, ridSet.toString());
        }
        // 冲销标识等于1或者过账标识等于0
        Set<String> isWriteOff = itemDTOList.stream()
                .filter(e -> EnumRealYn.TRUE.getIntValue().equals(e.getIsWriteOff())
                        || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
                .map(BizReceiptContractReceivingItemDTO::getRid).collect(Collectors.toSet());
        if (!isWriteOff.isEmpty()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, isWriteOff.toString());
        }
        // 获取收货单
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(po.getHeadId());

        // 转dto
        BizReceiptContractReceivingHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        headDTO.setWriteOffPostingDate(po.getWriteOffPostingDate());
        headDTO.setWriteOffReason(po.getWriteOffReason());
        // 设置冲销标识
        itemDTOList.forEach(itemDTO -> itemDTO.setWriteOffPostingDate(po.getWriteOffPostingDate()).setWriteOffReason(po.getWriteOffReason())
                .setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        headDTO.setItemList(itemDTOList);
        // 校验下游付款预算单
        LambdaQueryWrapper<BizReceiptPaymentPlanItem> paymentPlanItemWrapper = new LambdaQueryWrapper<>();
        paymentPlanItemWrapper.in(BizReceiptPaymentPlanItem::getPreReceiptItemId, po.getItemIds());
        List<BizReceiptPaymentPlanItem> paymentPlanItemList = bizReceiptPaymentPlanItemDataWrap.list(paymentPlanItemWrapper);
        if (UtilCollection.isNotEmpty(paymentPlanItemList)) {
            List<Long> paymentPlanHeadIdList = paymentPlanItemList.stream().map(BizReceiptPaymentPlanItem::getHeadId).collect(Collectors.toList());
            List<BizReceiptPaymentPlanHead> paymentPlanHeadList = bizReceiptPaymentPlanHeadDataWrap.listByIds(paymentPlanHeadIdList);
            for (BizReceiptPaymentPlanHead paymentPlanHead : paymentPlanHeadList) {
                if (!EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue().equals(paymentPlanHead.getReceiptStatus())) {
                    throw new WmsException(EnumReturnMsg.RETURN_ERROR_PAYMENT_PLAN_STATUS_CANNOT_REVOKE, paymentPlanHead.getReceiptCode());
                }
            }
        }
        // 设置要冲销的验收收货单到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
    }

    /**
     * sap 冲销
     *
     * @param ctx
     */
    public void writeOffToSap(BizContext ctx) {
        // 入参上下文 - 收货单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();

        List<BizReceiptContractReceivingItemDTO> itemListNotSync = headDTO.getItemList();

        /* ******** 设置冲销账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        HXPostingReturn returnObj = new HXPostingReturn();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */

            HXPostingHeader header = this.transferPosting(headDTO, itemListNotSync, true);
            returnObj = hxInterfaceService.writeOff(header, true);
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号

                for (BizReceiptContractReceivingItemDTO itemDTO : itemListNotSync) {
                    itemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                    itemDTO.setWriteOffMatDocCode(returnObj.getMatDocCode());
                    itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
                    itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
                }

                // 删除下游付款预算单
                LambdaQueryWrapper<BizReceiptPaymentPlanItem> paymentPlanItemWrapper = new LambdaQueryWrapper<>();
                paymentPlanItemWrapper.in(BizReceiptPaymentPlanItem::getPreReceiptItemId, itemListNotSync.stream().map(BizReceiptContractReceivingItemDTO::getId).collect(Collectors.toList()));
                List<BizReceiptPaymentPlanItem> paymentPlanItemList = bizReceiptPaymentPlanItemDataWrap.list(paymentPlanItemWrapper);
                if (UtilCollection.isNotEmpty(paymentPlanItemList)) {
                    bizReceiptPaymentPlanItemDataWrap.removeBatchByIds(paymentPlanItemList.stream().map(BizReceiptPaymentPlanItem::getId).collect(Collectors.toList()));
                    bizReceiptPaymentPlanHeadDataWrap.removeBatchByIds(paymentPlanItemList.stream().map(BizReceiptPaymentPlanItem::getHeadId).collect(Collectors.toList()));
                    // 删除单据流
                    receiptRelationService.deleteReceiptTree(paymentPlanItemList.stream().map(BizReceiptPaymentPlanItem::getHeadId).collect(Collectors.toList()), EnumReceiptType.PAYMENT_PLAN.getValue());
                }

                // 更新收货单行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】
                this.updateItem(itemListNotSync);

            } else {
                log.error("收货单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync =
                    headDTO.getItemList().stream().map(BizReceiptContractReceivingItemDTO::getWriteOffMatDocCode)
                            .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }

        ctx.setPoContextData(headDTO);
    }

    /**
     * 冲销行项目校验
     *
     * @param itemDTO BizInspectInputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptContractReceivingItemDTO itemDTO) {

        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemDTO.getItemStatus()));
    }

    /**
     * 过账前数据校验
     */
    public void checkPost(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取收货单
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptContractReceivingHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        // 填充关联属性及父子属性
        dataFillService.fillAttr(headDTO);
        // 校验可过账的状态
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        if (!canRePostStatusSet.contains(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
        }
    }

}
