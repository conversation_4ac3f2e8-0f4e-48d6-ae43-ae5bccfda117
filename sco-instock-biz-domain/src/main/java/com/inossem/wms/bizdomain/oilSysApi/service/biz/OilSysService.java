package com.inossem.wms.bizdomain.oilSysApi.service.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleRuleDataWrap;
import com.inossem.wms.bizbasis.masterdata.costcenter.service.datawrap.DicCostCenterDataWrap;
import com.inossem.wms.bizbasis.masterdata.wbs.service.datawrap.DicWbsDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockBinDataWrap;
import com.inossem.wms.bizdomain.arrange.service.biz.BinArrangeService;
import com.inossem.wms.bizdomain.output.service.biz.MaterialOutputService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssembleRule;
import com.inossem.wms.common.model.bizdomain.arrange.dto.BizReceiptArrangeHeadDTO;
import com.inossem.wms.common.model.bizdomain.arrange.dto.BizReceiptArrangeItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.masterdata.costcenter.entity.DicCostCenter;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.wbs.entity.DicWbs;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 加油站平台对接
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class OilSysService {

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    protected BizReceiptAssembleRuleDataWrap bizReceiptAssembleRuleDataWrap;
    @Autowired
    private BinArrangeService binArrangeService;
    @Autowired
    private MaterialOutputService materialOutputService;
    @Autowired
    protected StockBinDataWrap stockBinDataWrap;
    @Autowired
    protected DicCostCenterDataWrap dicCostCenterDataWrap;
    @Autowired
    protected DicWbsDataWrap dicWbsDataWrap;

    /**
     * 接口身份认证
     */
    private void checkAuth() {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(ra)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        HttpServletRequest request = sra.getRequest();
        String username = request.getHeader("username");
        String password = request.getHeader("password");
        if (!UtilConst.getInstance().getOilUserName().equals(username) || !UtilConst.getInstance().getOilPassword().equals(password)) {
            throw new WmsException(EnumReturnMsg.SSO_USER_NOT_EXIST);
        }
    }

    /**
     * 加油站平台对接-新增出库-接口2
     */
    public JSONObject saveOutput(BizContext ctx) {
        JSONObject returnJson = new JSONObject();
        try {
            this.checkAuth();
            String jsonStr = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            JSONArray jsonArray = jsonObject.getJSONArray("list");
            List<BizReceiptOutputItemDTO> itemList = new ArrayList<>();
            SysUser user = dictionaryService.getSysUserCacheByuserCode(Const.DEFAULT_USER_CODE);
            CurrentUser currentUser = new CurrentUser();
            currentUser.setId(user.getId());
            Long ftyId = dictionaryService.getFtyIdCacheByCode(Const.DEFAULT_FACTORY_CODE);
            Long locationId = dictionaryService.getLocationIdCacheByCode(Const.DEFAULT_FACTORY_CODE, Const.DEFAULT_LOCATION_CODE_J01);
            DicStockLocationDTO dicStockLocationDTO = dictionaryService.getLocationCacheById(locationId);
            Long whId = dicStockLocationDTO.getWhId();
            String whCode = dicStockLocationDTO.getWhCode();
            BizReceiptOutputHeadDTO headDTO = new BizReceiptOutputHeadDTO();
            // 领料出库
            headDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonItem = jsonArray.getJSONObject(i);
                if (jsonItem.containsKey("outboundCostCenter") && UtilString.isNotNullOrEmpty(jsonItem.getString("outboundCostCenter"))) {
                    String costCenterCode = jsonItem.getString("outboundCostCenter");
                    DicCostCenter costCenter = dicCostCenterDataWrap.getOne(new QueryWrapper<DicCostCenter>().lambda().eq(DicCostCenter::getCostCenterCode, costCenterCode));
                    headDTO.setCostCenterId(costCenter.getId());
                    headDTO.setCostCenterCode(costCenterCode);
                }
                if (jsonItem.containsKey("wbsCode") && UtilString.isNotNullOrEmpty(jsonItem.getString("wbsCode"))) {
                    String wbsCode = jsonItem.getString("wbsCode");
                    DicWbs dicWbs = dicWbsDataWrap.getOne(new QueryWrapper<DicWbs>().lambda().eq(DicWbs::getWbsCode, wbsCode));
                    headDTO.setWbsId(dicWbs.getId());
                    headDTO.setWbsCode(wbsCode);
                }
                String binCode = jsonItem.getString("binCode");
                DicWhStorageBinDTO bin = dictionaryService.getBinCacheByBinCode(whCode, binCode);

                Map<Long, List<StockBinDTO>> stockBinMap = new HashMap<>();
                JSONArray itemListArray = jsonItem.getJSONArray("itemList");
                for (int j = 0; j < itemListArray.size(); j++) {
                    JSONObject item = itemListArray.getJSONObject(j);
                    String remark = item.getString("remark");
                    String matCode = item.getString("matCode");
                    Long matId = dictionaryService.getMatIdByMatCode(matCode);
                    DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(matId);
                    BigDecimal qty = item.getBigDecimal("outboundCount");
                    // 自动配货逻辑
                    StockBinPO stockBinPO = new StockBinPO();
                    stockBinPO.setMatId(matId);
                    stockBinPO.setFtyId(ftyId);
                    stockBinPO.setLocationId(locationId);
                    stockBinPO.setBinId(bin.getId());
                    List<StockBinDTO> stockList = new ArrayList<>();
                    if(stockBinMap.size() == 0 || !stockBinMap.containsKey(matId)){
                        stockList = stockBinDataWrap.selectStockBinByStockBinPo(stockBinPO);
                        stockBinMap.put(matId, stockList);

                    } else {
                        stockList = stockBinMap.get(matId);
                    }
                    for (StockBinDTO stockBinDTO : stockList) {
                        if (qty.equals(BigDecimal.ZERO) || stockBinDTO.getQty().equals(BigDecimal.ZERO)) {
                            continue;
                        }
                        BizReceiptOutputItemDTO itemDTO = new BizReceiptOutputItemDTO();
                        itemDTO.setFtyId(ftyId);
                        itemDTO.setLocationId(locationId);
                        itemDTO.setWhId(whId);
                        itemDTO.setTypeId(bin.getTypeId());
                        itemDTO.setBinId(bin.getId());
                        itemDTO.setItemRemark(remark);
                        itemDTO.setMatId(matId);
                        itemDTO.setUnitId(dicMaterialDTO.getUnitId());
                        itemDTO.setStockQty(stockBinDTO.getQty());
                        if (stockBinDTO.getQty().compareTo(qty) >= 0) {
                            itemDTO.setQty(qty);
                            stockBinDTO.setQty(stockBinDTO.getQty().subtract(qty));
                            qty = BigDecimal.ZERO;

                        } else {
                            itemDTO.setQty(stockBinDTO.getQty());
                            qty = qty.subtract(stockBinDTO.getQty());
                            stockBinDTO.setQty(BigDecimal.ZERO);
                        }
                        List<BizReceiptOutputBinDTO> binDTOList = new ArrayList<>();
                        BizReceiptOutputBinDTO binDTO = new BizReceiptOutputBinDTO();
                        binDTO.setTypeId(bin.getTypeId());
                        binDTO.setBinId(bin.getId());
                        binDTO.setQty(itemDTO.getQty());
                        binDTO.setBatchId(stockBinDTO.getBatchId());
                        binDTO.setMatId(itemDTO.getMatId());
                        binDTOList.add(binDTO);
                        itemDTO.setBinDTOList(binDTOList);
                        itemList.add(itemDTO);
                    }
                }
            }
            dataFillService.fillAttr(itemList);
            BizContext ctxNew = new BizContext();
            ctxNew.setCurrentUser(currentUser);
            headDTO.setItemDTOList(itemList);
            ctxNew.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            materialOutputService.submitSameTime(ctxNew);

            returnJson.put("success", true);
            returnJson.put("code", 1);
            returnJson.put("message", "操作成功" + headDTO.getReceiptCode());

        } catch (Exception e){
            returnJson.put("success", false);
            returnJson.put("code", 0);
            returnJson.put("message", "操作异常:" + e.getMessage());
        }
        return returnJson;
    }

    /**
     * 加油站平台对接-新增仓位整理-接口4
     */
    public JSONObject saveBinArrange(BizContext ctx) {
        JSONObject returnJson = new JSONObject();
        try {
            this.checkAuth();
            String jsonStr = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            JSONArray jsonArray = jsonObject.getJSONArray("list");
            List<BizReceiptArrangeItemDTO> itemList = new ArrayList<>();
            SysUser user = dictionaryService.getSysUserCacheByuserCode(Const.DEFAULT_USER_CODE);
            CurrentUser currentUser = new CurrentUser();
            currentUser.setId(user.getId());
            Long ftyId = dictionaryService.getFtyIdCacheByCode(Const.DEFAULT_FACTORY_CODE);
            Long locationId = dictionaryService.getLocationIdCacheByCode(Const.DEFAULT_FACTORY_CODE, Const.DEFAULT_LOCATION_CODE_J01);
            DicStockLocationDTO dicStockLocationDTO = dictionaryService.getLocationCacheById(locationId);
            Long whId = dicStockLocationDTO.getWhId();
            String whCode = dicStockLocationDTO.getWhCode();
            QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, EnumReceiptType.STOCK_BIN_ARRANGE.getValue());
            BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);
            Map<String, List<StockBinDTO>> stockMap = new HashMap<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonItem = jsonArray.getJSONObject(i);
                String sourceBinCode = jsonItem.getString("sourceBinCode");
                DicWhStorageBinDTO sourceBin = dictionaryService.getBinCacheByBinCode(whCode, sourceBinCode);
                String targetBinCode = jsonItem.getString("targetBinCode");
                DicWhStorageBinDTO targetBin = dictionaryService.getBinCacheByBinCode(whCode, targetBinCode);
                String matCode = jsonItem.getString("matCode");
                Long matId = dictionaryService.getMatIdByMatCode(matCode);
                DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(matId);
                BigDecimal qty = jsonItem.getBigDecimal("qty");
                // 自动配货逻辑
                String key = matId + "-" + sourceBin.getId();
                List<StockBinDTO> stockList;
                if(!stockMap.isEmpty() && stockMap.containsKey(key)){
                    stockList = stockMap.get(key);

                } else {
                    StockBinPO stockBinPO = new StockBinPO();
                    stockBinPO.setMatId(matId);
                    stockBinPO.setFtyId(ftyId);
                    stockBinPO.setLocationId(locationId);
                    stockBinPO.setBinId(sourceBin.getId());
                    stockList = stockBinDataWrap.selectStockBinByStockBinPo(stockBinPO);
                    stockMap.put(key, stockList);
                }
                for (StockBinDTO stockBinDTO : stockList) {
                    if (qty.equals(BigDecimal.ZERO) || stockBinDTO.getQty().equals(BigDecimal.ZERO)) {
                        continue;
                    }
                    BizReceiptArrangeItemDTO itemDTO = new BizReceiptArrangeItemDTO();
                    itemDTO.setFtyId(ftyId);
                    itemDTO.setLocationId(locationId);
                    itemDTO.setWhId(dicStockLocationDTO.getWhId());
                    itemDTO.setSourceWhId(whId);
                    itemDTO.setSourceTypeId(sourceBin.getTypeId());
                    itemDTO.setSourceBinId(sourceBin.getId());
                    itemDTO.setTargetWhId(whId);
                    itemDTO.setTargetTypeId(targetBin.getTypeId());
                    itemDTO.setTargetBinId(targetBin.getId());
                    itemDTO.setMatId(matId);
                    itemDTO.setUnitId(dicMaterialDTO.getUnitId());
                    itemDTO.setBatchId(stockBinDTO.getBatchId());
                    itemDTO.setBatchCode(stockBinDTO.getBatchInfo().getBatchCode());
                    itemDTO.setStockQty(stockBinDTO.getQty());
                    if (stockBinDTO.getQty().compareTo(qty) >= 0) {
                        stockBinDTO.setQty(stockBinDTO.getQty().subtract(qty));
                        itemDTO.setOperationQty(qty);
                        qty = BigDecimal.ZERO;

                    } else {
                        itemDTO.setOperationQty(stockBinDTO.getQty());
                        qty = qty.subtract(stockBinDTO.getQty());
                        stockBinDTO.setQty(BigDecimal.ZERO);
                    }
                    itemDTO.setQty(itemDTO.getOperationQty());
                    itemList.add(itemDTO);
                }
            }
            BizReceiptArrangeHeadDTO headDTO = new BizReceiptArrangeHeadDTO();
            dataFillService.fillAttr(itemList);
            for (BizReceiptArrangeItemDTO itemDTO : itemList) {
                List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
                BizReceiptAssembleDTO assembleDTO = new BizReceiptAssembleDTO();
                assembleDTO.setMatId(itemDTO.getMatId());
                assembleDTO.setFtyId(ftyId);
                assembleDTO.setLocationId(locationId);
                assembleDTO.setSpecCode(specFeature.getFeatureCode());
                assembleDTO.setQty(itemDTO.getQty());
                assembleDTO.setStockQty(itemDTO.getStockQty());
                // 批次,存储类型,仓位,wbs编号,wbs描述
                String specDisplayValue = itemDTO.getBatchCode() + "," + itemDTO.getSourceTypeCode() + "," + itemDTO.getSourceBinCode() + "," + itemDTO.getBatchInfo().getSpecStockCode() + "," + itemDTO.getBatchInfo().getSpecStockName();
                String specValue = itemDTO.getBatchId() + "," + itemDTO.getSourceTypeId() + "," + itemDTO.getSourceBinId() + "," + itemDTO.getBatchInfo().getSpecStockCode() + "," + itemDTO.getBatchInfo().getSpecStockName();
                assembleDTO.setSpecDisplayValue(specDisplayValue);
                assembleDTO.setSpecValue(specValue);
                assembleDTOList.add(assembleDTO);
                itemDTO.setAssembleDTOList(assembleDTOList);
            }
            BizContext ctxNew = new BizContext();
            ctxNew.setCurrentUser(currentUser);
            headDTO.setItemList(itemList);
            ctxNew.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            binArrangeService.submit(ctxNew);

            returnJson.put("success", true);
            returnJson.put("code", 1);
            returnJson.put("message", "操作成功");

        } catch (Exception e){
            returnJson.put("success", false);
            returnJson.put("code", 0);
            returnJson.put("message", "操作异常:" + e.getMessage());
        }
        return returnJson;
    }

    /**
     * 加油站平台对接-库存查询-接口5
     */
    public JSONObject getMatBinStock(BizContext ctx) {
        JSONObject returnJson = new JSONObject();
        try {
            this.checkAuth();
            StockBinPO stockBinPo = new StockBinPO();
            String jsonStr = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            if (jsonObject.containsKey("list")) {
                JSONArray jsonArrayList = jsonObject.getJSONArray("list");
                for (int i = 0; i < jsonArrayList.size(); i++) {
                    JSONObject jsonItem = jsonArrayList.getJSONObject(i);
                    if (jsonItem.containsKey("matCode")) {
                        stockBinPo.setMatCode(jsonItem.getString("matCode"));
                    }
                    if (jsonItem.containsKey("binCode")) {
                        stockBinPo.setBinCode(jsonItem.getString("binCode"));
                    }
                }
            }
            stockBinPo.setFtyCode(Const.DEFAULT_FACTORY_CODE);
            stockBinPo.setLocationCode(Const.DEFAULT_LOCATION_CODE_J01);
            List<StockBinDTO> list = stockBinDataWrap.getMatBinStock(stockBinPo);
            JSONArray jsonArray = new JSONArray();
            for (StockBinDTO stockBin : list) {
                JSONObject jsonParam = new JSONObject();
                jsonParam.put("matCode", stockBin.getMatCode());
                jsonParam.put("matName", stockBin.getMatName());
                jsonParam.put("binCode", stockBin.getBinCode());
                jsonParam.put("qty", stockBin.getQty());
                jsonArray.add(jsonParam);
            }
            returnJson.put("date", jsonArray);
            returnJson.put("success", true);
            returnJson.put("code", 1);
            returnJson.put("message", "操作成功");

        } catch (Exception e){
            returnJson.put("success", false);
            returnJson.put("code", 0);
            returnJson.put("message", "操作异常:" + e.getMessage());
        }
        return returnJson;
    }
}
