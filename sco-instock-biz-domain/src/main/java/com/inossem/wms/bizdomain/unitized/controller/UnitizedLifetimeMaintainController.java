package com.inossem.wms.bizdomain.unitized.controller;

import com.inossem.wms.bizdomain.lifetime.service.biz.LifetimeMaintainService;
import com.inossem.wms.bizdomain.unitized.service.biz.UnitizedLifetimeMaintainService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.lifetime.dto.BizReceiptLifetimeHeadDTO;
import com.inossem.wms.common.model.bizdomain.lifetime.po.BizReceiptLifetimeSearchPO;
import com.inossem.wms.common.model.bizdomain.lifetime.vo.BizReceiptLifetimePageVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.lifetime.InspectResultMapVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 寿期维护 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@RestController
public class UnitizedLifetimeMaintainController {

    @Autowired
    protected UnitizedLifetimeMaintainService lifetimeMaintainService;

    /**
     * 查询检定结果下拉
     *
     * @return 检定结果下拉框
     */
    @ApiOperation(value = "查询检定结果下拉", tags = {"寿期管理-寿期维护"})
    @GetMapping(path = "/unitized/lifetime/lifetime-maintain/inspect-result-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<InspectResultMapVO>> getinspectResultDown(BizContext ctx) {
        lifetimeMaintainService.getinspectResultDown(ctx);
        MultiResultVO<InspectResultMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询报废原因下拉
     *
     * @return 检定结果下拉框
     */
    @ApiOperation(value = "查询报废原因下拉", tags = {"寿期管理-寿期维护"})
    @GetMapping(path = "/unitized/lifetime/lifetime-maintain/scrap-cause-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<?>> getscrapCauseDown(BizContext ctx) {
        lifetimeMaintainService.getscrapCauseDown(ctx);
        MultiResultVO<?> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询寿期维护列表-分页
     *
     * @param po 寿期分页查询入参
     * @return 寿期维护单列表
     */
    @ApiOperation(value = "查询寿期维护列表-分页", tags = {"寿期管理-寿期维护"})
    @PostMapping(value = "/unitized/lifetime/lifetime-maintain/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptLifetimePageVO>> getPage(@RequestBody BizReceiptLifetimeSearchPO po, BizContext ctx) {
        lifetimeMaintainService.getPage(ctx);
        PageObjectVO<BizReceiptLifetimePageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询寿期维护单详情
     *
     * @param id 寿期维护单抬头表主键
     * @return 寿期维护单详情
     */
    @ApiOperation(value = "查询寿期维护单详情", tags = {"寿期管理-寿期维护"})
    @GetMapping(value = "/unitized/lifetime/lifetime-maintain/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptLifetimeHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        lifetimeMaintainService.getInfo(ctx);
        BizResultVO<BizReceiptLifetimeHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 寿期维护-保存
     *
     * @param po 保存寿期维护表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "寿期维护-保存", tags = {"寿期管理-寿期维护"})
    @PostMapping(value = "/unitized/lifetime/lifetime-maintain/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptLifetimeHeadDTO po, BizContext ctx) {
        lifetimeMaintainService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 寿期维护-提交
     *
     * @param po 提交寿期维护表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "寿期维护-提交", tags = {"寿期管理-寿期维护"})
    @PostMapping(value = "/unitized/lifetime/lifetime-maintain/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptLifetimeHeadDTO po, BizContext ctx) {
        lifetimeMaintainService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 寿期维护-删除
     *
     * @param id 寿期维护单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "寿期维护-删除", tags = {"寿期管理-寿期维护"})
    @DeleteMapping(value = "/unitized/lifetime/lifetime-maintain/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        lifetimeMaintainService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_LIFETIME_DELETE_SUCCESS, receiptCode);
    }

}
