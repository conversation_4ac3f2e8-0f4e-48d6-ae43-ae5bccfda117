package com.inossem.wms.common.enums.workflow;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批级别枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumApprovalLevel {

    // 1级审批【专工】
    LEVEL_1("1"),
    // 2级审批节点【部门领导(部长)】
    LEVEL_2("2"),
    // 3级审批节点【分管领导】
    LEVEL_3("3"),
    // 4级审批节点【主管】
    LEVEL_4("4"),
    // 5级审批节点【采购计划管理专员】
    LEVEL_5("5"),
    // 6级审批节点【成本主管】
    LEVEL_6("6"),
    // 7级审批节点【费用主管】
    LEVEL_7("7"),
    // 8级审批节点【财务经理】
    LEVEL_8("8"),
    // 9级审批节点【财务出纳】
    LEVEL_9("9"),
    // 10级审批节点【财务部门负责人】
    LEVEL_10("10");

    @Getter
    private final String value;
}
