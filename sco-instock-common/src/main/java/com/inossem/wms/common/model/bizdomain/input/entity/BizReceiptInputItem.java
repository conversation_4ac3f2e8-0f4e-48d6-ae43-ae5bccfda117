package com.inossem.wms.common.model.bizdomain.input.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 入库单行项目表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptInputItem对象", description = "入库单行项目表")
@TableName("biz_receipt_input_item")
public class BizReceiptInputItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "入库单行项目号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键" , example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据操作数量" , example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private Long ftyId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "数量-订单单位" , example = "10")
    private BigDecimal qty;

    @ApiModelProperty(value = "作业数量" , example = "10")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "订单单位" , example = "1")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号" , example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度" , example = "2015-05-11")
    private String matDocYear;

    @ApiModelProperty(value = "移动类型id" , example = "3010")
    private String moveTypeId;

    @ApiModelProperty(value = "冲销标志0-false, 1-true" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true" , example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "冲销物料凭证号" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销物料凭证号", example = "52222222")
    private String deliveryWriteOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间", example = "2021-05-11")
    private Date deliveryWriteOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期", example = "2021-05-11")
    private Date deliveryWriteOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号", example = "0010")
    private String deliveryWriteOffMatDocRid;

    @ApiModelProperty(value = "冲销年度", example = "2021")
    private String deliveryWriteOffMatDocYear;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "维修厂商")
    private String repairFty;

    @ApiModelProperty(value = "工具状态")
    private Integer toolStatus;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;


    private Integer tagType;

    private Integer isSingle;

    private Integer isLabelPrint;

    @ApiModelProperty(value = "工器具 - 管理状态备注")
    private String toolManageStatusRemark;

    @ApiModelProperty(value = "工器具 - 送检单位")
    private String toolInspectUnit;

    @ApiModelProperty(value = "工器具 - 使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    private String toolUseReason;

    @ApiModelProperty(value = "工器具 - 维保日期")
    private Date maintenanceDate;

    @ApiModelProperty(value = "保养备注")
    private String mainRequirement;

    @TableField("mat_doc_code_103")
    private String matDocCode103;
    @TableField("mat_doc_rid_103")
    private String matDocRid103;
    @TableField("mat_doc_year_103")
    private String matDocYear103;

    @ApiModelProperty(value = "到货通知抬头id")
    private Long deliveryNoticeHeadId;

    @ApiModelProperty(value = "到货通知行项目id")
    private Long deliveryNoticeItemId;

    @ApiModelProperty(value = "到货登记抬头id")
    private Long arrivalRegisterHeadId;

    @ApiModelProperty(value = "到货登记行项目id")
    private Long arrivalRegisterItemId;

    @ApiModelProperty(value = "领用单号")
    private String tempStoreOutCode;

    @ApiModelProperty(value = "暂存期间维保要求")
    private String maintenanceRequirements;

    @ApiModelProperty(value = "暂存分类:修旧利废0 专用工器具1 见证件2 试块3 其他4")
    private Integer category;

    @ApiModelProperty(value = "暂存期限（1-12月）")
    private Integer tempStorePeriod;

    @ApiModelProperty(value = "暂存到期日期")
    private Date tempStoreExpireDate;

    @ApiModelProperty(value = "暂存延期到期日期")
    private Date tempStoreDelayExpireDate;

    @ApiModelProperty(value = "暂存延期原因")
    private String tempStoreDelayReason;

    @ApiModelProperty(value = "暂存出库单号")
    private String tempStoreOutputReceiptCode;

    @ApiModelProperty(value = "暂存存储级别")
    private String tempStoreLevel;


    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;

    @ApiModelProperty(value = "已预制数量")
    private BigDecimal precastQty;

    @ApiModelProperty(value = "已预制金额")
    private BigDecimal precastAmount;
}
