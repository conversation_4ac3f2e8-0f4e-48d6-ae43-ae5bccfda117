package com.inossem.wms.common.enums;

import com.inossem.wms.common.model.common.enums.EnumBidMethodMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 招标方式枚举
 */
@Getter
@AllArgsConstructor
public enum EnumBidMethod {

    BIDDING_PROJECT(50, "竞价项目"),
    PURCHASE_AT_INQUIRY_PRICE(60, "询比价"),
    COMPETITIVE_NEGOTIATION(70, "竞争性谈判"),
    DIRECT_PURCHASE(80, "直接采购"),
    ;


    private final Integer code;
    private final String desc;

    private static List<EnumBidMethodMapVO> list;

    public static List<EnumBidMethodMapVO> toList() {
        if (list == null) {
            List<EnumBidMethodMapVO> listInner = new ArrayList<>();
            EnumBidMethod[] ary = EnumBidMethod.values();
            for (EnumBidMethod e : ary) {
                EnumBidMethodMapVO vo = new EnumBidMethodMapVO();
                vo.setBidMethod(e.getCode());
                listInner.add(vo);
            }
            list = listInner;
        }
        return list;
    }

    public static EnumBidMethod getByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (EnumBidMethod e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
