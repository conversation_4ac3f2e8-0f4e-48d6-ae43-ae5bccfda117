package com.inossem.wms.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.model.approval.dto.BizApprovalRuleDTO;
import com.inossem.wms.common.model.email.dto.BizMailRuleDTO;

import lombok.Data;

/**
 * 临时存储区, 系统配置等常量获取
 */
@Data
public class UtilConst {

    private static volatile UtilConst instance;

    /* *******************  系统配置表内容  ********************************/
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 图片路径
     */
    private String imgPath;
    /**
     * PDA升级包路径
     */
    private String appUpgradeFilePath;
    /**
     * 项目路径
     */
    private String baseUrl;
    /**
     * 入库台账文件路径
     */
    private String ledgerFilePath;
    /**
     * dts同步设计院图纸存放路径
     */
    private String dtsPaperFilePath;

    /**
     * 远端dts同步设计院图纸获取路径
     */
    private String dtsPaperRemoteFilePath;

    /**
     * wms帐号
     */
    private String wmsUsername;
    /**
     * wms密码
     */
    private String wmsPassword;

    /**
     * ERP外接系统地址
     */
    private String erpUrl;
    /**
     * 是否同步ERP
     */
    private boolean erpSyncMode;
    /**
     * ERP用户名
     */
    private String erpUserName;
    /**
     * ERP密码
     */
    private String erpPassword;
    /**
     * ERP-BI密钥
     */
    private String secret;

    /**
     * ERP-BI外接系统地址
     */
    private String biUrl;
    /**
     * ERP-BI用户名
     */
    private String biUserName;
    /**
     * ERP-BI密码
     */
    private String biPassword;
    /**
     * ERP-BI密钥
     */
    private String biSecret;
    /**
     * ERP-BI应用名称
     */
    private String biAppName;

    /**
     * OA外接系统地址
     */
    private String oaUrl;
    /**
     * 是否同步OA
     */
    private boolean oaSyncMode;
    /**
     * OA用户名
     */
    private String oaUserName;
    /**
     * OA密码
     */
    private String oaPassword;
    /**
     * OA应用名称
     */
    private String oaAppName;
    /**
     * OA模型名称
     */
    private String oaModelName;
    /**
     * OA模型ID
     */
    private String oaModelId;
    /**
     * Oil-enabled
     */
    private Boolean oilEnabled;
    /**
     * Oil-url
     */
    private String oilUrl;
    /**
     * Oil-appId
     */
    private String oilAppId;
    /**
     * Oil-appSecret
     */
    private String oilAppSecret;
    /**
     * Oil用户名
     */
    private String oilUserName;
    /**
     * Oil密码
     */
    private String oilPassword;
    /**
     * 自动上架策略是否开启
     */
    private Boolean loadStrategyEnabled;
    /**
     * 自动下架策略是否开启
     */
    private Boolean unloadStrategyEnabled;
    /**
     * 0不开启附件,1开启附件
     */
    private boolean attachmentRequired;
    /**
     * 0不开启业务记录,1开启业务记录
     */
    private boolean operationLogRequired;
    /**
     * 0不开启打印,1开启打印
     */
    private boolean printEnable;
    /**
     * ums接口地址
     */
    private String umsUrl;
    /**
     * ums接口地址
     */
    private String umsUrlOther;

    /**
     * 是否同步dts
     */
    private boolean dtsSync;
    /**
     * dts ftp地址
     */
    private String dtsFtpPath;
    /**
     * dts ftp端口
     */
    private int dtsFtpPort;
    /**
     * dts用户名
     */
    private String dtsUserName;
    /**
     * dts密码
     */
    private String dtsPassword;
    /**
     * dts 目录
     */
    private String dtsDirectory;
    /**
     * 华信领导
     */
    private String hxLeader;
    /**
     * 能殷领导
     */
    private String nyLeader;    

    /* *******************  系统配置表内容结束  ********************************/

    /* *******************  系统常用数据  ********************************/
    /**
     * 临时存储区集合
     */
    private Set<String> typeSet = new HashSet<>();
    /**
     * confWh表中outputEnabled为1的type集合
     */
    private Set<String> outputEnabledTypeSet;
    /**
     * 开始审批MAP集合,
     */
    private Map<Integer, BizApprovalRuleDTO> wfReceiptTypeMap;
    /**
     * 开始邮件MAP集合,
     */
    private Map<Integer, BizMailRuleDTO> mailReceiptTypeMap;
    /**
     * 开始短信MAP集合,
     */
    private Map<Integer, BizMailRuleDTO> smsReceiptTypeMap;

    private UtilConst() {}

    public static UtilConst getInstance() {
        if (instance == null) {
            synchronized (UtilConst.class) {
                if (instance == null) {
                    instance = new UtilConst();
                }
            }
        }

        return instance;
    }

    /**
     * 获取临时存储区集合Set
     *
     * @return Set<String> 全部临时存储区code集合
     */
    public Set<String> getDefaultStorageTypeCodeSet() {
        if (UtilCollection.isEmpty(typeSet)) {
            for (EnumDefaultStorageType storageType : EnumDefaultStorageType.values()) {
                typeSet.add(storageType.getTypeCode());
            }
        }
        return typeSet;
    }

    /**
     * 懒加载可出库临时存储类型集合
     *
     * @return Set<String> 可出库临时存储类型code集合
     */
    public Set<String> getOutputEnabledTypeSet() {
        if (UtilCollection.isEmpty(outputEnabledTypeSet)) {
            outputEnabledTypeSet = new HashSet<>();
            for (EnumDefaultStorageType storageType : EnumDefaultStorageType.values()) {
                if(storageType.getOutputEnable().equals(EnumRealYn.TRUE.getIntValue())) {
                    outputEnabledTypeSet.add(storageType.getTypeCode());
                }
            }
        }
        return outputEnabledTypeSet;
    }

    /**
     * 主动新增/修改一个开启审批的业务map
     *
     * @param receiptType 单据类型
     * @param procId 流程ID
     */
    public void setWfReceiptType(Integer receiptType, String procId) {
        if (wfReceiptTypeMap == null) {
            wfReceiptTypeMap = new HashMap<>();
        }

        if (wfReceiptTypeMap.containsKey(receiptType)) {
            BizApprovalRuleDTO type = wfReceiptTypeMap.get(receiptType);
            if (type == null) {
                type = new BizApprovalRuleDTO();
                type.setProcId(procId);
                type.setReceiptType(receiptType);
            } else {
                type.setProcId(procId);
            }

            wfReceiptTypeMap.put(receiptType, type);

        } else {
            BizApprovalRuleDTO type = new BizApprovalRuleDTO();
            type.setProcId(procId);
            type.setReceiptType(receiptType);

            wfReceiptTypeMap.put(receiptType, type);
        }
    }

    /**
     * 获取审批业务的模板ID
     *
     * @param receiptType 单据类型
     * @return String
     */
    public String getWfProcIdByReceiptType(Integer receiptType) {
		if (wfReceiptTypeMap == null) {
			wfReceiptTypeMap = new HashMap<>();
		}

        if (wfReceiptTypeMap.containsKey(receiptType)) {
            BizApprovalRuleDTO type = wfReceiptTypeMap.get(receiptType);
            return type.getProcId();
        }

        return Const.STRING_EMPTY;
    }

    /**
     * 判断业务流程是否需要审批
     *
     * @param receiptType 单据类型
     * @return boolean
     */
    public boolean getWfByReceiptType(Integer receiptType) {
        return !UtilString.isNullOrEmpty(getWfProcIdByReceiptType(receiptType));
    }

    /**
     * 获取全部可审批功能列表
     *
     * @return List<BizApprovalRuleDTO>
     */
    public List<BizApprovalRuleDTO> getAllWfReceiptType() {
        if (wfReceiptTypeMap == null) {
            wfReceiptTypeMap = new HashMap<>();
        }
        return new ArrayList<>(wfReceiptTypeMap.values());
    }

    /**
     * 主动新增/修改一个开启邮件的业务map
     *
     * @param receiptType 单据类型
     * @param procId 模版ID
     */
    public void setMailReceiptType(Integer receiptType, String procId) {
        if (mailReceiptTypeMap == null) {
            mailReceiptTypeMap = new HashMap<>();
        }

        if (mailReceiptTypeMap.containsKey(receiptType)) {
            BizMailRuleDTO type = mailReceiptTypeMap.get(receiptType);
            if (type == null) {
                type = new BizMailRuleDTO();
                // type.setProcId(procId);
                type.setReceiptType(receiptType);
            } else {
                // type.setProcId(procId);
            }

            mailReceiptTypeMap.put(receiptType, type);

        } else {
            BizMailRuleDTO type = new BizMailRuleDTO();
            // type.setProcId(procId);
            type.setReceiptType(receiptType);

            mailReceiptTypeMap.put(receiptType, type);
        }
    }

    /**
     * 判断业务流程是否需要邮件
     *
     * @param receiptType 单据类型
     * @return boolean
     */
    public boolean getMailByReceiptType(Integer receiptType) {
        if (mailReceiptTypeMap == null) {
            mailReceiptTypeMap = new HashMap<>();
        }
        return mailReceiptTypeMap.containsKey(receiptType);
    }

    /**
     * 获取全部可邮件功能列表
     *
     */
    public List<BizMailRuleDTO> getAllMailReceiptType() {
        if (mailReceiptTypeMap == null) {
            mailReceiptTypeMap = new HashMap<>();
        }
        return new ArrayList<>(mailReceiptTypeMap.values());
    }

    /**
     * 主动新增/修改一个开启短信的业务map
     *
     * @param receiptType 单据类型
     */
    public void setSmsReceiptType(Integer receiptType) {
        if (smsReceiptTypeMap == null) {
            smsReceiptTypeMap = new HashMap<>();
        }

        if (!smsReceiptTypeMap.containsKey(receiptType)) {
            BizMailRuleDTO type = new BizMailRuleDTO();
            type.setReceiptType(receiptType);
            smsReceiptTypeMap.put(receiptType, type);
        }
    }

    /**
     * 判断业务流程是否需要短信
     *
     * @param receiptType 单据类型
     */
    public boolean getSmsByReceiptType(Integer receiptType) {
        if (smsReceiptTypeMap == null) {
            smsReceiptTypeMap = new HashMap<>();
        }
        return smsReceiptTypeMap.containsKey(receiptType);
    }

    /**
     * 获取全部可短信功能列表
     */
    public List<BizMailRuleDTO> getAllSmsReceiptType() {
        if (smsReceiptTypeMap == null) {
            smsReceiptTypeMap = new HashMap<>();
        }
        return new ArrayList<>(smsReceiptTypeMap.values());
    }

}
