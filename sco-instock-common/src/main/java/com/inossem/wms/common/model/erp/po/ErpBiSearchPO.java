package com.inossem.wms.common.model.erp.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * BI查询PO
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ErpBiSearchPO {

    @ApiModelProperty(value = "公司代码")
    private String compCode;

    @ApiModelProperty(value = "更新日期起始")
    private String updDateLow;

    @ApiModelProperty(value = "更新日期截止")
    private String updDateHigh;

}
