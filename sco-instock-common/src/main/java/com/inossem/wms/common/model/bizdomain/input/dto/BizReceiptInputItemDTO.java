package com.inossem.wms.common.model.bizdomain.input.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecClassifyDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 入库单行项目传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "入库单行项目传输对象", description = "入库单行项目传输对象")
public class BizReceiptInputItemDTO implements Serializable {

    private static final long serialVersionUID = -7143714612235442703L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "库位" , example = "10")
    private String binCodeStr;

    @ApiModelProperty(value = "入库类型")
    private String inputType;

    BizReceiptAssembleRuleDTO receiptAssembleRuleDTO;

    private String specCode;

    private String specValue;

    private String preReceiptRid;

    /**
     * 仓位库存
     */
    @ApiModelProperty(value = "仓位库存")
    private List<StockBinDTO> stockBinList;

    @ApiModelProperty(value = "保养备注")
    private String mainRequirement;

    @ApiModelProperty(value = "技术参数")
    private String technicalSpecification;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private String depositTypeI18n;

    @ApiModelProperty(value = "扩展属性-净单价" , example = "20.1")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "扩展字段（工器具入库使用保存在批次信息里面） - 规格型号")
    private String formatCode;

    @ApiModelProperty(value = "入库数量")
    private Long inputNum;

    @ApiModelProperty(value = "填充属性 - 前置单据号", example = "4500000001")
    private String preReceiptCode;

    @ApiModelProperty(value = "填充属性 - 入库单号", example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "填充属性 - 单据类型", example = "220")
    private Integer receiptType;

    @ApiModelProperty(value = "填充属性 - 单据状态", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "填充属性 - 物料编码", example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称", example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料名称", example = "物料描述001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 计量单位编码", example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称", example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位", example = "2")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "是否为零价值工厂【1是，0否】", example = "0")
    private Integer isWorthless;

    @ApiModelProperty(value = "填充属性 - 接收库存地点", example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name", example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码", example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述", example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 采购订单编码", example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "填充属性 - 采购订单行号", example = "0010")
    private String referReceiptRid;

    @ApiModelProperty(value = "填充属性 - SAP采购订单类型  BZ：标准  FJWZ：废旧物资", example = "BZ")
    private String erpReceiptType;

    @ApiModelProperty(value = "填充属性 - SAP采购订单类型描述", example = "1")
    private String erpReceiptTypeName;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建人code", example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建人name", example = "管理员")
    private String erpCreateUserName;

    @ApiModelProperty(value = "填充属性 - SAP采购订单创建时间", example = "2021-05-11")
    private Date erpCreateTime;

    @ApiModelProperty(value = "填充属性 - 供应商名称", example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码", example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "填充属性 - 合同编号", example = "20201101001")
    private String contractCode;

    @ApiModelProperty(value = "填充属性 - 合同描述", example = "英诺森采购合同")
    private String contractName;

    @ApiModelProperty(value = "填充属性 - 特殊库存代码", example = "J-240412.24451")
    private String specStockCode;

    @ApiModelProperty(value = "填充属性 - 特殊库存描述", example = "备品项目WBS")
    private String specStockName;

    @ApiModelProperty(value = "MRP组code", example = "1")
    private String mrpGroupCode;

    @ApiModelProperty(value = "MRP组描述", example = "1")
    private String mrpGroupName;

    @ApiModelProperty(value = "计划员", example = "Admin")
    private String planUserName;

    @ApiModelProperty(value = "生产车间code", example = "1")
    private String productionTeamCode;

    @ApiModelProperty(value = "生产车间name", example = "1")
    private String productionTeamName;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 修改人名称", example = "管理员")
    private String preModifyUserName;

    @ApiModelProperty(value = "填充属性 - 仓位")
    @SonAttr(sonTbName = "biz_receipt_input_bin", sonTbFkAttrName = "itemId")
    private List<BizReceiptInputBinDTO> binList;

    @ApiModelProperty(value = "填充属性 - 批次信息")
    private BizBatchInfoDTO bizBatchInfoDTO;

    @ApiModelProperty(value = "扩展属性 - 批次图片")
    private List<BizBatchImgDTO> bizBatchImgDTOList;

    @ApiModelProperty(value = "扩展属性 - 物料批次特性")
    private BizSpecClassifyDTO materialSpecDTO;

    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称", example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "扩展字段 - 标签类型  1：RFID抗金属  2：RFID非抗金属 0：普通标签", example = "0")
    private Integer tagType;

    @ApiModelProperty(value = "扩展字段 - 单品/批次  0批次 1单品", example = "0")
    private Integer isSingle;

    @ApiModelProperty(value = "扩展属性 - 标签数据列表")
    private List<BizLabelDataDTO> bizLabelDataDTOList;

    @ApiModelProperty(value = "扩展属性 - 标签与单据关联数据")
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptItemId")
    private List<BizLabelReceiptRelDTO> labelReceiptRelDTOList;

    @ApiModelProperty(value = "填充属性 - 推荐仓位信息")
    private DicWhStorageBinDTO storageBin;

    @ApiModelProperty(value = "扩展属性 - 推荐仓位id", example = "152218489651201")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "*", targetAttrName = "storageBin")
    private Long recommendBinId;

    @ApiModelProperty(value = "借用方式【1：长期借用单；2：短期借用单】")
    private Integer borrowType;

    @ApiModelProperty(value = "工器具 - 借用人")
    private String borrowUserName;

    @ApiModelProperty(value = "工器具 - 借用出库单单号")
    private String outputReceiptCode;

    @ApiModelProperty(value = "预计借用天数（短期借用单必填）")
    private Integer estimateBorrowDay;

    @ApiModelProperty(value = "填充属性 - 工具编码", example = "100001")
    private String toolCode;

    @ApiModelProperty(value = "扩展属性 - 工具状态描述")
    private String toolStatusI18n;

    @ApiModelProperty(value = "出厂编码")
    private String outFtyCode;

    @ApiModelProperty(value = "存储类型id")
    private Long typeId;

    @ApiModelProperty(value = "仓位id")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    private Long binId;

    @ApiModelProperty(value = "填充属性 - 仓位code")
    private String binCode;

    @ApiModelProperty(value = "工器具已入库数量", example = "1")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "填充属性 - 前序单据物料凭证编号", example = "5211111111")
    private String preMatDocCode;

    @ApiModelProperty(value = "填充属性 - 前序单据物料凭证的行序号", example = "111")
    private String preMatDocRid;

    @ApiModelProperty(value = "填充属性 - 前序单据物料凭证年度", example = "2015")
    private String preMatDocYear;

    @ApiModelProperty(value = "填充属性 - 前序单据物料凭证编号", example = "5211111111")
    private String deliveryMatDocCode;

    @ApiModelProperty(value = "填充属性 - 前序单据物料凭证的行序号", example = "111")
    private String deliveryMatDocRid;

    @ApiModelProperty(value = "填充属性 - 前序单据物料凭证年度", example = "2015")
    private String deliveryMatDocYear;

    @ApiModelProperty(value = "填充属性 - 生产日期", example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "填充属性 - 批次code")
    private String batchCode;

    @ApiModelProperty(value = "扩展属性 - 到货登记行项目id")
    private Long arrivalRegisterItemId;

    @ApiModelProperty(value = "是否已接收或接收中 1接收 0未接收", example = "1")
    private Byte repairStatus;

    @ApiModelProperty(value = "维修状态描述", example = "2")
    private String receiveStatusI18n;

    @ApiModelProperty(value = "维修状态", example = "1")
    private Integer receiveStatus;

    @ApiModelProperty(value = "接收地点", example = "wh")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    private String receiveWhId;

    @ApiModelProperty(value = "填充属性 - 包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "扩展属性 - 包装方式描述（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private String packageTypeI18n;

    @ApiModelProperty(value = "项目类别")
    private String projectType;

    @ApiModelProperty(value = "科目类别")
    private String subjectType;

    @ApiModelProperty(value = "质检会签单单号 - 打印使用")
    private String signInspectReceiptCode;

    @ApiModelProperty(value = "总账科目文本")
    private String generalLedgerAccountText;

    @ApiModelProperty(value = "填充属性 - 分类:修旧利废0 专用工器具1 见证件2 试块3 其他4")
    private String categoryI18n;

    @ApiModelProperty(value = "填充属性 - 暂存期限（1-12月）")
    private String tempStorePeriodStr;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id", example = "***************", required = false)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_input_head", sourceAttrName = "receiptCode,receiptType,receiptStatus",
            targetAttrName = "receiptCode,receiptType,receiptStatus")
    @ApiModelProperty(value = "head表id", example = "1")
    private Long headId;

    @ApiModelProperty(value = "入库单行项目号", example = "1")
    private String rid;

    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_OUTPUT_HEAD, Const.PRE_RECEIPT_TYPE_INSPECT_HEAD,Const.PRE_RECEIPT_TYPE_PURCHASE_INPUT_HEAD},
            sourceAttrName = "receiptCode,repairFty,modifyUserId",
            targetAttrName = "preReceiptCode,repairFty,preModifyUserId")
    @ApiModelProperty(value = "前续单据head主键", example = "111")
    private Long preReceiptHeadId;

    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_OUTPUT_ITEM, Const.PRE_RECEIPT_TYPE_INSPECT_ITEM, Const.PRE_RECEIPT_TYPE_INCONFORMITY_ITEM,Const.RECEIPT_TYPE_INPUT_ITEM,Const.PRE_RECEIPT_TYPE_APPLY_ITEM},
            sourceAttrName = "inputQty,matDocCode,matDocRid,matDocYear,productDate,rid,tempStorePeriod", targetAttrName = "inputQty,preMatDocCode,preMatDocRid,preMatDocYear,productDate,preReceiptRid,tempStorePeriod")
    @ApiModelProperty(value = "前续单据item主键", example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型", example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据操作数量", example = "10")
    private BigDecimal preReceiptQty;

    @RlatAttr(rlatTableName = {Const.REFER_RECEIPT_TYPE_PURCHASE_HEAD, Const.REFER_RECEIPT_TYPE_PRODUCTION_HEAD, Const.REFER_RECEIPT_TYPE_APPLY_HEAD},
            sourceAttrName = "receiptCode,erpReceiptType,erpReceiptTypeName,erpCreateUserCode,erpCreateUserName,erpCreateTime,borrowType,deptId,deptOfficeId,estimateBorrowDay",
            targetAttrName = "referReceiptCode,erpReceiptType,erpReceiptTypeName,erpCreateUserCode,erpCreateUserName,erpCreateTime,borrowType,deptId,deptOfficeId,estimateBorrowDay")
    @ApiModelProperty(value = "参考单据head主键", example = "111")
    private Long referReceiptHeadId;

    @RlatAttr(rlatTableName = {Const.REFER_RECEIPT_TYPE_PURCHASE_ITEM, Const.REFER_RECEIPT_TYPE_PRODUCTION_ITEM, Const.REFER_RECEIPT_TYPE_APPLY_ITEM},
            sourceAttrName = "rid,supplierCode,supplierName,contractCode,contractName,mrpGroupCode,mrpGroupName,planUserName,productionTeamCode,productionTeamName,projectType,subjectType,specStockCode,specStockName,generalLedgerAccountText",
            targetAttrName = "referReceiptRid,supplierCode,supplierName,contractCode,contractName,mrpGroupCode,mrpGroupName,planUserName,productionTeamCode,productionTeamName,projectType,subjectType,specStockCode,specStockName,generalLedgerAccountText")
    @ApiModelProperty(value = "参考单据item主键", example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型", example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "凭证时间", example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期", example = "2021-05-11")
    private Date postingDate;

    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName,isWorthless,corpId", targetAttrName = "ftyCode,ftyName,isWorthless,corpId")
    @ApiModelProperty(value = "工厂编码", example = "8000")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn,packageType,depositType", targetAttrName = "matCode,matName,matNameEn,packageType,depositType")
    @ApiModelProperty(value = "物料id", example = "60000001")
    private Long matId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点id", example = "145725436526593")
    private Long locationId;

    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*,batchCode", targetAttrName = "bizBatchInfoDTO,batchCode")
    @ApiModelProperty(value = "批次id", example = "159707553660932")
    private Long batchId;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库id", example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "数量-订单单位", example = "10")
    private BigDecimal qty;

    @ApiModelProperty(value = "作业数量", example = "10")
    private BigDecimal taskQty;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    @ApiModelProperty(value = "订单单位", example = "1")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证编号", example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号", example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度", example = "2015")
    private String matDocYear;

    @ApiModelProperty(value = "移动类型id", example = "3010")
    private Long moveTypeId;

    @ApiModelProperty(value = "冲销标志0-false, 1-true", example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true", example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "冲销物料凭证号", example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间", example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期", example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号", example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度", example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销物料凭证号", example = "52222222")
    private String deliveryWriteOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间", example = "2021-05-11")
    private Date deliveryWriteOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期", example = "2021-05-11")
    private Date deliveryWriteOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号", example = "0010")
    private String deliveryWriteOffMatDocRid;

    @ApiModelProperty(value = "冲销年度", example = "2021")
    private String deliveryWriteOffMatDocYear;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userName", targetAttrName = "preModifyUserName")
    @ApiModelProperty(value = "质检会签单的提交人", example = "1", required = false)
    private Long preModifyUserId;

    @ApiModelProperty(value = "维修厂商", example = "145343907954689")
    private String repairFty;

    @ApiModelProperty(value = "工具状态")
    private Integer toolStatus;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;

    /* ********************** 顺序扩展字段开始 ****************************/

    @ApiModelProperty(value = "申请部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

    @ApiModelProperty(value = "填充属性 - 借用部门")
    private String deptCode;

    @ApiModelProperty(value = "填充属性 - 借用部门描述")
    private String deptName;

    @ApiModelProperty(value = "借用科室id")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeCode,deptOfficeName", targetAttrName = "deptOfficeCode,deptOfficeName")
    private Long deptOfficeId;

    @ApiModelProperty(value = "填充属性 - 借用科室")
    private String deptOfficeCode;

    @ApiModelProperty(value = "填充属性 - 借用科室描述")
    private String deptOfficeName;

    @ApiModelProperty(value = "工具类型")
    private Long toolTypeId;

    @ApiModelProperty(value = "工具类型名称")
    private String toolTypeName;

    @ApiModelProperty(value = "维保日期")
    private Date maintenanceDate;

    @ApiModelProperty(value = "工器具 - 维保日期")
    private Date maintenanceValidDate;

    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司编码", example = "1000", required = false)
    private Long corpId;

    @ApiModelProperty(value = "公司编码", example = "1000", required = false)
    private String corpCode;

    @ApiModelProperty(value = "公司描述", example = "示例公司", required = false)
    private String corpName;

    private Integer isLabelPrint;

    private Integer printNum;

    /* ********************** 顺序扩展字段结束 ****************************/

    @ApiModelProperty(value = "工器具 - 使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    private String toolUseReason;

    @ApiModelProperty(value = "工器具 - 管理状态备注")
    private String toolManageStatusRemark;

    @ApiModelProperty(value = "工器具 - 送检单位")
    private String toolInspectUnit;

    @ApiModelProperty(value = "工器具 - 送检单位时间")
    private Date toolInspectDate;

    @ApiModelProperty(value = "SAP 103凭证")
    private String matDocCode103;
    @ApiModelProperty(value = "SAP 103凭证行号")
    private String matDocRid103;
    @ApiModelProperty(value = "SAP 103凭证年份")
    private String matDocYear103;

    @ApiModelProperty(value = "到货通知抬头id")
    private Long deliveryNoticeHeadId;

    @ApiModelProperty(value = "到货通知行项目id")
    private Long deliveryNoticeItemId;

    @ApiModelProperty(value = "到货登记抬头id")
    private Long arrivalRegisterHeadId;

    @ApiModelProperty(value = "扩展属性 - 入库单成套运单表")
    @SonAttr(sonTbName = "biz_receipt_input_waybill", sonTbFkAttrName = "itemId")
    private List<BizReceiptInputWaybillDTO> inputWaybillList;

    @ApiModelProperty(value = "领用单号")
    private String tempStoreOutCode;

    @ApiModelProperty(value = "暂存期限（1-12月）")
    private Integer tempStorePeriod;

    @ApiModelProperty(value = "暂存期间维保要求")
    private String maintenanceRequirements;

    @ApiModelProperty(value = "暂存分类:修旧利废0 专用工器具1 见证件2 试块3 其他4")
    private Integer category;

    @ApiModelProperty(value = "暂存到期日期")
    private Date tempStoreExpireDate;

    @ApiModelProperty(value = "暂存延期到期日期")
    private Date tempStoreDelayExpireDate;

    @ApiModelProperty(value = "暂存延期原因")
    private String tempStoreDelayReason;

    @ApiModelProperty(value = "填充属性 - 单据附件")
    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptItemId")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "来自物资返运冲销")
    private Integer writeOffFromMatReturn;

    @ApiModelProperty(value = "暂存出库单号")
    private String tempStoreOutputReceiptCode;

    @ApiModelProperty(value = "暂存存储级别")
    private String tempStoreLevel;


    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @RlatAttr(rlatTableName = "biz_receipt_delivery_notice_item", sourceAttrName = "caseCode,contractCode", targetAttrName = "caseCode,subContractCode")
    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "合同编号")
    private String subContractCode;

    @ApiModelProperty(value = "提交人签名")
    private String submitUserSignImg;

    @ApiModelProperty(value = "已预制数量")
    private BigDecimal precastQty;

    @ApiModelProperty(value = "已预制金额")
    private BigDecimal precastAmount;

}
