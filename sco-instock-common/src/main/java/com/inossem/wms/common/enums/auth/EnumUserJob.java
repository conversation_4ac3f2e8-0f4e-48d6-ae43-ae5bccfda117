package com.inossem.wms.common.enums.auth;

import com.inossem.wms.common.model.auth.user.dto.UserJobDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户岗位枚举
 *
 * <AUTHOR>
 * @date 2022/05/12 15:39
 **/
@AllArgsConstructor
public enum EnumUserJob {

    LEVEL_1_APPROVAL(1, "专工"),
    LEVEL_2_APPROVAL(2, "部门领导"),
    LEVEL_3_APPROVAL(3, "分管领导"),
    LEVEL_4_APPROVAL(4, "主管"),
    LEVEL_5_APPROVAL(5, "采购计划管理专员"),
    LEVEL_6_APPROVAL(6, "成本主管"),
    LEVEL_7_APPROVAL(7, "费用主管"),
    LEVEL_8_APPROVAL(8, "财务经理"),
    LEVEL_9_APPROVAL(9, "财务出纳"),
    LEVEL_10_APPROVAL(10, "财务部门负责人");

    public static List<UserJobDTO> list;

    @Getter
    private final Integer value;

    @Getter
    private final String name;

    public static List<UserJobDTO> toList() {
        if (list == null) {
            List<UserJobDTO> listInner = new ArrayList<>();
            EnumUserJob[] ary = EnumUserJob.values();
            for (EnumUserJob e : ary) {
                UserJobDTO tempMap = new UserJobDTO();
                tempMap.setLevelId(e.getValue());
                tempMap.setJobName(e.getName());
                listInner.add(tempMap);
            }
            list = listInner;
        }
        return list;
    }


}
