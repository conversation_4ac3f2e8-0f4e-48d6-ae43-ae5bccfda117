package com.inossem.wms.system.workflow.listener.apply;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/18
 */
@Service
public class ListenerDirectPurchase2 extends ApprovalListener implements TaskListener, ExecutionListener {
    private static final long serialVersionUID = -6230308559255338232L;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution execution) {
        approvalCallback(execution, TagConst.APPROVAL_DIRECT_PURCHASE);
    }

    @Override
    public void notify(DelegateTask delegateTask) {

        // 如果是沟通任务，则直接返回
        if(this.isCommunicateTask(delegateTask)){
            return;
        }

        // 如果是转办任务，则直接返回
        if(this.isTransferTask(delegateTask)){
            return;
        }

        // 如果跳转审批节点，则直接返回
        if(this.jumpApprovalNode(delegateTask)){
            return;
        }

        String taskDefKey = delegateTask.getTaskDefinitionKey();
        String userCode1 = (String) delegateTask.getVariable("userCode1");
        String userCode2 = (String) delegateTask.getVariable("userCode2");
        String demandDeptCode = (String) delegateTask.getVariable("demandDeptCode");

        // 根据节点配置审批人
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点 - 经营部部门领导
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.BMD.getCode(), null, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点 - 业务需求部门分管领导
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(demandDeptCode, null, EnumApprovalLevel.LEVEL_3);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 三级审批节点 - 财务部门负责人
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode((String) null, null, EnumApprovalLevel.LEVEL_10);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_4_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 四级审批节点 - 经营部分管领导
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.BMD.getCode(), null, EnumApprovalLevel.LEVEL_3);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_5_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 五级审批节点 - 华信（公司领导）李吉根
            addApproveUser(delegateTask, Collections.singletonList(userCode1));
        } else if (EnumApprovalNode.LEVEL_6_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 六级审批节点 - 能殷（公司领导）林申晟
            addApproveUser(delegateTask, Collections.singletonList(userCode2));
        }
    }
}
