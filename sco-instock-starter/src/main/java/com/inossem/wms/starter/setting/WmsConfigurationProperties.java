package com.inossem.wms.starter.setting;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * InStock系统自定义配置属性
 * 从application.yml配置文件中读取相关
 *
 * <AUTHOR> <<EMAIL>>
 */
@Configuration
public class WmsConfigurationProperties {

    @Bean
    @ConfigurationProperties(prefix = "wms.biz-domain.task")
    public TaskConfig getTaskConfig() {
        return new TaskConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.biz-domain.common")
    public CommonConfig getCommonConfig() {
        return new CommonConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.system")
    public SystemConfig getSystemConfig() {
        return new SystemConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.oa")
    public OaConfig getOaConfig() {
        return new OaConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.oil")
    public OilConfig getOilConfig() {
        return new OilConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.erp")
    public ErpConfig getErpConfig() {
        return new ErpConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.bi")
    public BiConfig getBiConfig() {
        return new BiConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.sms")
    public SmsConfig getSmsConfig() {
        return new SmsConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.ums")
    public UmsConfig getUmsConfig() {
        return new UmsConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.dts")
    public DtsConfig getDtsConfig() {
        return new DtsConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "wms.leader")
    public LeaderConfig getLeaderConfig() {
        return new LeaderConfig();
    }   

    @Data
    public final class TaskConfig {
        private Boolean loadStrategyEnabled;
        private Boolean unloadStrategyEnabled;
    }

    @Data
    public final class CommonConfig {
        private Boolean attachmentEnabled;
        private Boolean operationLogEnabled;
        private Boolean labelPrinterEnabled;
    }

    @Data
    public final class SystemConfig {
        private String filePath;
        private String imgPath;
        private String appUpgradeFilePath;
        private Boolean cacheRefresh;
        private String baseUrl;
        private String ledgerFilePath;
        private String dtsFilePath;
        /**
         * wms帐号
         */
        private String wmsUsername;
        /**
         * wms密码
         */
        private String wmsPassword;
    }

    @Data
    public final class ErpConfig {
        private Boolean dataSyncEnabled;
        private String restApiUrl;
        private String username;
        private String password;
        private String secret;
    }

    @Data
    public final class BiConfig {
        private String restApiUrl;
        private String username;
        private String password;
        private String secret;
        private String appName;
    }

    @Data
    public final class OaConfig {
        private Boolean dataSyncEnabled;
        private String restApiUrl;
        private String username;
        private String password;
        private String appName;
        private String modelName;
        private String modelId;
    }

    @Data
    public final class OilConfig {
        private Boolean enabled;
        private String url;
        private String appId;
        private String appSecret;
        private String username;
        private String password;
    }

    @Data
    public final class SmsConfig {
        private String appKey;
        private String appSecret;
        private String serverUrl;
    }

    @Data
    public final class UmsConfig {
        private String url;
        private String urlOther;
    }
    @Data
    public final class DtsConfig {
        private Boolean sync;
        private String ftpPath;
        private int ftpPort;
        private String username;
        private String password;
        private String directory;
        private String dtsFilePath;
        private String dtsRemoteFilePath;
    }

    @Data
    public final class LeaderConfig {
        private String hxLeader;
        private String nyLeader;
    }   

}
