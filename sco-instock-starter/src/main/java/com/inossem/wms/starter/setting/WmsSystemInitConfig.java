package com.inossem.wms.starter.setting;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.inossem.wms.bizbasis.common.dao.AuthCommonMapper;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.entity.BizApprovalRule;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.system.i18n.service.biz.I18nTextService;
import com.inossem.wms.system.i18n.service.biz.SysI18nDynamicTextService;
import com.inossem.wms.system.i18n.service.biz.SysI18nDynamicTypeService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WmsSystemInitConfig {

    @Autowired
    protected EditCacheService editCacheService;
    @Autowired
    protected WmsConfigurationProperties wmsConfig;
    @Autowired
    protected I18nTextService i18nTextService;
    @Autowired
    protected SysI18nDynamicTextService sysI18nDynamicTextService;
    @Autowired
    protected SysI18nDynamicTypeService sysI18nDynamicTypeService;
    @Autowired
    protected AuthCommonMapper authCommonMapper;

    public void init() {
        try {
            log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 系统配置初始化..");

            log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 加载系统配置..");
            // 系统级配置
            UtilConst.getInstance().setFilePath(wmsConfig.getSystemConfig().getFilePath());
            UtilConst.getInstance().setImgPath(wmsConfig.getSystemConfig().getImgPath());
            UtilConst.getInstance().setAppUpgradeFilePath(wmsConfig.getSystemConfig().getAppUpgradeFilePath());
            UtilConst.getInstance().setBaseUrl(wmsConfig.getSystemConfig().getBaseUrl());
            UtilConst.getInstance().setLedgerFilePath(wmsConfig.getSystemConfig().getLedgerFilePath());
            UtilConst.getInstance().setWmsUsername(wmsConfig.getSystemConfig().getWmsUsername());
            UtilConst.getInstance().setWmsPassword(wmsConfig.getSystemConfig().getWmsPassword());
            UtilConst.getInstance().setErpUrl(wmsConfig.getErpConfig().getRestApiUrl());
            UtilConst.getInstance().setErpSyncMode(wmsConfig.getErpConfig().getDataSyncEnabled());
            UtilConst.getInstance().setErpUserName(wmsConfig.getErpConfig().getUsername());
            UtilConst.getInstance().setErpPassword(wmsConfig.getErpConfig().getPassword());
            UtilConst.getInstance().setSecret(wmsConfig.getErpConfig().getSecret());
            UtilConst.getInstance().setBiUrl(wmsConfig.getErpConfig().getRestApiUrl());
            UtilConst.getInstance().setBiUserName(wmsConfig.getBiConfig().getUsername());
            UtilConst.getInstance().setBiPassword(wmsConfig.getBiConfig().getPassword());
            UtilConst.getInstance().setBiAppName(wmsConfig.getBiConfig().getAppName());
            UtilConst.getInstance().setBiSecret(wmsConfig.getBiConfig().getSecret());
            UtilConst.getInstance().setOaUrl(wmsConfig.getOaConfig().getRestApiUrl());
            UtilConst.getInstance().setOaSyncMode(wmsConfig.getOaConfig().getDataSyncEnabled());
            UtilConst.getInstance().setOaUserName(wmsConfig.getOaConfig().getUsername());
            UtilConst.getInstance().setOaPassword(wmsConfig.getOaConfig().getPassword());
            UtilConst.getInstance().setOaAppName(wmsConfig.getOaConfig().getAppName());
            UtilConst.getInstance().setOaModelName(wmsConfig.getOaConfig().getModelName());
            UtilConst.getInstance().setOaModelId(wmsConfig.getOaConfig().getModelId());
            UtilConst.getInstance().setOilEnabled(wmsConfig.getOilConfig().getEnabled());
            UtilConst.getInstance().setOilUrl(wmsConfig.getOilConfig().getUrl());
            UtilConst.getInstance().setOilAppId(wmsConfig.getOilConfig().getAppId());
            UtilConst.getInstance().setOilAppSecret(wmsConfig.getOilConfig().getAppSecret());
            UtilConst.getInstance().setOilUserName(wmsConfig.getOilConfig().getUsername());
            UtilConst.getInstance().setOilPassword(wmsConfig.getOilConfig().getPassword());
            UtilConst.getInstance().setUnloadStrategyEnabled(wmsConfig.getTaskConfig().getUnloadStrategyEnabled());
            UtilConst.getInstance().setLoadStrategyEnabled(wmsConfig.getTaskConfig().getLoadStrategyEnabled());
            UtilConst.getInstance().setAttachmentRequired(wmsConfig.getCommonConfig().getAttachmentEnabled());
            UtilConst.getInstance().setOperationLogRequired(wmsConfig.getCommonConfig().getOperationLogEnabled());
            UtilConst.getInstance().setPrintEnable(wmsConfig.getCommonConfig().getLabelPrinterEnabled());
            UtilConst.getInstance().setUmsUrl(wmsConfig.getUmsConfig().getUrl());
            UtilConst.getInstance().setUmsUrlOther(wmsConfig.getUmsConfig().getUrlOther());
            UtilConst.getInstance().setDtsSync(wmsConfig.getDtsConfig().getSync());
            UtilConst.getInstance().setDtsFtpPath(wmsConfig.getDtsConfig().getFtpPath());
            UtilConst.getInstance().setDtsFtpPort(wmsConfig.getDtsConfig().getFtpPort());
            UtilConst.getInstance().setDtsUserName(wmsConfig.getDtsConfig().getUsername());
            UtilConst.getInstance().setDtsPassword(wmsConfig.getDtsConfig().getPassword());
            UtilConst.getInstance().setDtsDirectory(wmsConfig.getDtsConfig().getDirectory());
            UtilConst.getInstance().setDtsPaperFilePath(wmsConfig.getSystemConfig().getDtsFilePath());
            UtilConst.getInstance().setDtsPaperRemoteFilePath(wmsConfig.getSystemConfig().getDtsFilePath());

            UtilConst.getInstance().setHxLeader(wmsConfig.getLeaderConfig().getHxLeader());
            UtilConst.getInstance().setNyLeader(wmsConfig.getLeaderConfig().getNyLeader());

            log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 加载审批配置..");

            // 加载需要审批的单据类型配置
            List<BizApprovalRule> approvalRuleList = authCommonMapper.selectAllApprovalRule();
            if(UtilCollection.isNotEmpty(approvalRuleList)){
                for(BizApprovalRule rule:approvalRuleList){
                    UtilConst.getInstance().setWfReceiptType(rule.getReceiptType(),rule.getProcId());
                }
            }

        } catch (NullPointerException e) {
            log.error(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 系统默认配置加载失败");
            e.printStackTrace();
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SYSTEM_CONFIG_ERROR);
        }
        try {
            if (wmsConfig.getSystemConfig().getCacheRefresh()) {
                initAllCache();
            } else {
                log.info("wms.system.cache-refresh=false 跳过缓存加载..");
            }
        } catch (Exception e) {
            log.error(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 系统缓存加载失败");
            e.printStackTrace();
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SYSTEM_CONFIG_ERROR);
        }
        log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 系统默认配置加载完成");
    }

    public void initAllCache(){

        try {
            // 缓存系统主数据
            log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 加载缓存信息..");
            editCacheService.initSysUserCache();
            editCacheService.initCorpCache();

            editCacheService.initLocationCache();
            editCacheService.initFtyCache();
            editCacheService.initWhCache();
            editCacheService.initStorageTypeCache();
            editCacheService.initDicWhStorageSection();
            editCacheService.initBinCache();

            editCacheService.initUnitCache();
            editCacheService.initRelUnitCache();

            editCacheService.initDicMaterialTypeCache();
            editCacheService.initDicMaterialGroupCache();

            //工器具类型缓存
//            editCacheService.initToolTypeCache();

            // 移动类型初始化
            editCacheService.initMoveTypeCache();

//            editCacheService.initPurchasePackageCache();
//            editCacheService.initMarginCategoryCache();
//            editCacheService.initMaterialCgnCache();
            // 国际化缓存
            i18nTextService.initLanguageCache();
            // sysI18nDynamicTextService.initCache();
            // sysI18nDynamicTypeService.initCache();

            editCacheService.initDicMaterialCache();
            // editCacheService.initDicMaterialFactoryCache();

            log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 缓存信息加载完成");
        } catch (Exception e) {
            log.error(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 系统缓存加载失败");
            e.printStackTrace();
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SYSTEM_CONFIG_ERROR);
        }

    }
}
